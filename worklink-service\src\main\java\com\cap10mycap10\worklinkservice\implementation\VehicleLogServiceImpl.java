package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.VehicleLogRepository;
import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.mapper.vehiclelog.VehicleLogToVehicleLogDto;
import com.cap10mycap10.worklinkservice.model.VehicleLog;
import com.cap10mycap10.worklinkservice.service.VehicleLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class VehicleLogServiceImpl implements VehicleLogService {
    @Autowired
    private VehicleLogRepository vehicleLogRepository;
    @Autowired
    private VehicleLogToVehicleLogDto toVehicleLogDto;
    @Override
    public VehicleLog getOne(Long id) {
        return vehicleLogRepository.findById(id).orElseThrow(()-> new BusinessValidationException("Vehicle log with that id was not found."));
    }

    @Override
    public VehicleLog save(VehicleLog id) {
        return vehicleLogRepository.save(id);
    }

    @Override
    public VehicleLog getByTransportId(Long id) throws BusinessValidationException {
        return vehicleLogRepository.findByTransportId(id).orElseThrow(()-> new RecordNotFoundException("Vehicle log with that transport id was not found."));
    }
    @Override
    public Optional<VehicleLog> getOneByTransportId(Long id)  {
        return vehicleLogRepository.findByTransportId(id);
    }

    @Override
    public Page<VehicleLogDto> findForVehicle(Long id, LogStatus status, PageRequest of) {
        return vehicleLogRepository.findByVehicleIdAndStatus(id, status, of).map(toVehicleLogDto);
    }
}
