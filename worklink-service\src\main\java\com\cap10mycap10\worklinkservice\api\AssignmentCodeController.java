package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeCreateDto;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeResultDto;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeUpdateDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.permissions.assignmentcode.CreateAssignmentCode;
import com.cap10mycap10.worklinkservice.permissions.assignmentcode.DeleteAssignmentCode;
import com.cap10mycap10.worklinkservice.permissions.assignmentcode.UpdateAssignmentCode;
import com.cap10mycap10.worklinkservice.permissions.assignmentcode.ViewAssignmentCode;
import com.cap10mycap10.worklinkservice.service.AssignmentCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class AssignmentCodeController {

    private final AssignmentCodeService assignmentCodeService;

    public AssignmentCodeController(final AssignmentCodeService assignmentCodeService) {
        this.assignmentCodeService = assignmentCodeService;
    }

    /*@CreateAssignmentCode*/
    @PostMapping(value = "assignment-code", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity create(@RequestBody AssignmentCodeCreateDto assignmentCodeCreateDto) {
        log.info("Request to add assignment code: {}", assignmentCodeCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        try{
            AssignmentCodeResultDto result = assignmentCodeService.save(assignmentCodeCreateDto);
            return ResponseEntity.created(uri)
                    .body(result);
        }catch (BusinessValidationException e){
            return ResponseEntity.status(409).body(e);
        }
    }

    /*@ViewAssignmentCode*/
    @GetMapping(value = "assignment-code/{id}")
    public ResponseEntity<AssignmentCodeResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get assignment code with id: {}", id);
        return ResponseEntity.ok(assignmentCodeService.findById(id));
    }

   /* @ViewAssignmentCode*/
    @GetMapping(value = "assignment-code-agency/{id}")
    public ResponseEntity<List<AssignmentCodeResultDto>> findByAgencyId(@PathVariable("id") Long id) {
        log.info("Request to get assignment code with id: {}", id);
        return ResponseEntity.ok(assignmentCodeService.findByAgencyId(id));
    }

    @GetMapping(value = "assignment-code-client/{id}")
    public ResponseEntity<List<AssignmentCodeResultDto>> findByClientId(@PathVariable("id") Long id) {
        log.info("Request to get assignment code with id: {}", id);
        return ResponseEntity.ok(assignmentCodeService.findByClientId(id));
    }

    /*@ViewAssignmentCode*/
    @GetMapping(value = "assignment-codes")
    public ResponseEntity<List<AssignmentCodeResultDto>> findById() {
        log.info("Request for all assignment codes");
        return ResponseEntity.ok(assignmentCodeService.findAll());
    }

    @GetMapping(value = "assignment-codes/{code}")
    public ResponseEntity<AssignmentCodeResultDto> findByCode(@PathVariable("code") String code) {
        log.info("Request to find assignment code with code: {}", code);
        return ResponseEntity.ok(assignmentCodeService.findByCode(code));
    }

    /*@ViewAssignmentCode*/
    @GetMapping(value = "assignment-codes/{page}/{size}")
    public ResponseEntity<Page<AssignmentCodeResultDto>> findById(@PathVariable("page") int page,
                                                                  @PathVariable("size") int size) {
        log.info("Request for assignment code: {}, {}", page
        ,size);
        return ResponseEntity.ok(assignmentCodeService.findAllPaged(PageRequest.of(page, size)));
    }



    /*@UpdateAssignmentCode*/
    @PutMapping(value = "assignment-code", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AssignmentCodeResultDto> update(@RequestBody AssignmentCodeUpdateDto assignmentCodeUpdateDto) {
        log.info("Request to edit assignment code: {}", assignmentCodeUpdateDto);
        return ResponseEntity.ok(assignmentCodeService.save(assignmentCodeUpdateDto));
    }

   /* @DeleteAssignmentCode*/
    @DeleteMapping(value = "assignment-code/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete assignmentCodeName with id: {}", id);
        assignmentCodeService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
