package com.cap10mycap10.worklinkservice.mapper.client;

import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.Address;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.service.ServicesService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class ClientDtoToClient implements Converter<ClientDto, Client> {

    private final AuthenticationFacade authenticationFacade;
    private final ServicesService service;

    public ClientDtoToClient(AuthenticationFacade authenticationFacade, ServicesService service) {
        this.authenticationFacade = authenticationFacade;
        this.service = service;
    }

    @Override
    public Client convert(ClientDto clientDto) {
        Client client = new Client();
        client.setName(clientDto.getName());
        Address address = new Address();
        address.setFirstLine(clientDto.getAddress().getFirstLine());
        address.setTown(clientDto.getAddress().getTown());
        address.setPostcode(clientDto.getAddress().getPostcode());
        client.setAddress(address);
        client.setEmail(clientDto.getEmail());
        client.setBillingEmail(clientDto.getBillingEmail());
        client.setTelephone(clientDto.getTelephone());
        client.setLogo(clientDto.getLogo());
        client.setCreatedBy(authenticationFacade.getAuthentication().getName());
        client.setStatus(Status.ACTIVE);
        client.setService(service.getOne(clientDto.getServiceId()));
        return client;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
