package com.cap10mycap10.worklinkservice.mapper.workercompliance;

import com.cap10mycap10.worklinkservice.dao.WorkerComplianceRepository;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.model.WorkerCompliance;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;

@Component
@Slf4j
public class WorkerComplianceToWorkerComplianceResultDto implements Converter<WorkerCompliance, WorkerComplianceResultDto> {

    private final WorkerComplianceRepository workerComplianceRepository;

    public WorkerComplianceToWorkerComplianceResultDto(WorkerComplianceRepository workerComplianceRepository) {
        this.workerComplianceRepository = workerComplianceRepository;
    }

    @Override
    public WorkerComplianceResultDto convert(WorkerCompliance workerCompliance) {
        WorkerComplianceResultDto workerComplianceResultDto = new WorkerComplianceResultDto();
        workerComplianceResultDto.setId(workerCompliance.getId());

        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        if (workerCompliance.getComplianceDate() != null) {
            String string = workerCompliance.getComplianceDate().format(pattern);
            workerComplianceResultDto.setComplianceDate(string);
        }

        if (workerCompliance.getComplianceExpiry() != null) {
            String string = workerCompliance.getComplianceExpiry().format(pattern);
            workerComplianceResultDto.setComplianceExpiry(string);
        }

        if (workerCompliance.getStatus() != null) {
            workerComplianceResultDto.setStatus(workerCompliance.getStatus().toString());
        }

        if (workerCompliance.getDocument() != null) {
            workerComplianceResultDto.setDocument(workerCompliance.getDocument());
        }


        if (workerCompliance.getComment() != null) {
            workerComplianceResultDto.setComment(workerCompliance.getComment());
        }

        workerComplianceResultDto.setName(workerCompliance.getName());
        workerComplianceResultDto.setCode(workerCompliance.getCode());
        workerComplianceResultDto.setDescription(workerCompliance.getDescription());
        

        return workerComplianceResultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
