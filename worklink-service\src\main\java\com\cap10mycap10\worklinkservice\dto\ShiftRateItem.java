package com.cap10mycap10.worklinkservice.dto;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

@Data
public class ShiftRateItem {

    private String  dayOfTheWeek;

    private BigDecimal rate;

    private LocalTime startTime;

    private LocalTime endTime;

    private LocalDateTime rateStartTime;

    private LocalDateTime rateEndTime;

    public ShiftRateItem() {
    }

    public ShiftRateItem(String dayOfTheWeek,  LocalTime startTime, LocalTime endTime,BigDecimal rate) {
        this.dayOfTheWeek = dayOfTheWeek;
        this.rate = rate;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ShiftRateItem that = (ShiftRateItem) o;
        return Objects.equals(dayOfTheWeek, that.dayOfTheWeek) && Objects.equals(rate, that.rate) && Objects.equals(startTime, that.startTime) && Objects.equals(endTime, that.endTime) && Objects.equals(rateStartTime, that.rateStartTime) && Objects.equals(rateEndTime, that.rateEndTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dayOfTheWeek, rate, startTime, endTime, rateStartTime, rateEndTime);
    }
}
