package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.ShiftDirectorateRepository;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateCreateDto;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateResultDto;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateUpdateDto;
import com.cap10mycap10.worklinkservice.mapper.shiftdirectorate.ShiftDirectorateDtoToShiftDirectorate;
import com.cap10mycap10.worklinkservice.mapper.shiftdirectorate.ShiftDirectorateToShiftDirectorateResultDto;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.model.ShiftDirectorate;
import com.cap10mycap10.worklinkservice.model.Location;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ClientService;
import com.cap10mycap10.worklinkservice.service.ShiftDirectorateService;
import com.cap10mycap10.worklinkservice.service.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ShiftDirectorateServiceImpl implements ShiftDirectorateService {

    @Autowired
    private  ShiftDirectorateRepository shiftDirectorateRepository;
    @Autowired
    private ShiftDirectorateToShiftDirectorateResultDto toShiftDirectorateResultDto;
    @Autowired
    private ShiftDirectorateDtoToShiftDirectorate toShiftDirectorate;
    @Autowired
    private LocationService locationService;
    @Autowired
    private AgencyService agencyService;
    @Autowired
    private ClientService clientService;


    @Override
    public ShiftDirectorateResultDto save(ShiftDirectorateCreateDto shiftDirectorateCreateDto) {
        return toShiftDirectorateResultDto.convert(shiftDirectorateRepository.save(
                toShiftDirectorate.convert(shiftDirectorateCreateDto)
        ));
    }

    @Override
    public ShiftDirectorateResultDto findById(Long id) {
        return toShiftDirectorateResultDto.convert(shiftDirectorateRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Shift directorate not found {}")));
    }

    @Override
    public List<ShiftDirectorateResultDto> findAll() {
        return shiftDirectorateRepository.findAll()
                .stream()
                .map(toShiftDirectorateResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<ShiftDirectorateResultDto> findAllWithFilter(String location) {
        List<ShiftDirectorate> shiftDirectorates = shiftDirectorateRepository.findAll();

        if(location != null ){
            List<ShiftDirectorateResultDto> shifts = shiftDirectorates.stream()
                    .filter(shiftDirectorate -> shiftDirectorate.getName().toLowerCase().contains(location.toLowerCase()))
                    .map(toShiftDirectorateResultDto::convert)
                    .collect(Collectors.toList());
            return shifts;
        }

        return shiftDirectorates.stream()
                .map(toShiftDirectorateResultDto::convert)
                .collect(Collectors.toList());


    }

    @Override
    public ShiftDirectorate getByDeputyId(Long id) {
        return shiftDirectorateRepository.findByDeputyId(id);
    }

    @Override
    public Page<ShiftDirectorateResultDto> findAllPaged(String searchCriteria, Long locationId,PageRequest of) {
        List<ShiftDirectorate> shiftDirectorateList = shiftDirectorateRepository.findAll();

        if(locationId != null){
            shiftDirectorateList = shiftDirectorateList.stream()
                    .filter(shiftDirectorate -> shiftDirectorate.getLocation().getId() == locationId)
                    .collect(Collectors.toList());
        }
        if(searchCriteria != null){
            shiftDirectorateList = shiftDirectorateList.stream()
                    .filter(shiftDirectorate -> shiftDirectorate.getName().toLowerCase().contains(searchCriteria.toLowerCase()))
                    .collect(Collectors.toList());
        }

        Page<ShiftDirectorate> shiftDirectoratePage = PaginationUtil.paginateList(of, shiftDirectorateList);
        return shiftDirectoratePage.map(toShiftDirectorateResultDto::convert);
    }

    @Override
    public void deleteById(Long id) {
        ShiftDirectorate shiftDirectorate = getOne(id);
        try {
            shiftDirectorateRepository.deleteById(id);
            shiftDirectorateRepository.flush();
        } catch (Exception ex) {
            throw new BusinessValidationException("Shift Directorate cannot be deleted");
        }
    }

    @Override
    public ShiftDirectorateResultDto save(ShiftDirectorateUpdateDto shiftDirectorateUpdateDto) {
        ShiftDirectorate shiftDirectorate = getOne(shiftDirectorateUpdateDto.getId());
        shiftDirectorate.setName(shiftDirectorateUpdateDto.getName());
        shiftDirectorate.setPhoneNumber(shiftDirectorateUpdateDto.getPhoneNumber());
        shiftDirectorate.setPostCode(shiftDirectorateUpdateDto.getPostCode());
        Location location = locationService.getOne(shiftDirectorateUpdateDto.getLocationId());
        shiftDirectorate.setLocation(location);
//        shiftDirectorate.setClient(clientService.getOne(shiftDirectorateUpdateDto.getPayerId()));

        return toShiftDirectorateResultDto.convert(shiftDirectorateRepository.save(shiftDirectorate));
    }

    @Override
    public ShiftDirectorate getOne(Long id) {
        return shiftDirectorateRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Shift directorate not found"));
    }

    @Override
    public List<ShiftDirectorateResultDto> findByLocationId(Long id) {
        return shiftDirectorateRepository.findAllByLocation_Id(id)
                .stream()
                .map(toShiftDirectorateResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Page<ShiftDirectorateResultDto> findAllPagedByClient(Long clientId, String searchCriteria, PageRequest of) {
        Client client = clientService.getOne(clientId);
        List<ShiftDirectorate> shiftDirectorateList = shiftDirectorateRepository.findAllByClientOrderByName(client);

        if(searchCriteria != null ){
            shiftDirectorateList = shiftDirectorateList.stream()
                    .filter(shiftDirectorate -> shiftDirectorate.getName().toLowerCase().contains(searchCriteria.toLowerCase()))
                    .collect(Collectors.toList());
        }
        Page<ShiftDirectorate> page = PaginationUtil.paginateList(of, shiftDirectorateList);
        return page.map(toShiftDirectorateResultDto::convert);
    }

    @Override
    public Page<ShiftDirectorateResultDto> findAllPagedByAgency(Long agencyId, String searchCriteria, PageRequest of) {
        agencyService.getOne(agencyId);
        List<ShiftDirectorate> shiftDirectorateList = shiftDirectorateRepository.findAllByAgentId(agencyId);

        if(searchCriteria != null ){
            shiftDirectorateList = shiftDirectorateList.stream()
                    .filter(shiftDirectorate -> shiftDirectorate.getName().toLowerCase().contains(searchCriteria.toLowerCase()))
                    .collect(Collectors.toList());
        }
        Page<ShiftDirectorate> page = PaginationUtil.paginateList(of, shiftDirectorateList);
        return page.map(toShiftDirectorateResultDto::convert);

    }


}
