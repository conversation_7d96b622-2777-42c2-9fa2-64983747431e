package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.workercompliance.IWorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceCreateDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceUpdateDto;
import com.cap10mycap10.worklinkservice.model.WorkerCompliance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface WorkerComplianceService {
    WorkerComplianceResultDto addWorkerCompliance(WorkerComplianceCreateDto workerComplianceCreateDto);

    void updateWorkerCompliance(WorkerComplianceUpdateDto workerComplianceUpdateDto);

    void deleteWorkerCompliance(Long id);

    WorkerCompliance findById(Long id);

    Page<IWorkerComplianceResultDto> findWorkerCompliances(Long workerId, PageRequest of);
    List<WorkerComplianceResultDto> findAgencyWorkerCompliances(Long workerId, Long agencyId, PageRequest of);

    Page<WorkerCompliance> findAllPaged(PageRequest of);

    WorkerCompliance save(WorkerComplianceUpdateDto workerComplianceUpdateDto);
    void addComplianceDoc(Long workerId, Long compliance, MultipartFile file);

    WorkerCompliance getOne(Long id);
}
