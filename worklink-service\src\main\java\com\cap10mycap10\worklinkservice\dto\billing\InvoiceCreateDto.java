package com.cap10mycap10.worklinkservice.dto.billing;


import com.cap10mycap10.worklinkservice.enums.InvoiceType;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;


@Data
public class InvoiceCreateDto {
    private List<Long> shiftIds;
    private List<Long> workerTrainingSessionIds;
    private List<Long> trainingSessionIds;
    private LocalDate dueDate;
    private String notes;
    private Boolean email;
    private InvoiceType invoiceType;
}
