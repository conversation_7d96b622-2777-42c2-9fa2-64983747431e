//package com.cap10mycap10.worklinkservice.mapper.shifttype;
//
//import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
//import com.cap10mycap10.worklinkservice.dto.shifttype.ShiftTypeCreateDto;
//import com.cap10mycap10.worklinkservice.enums.Status;
//import com.cap10mycap10.worklinkservice.model.ShiftType;
//import com.fasterxml.jackson.databind.JavaType;
//import com.fasterxml.jackson.databind.type.TypeFactory;
//import com.fasterxml.jackson.databind.util.Converter;
//import org.springframework.stereotype.Component;
//
//@Component
//public class ShiftTypeDtoToShiftType implements Converter<ShiftTypeCreateDto, ShiftType> {
//
//    private final AuthenticationFacade authenticationFacade;
//
//    public ShiftTypeDtoToShiftType(AuthenticationFacade authenticationFacade) {
//        this.authenticationFacade = authenticationFacade;
//    }
//
//    @Override
//    public ShiftType convert(ShiftTypeCreateDto shiftTypeCreateDto) {
//        ShiftType shiftType = new ShiftType();
//        shiftType.setCity(shiftTypeCreateDto.getCity());
//        shiftType.setStatus(Status.ACTIVE);
//        shiftType.setCreatedBy(authenticationFacade.getAuthentication().getCity());
//        return shiftType;
//    }
//
//    @Override
//    public JavaType getInputType(TypeFactory typeFactory) {
//        return null;
//    }
//
//    @Override
//    public JavaType getOutputType(TypeFactory typeFactory) {
//        return null;
//    }
//}
