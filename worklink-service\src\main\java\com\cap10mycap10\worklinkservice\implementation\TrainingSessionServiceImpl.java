package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.AvailableTraininingsResultsDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionCreateDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionResultDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionUpdateDto;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.TrainingSessionReportStatus;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionAuthorizeDto;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionResultsDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.helpers.WorkerTrainingSessionComparator;
import com.cap10mycap10.worklinkservice.mapper.trainingsession.TrainingSessionToTrainingSessionResultDto;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerToWorkerResultDto;
import com.cap10mycap10.worklinkservice.mapper.workertrainingsession.WorkerTrainingSessionToWorkerTrainingSessionResultsDto;
import com.cap10mycap10.worklinkservice.model.*;

import com.cap10mycap10.worklinkservice.service.*;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;


@Service
@Slf4j
@Transactional
public class TrainingSessionServiceImpl implements TrainingSessionService {
    @Autowired
    private   TrainingSessionRepository trainingSessionRepository;
    @Autowired
    private   WorkerTraining workerTraining;
    @Autowired
    private EmailService emailService;
    @Autowired
    WorkerTrainingSessionService workerTrainingSessionService;
    @Autowired
    private PushNotification pushNotification;
    @Autowired
    private  NotificationService notificationService;
    @Autowired
    private   WorkerTrainingRepository workerTrainingRepository;
    @Autowired
    private   WorkerTrainingSessionToWorkerTrainingSessionResultsDto toWorkerTrainingSessionResultsDto;
    @Autowired
    private   TrainingService trainingService;
    @Autowired
    private   WorkerRepository workerRepository;
    @Autowired
    private   WorkerTrainingSessionRepository workerTrainingSessionRepository;
    @Autowired
    private LocationService locationService;
    @Autowired
    private   AgencyService agencyService;
    @Autowired
    private InvoiceRepository invoiceRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private   EntityManager entityManager;
    @Autowired
    private   AgencyRepository agencyRepository;
    @Autowired
    private   TrainingSessionToTrainingSessionResultDto trainingSessionToTrainingSessionResultDto;
    @Autowired
    private   WorkerToWorkerResultDto workerResultDto;



    @Transactional
    @Override
    public TrainingSessionResultDto addTrainingSession(TrainingSessionCreateDto trainingSessionCreateDto) {
        TrainingSession trainingSession = new TrainingSession();
        trainingSession.setTrainer(agencyService.getOne(trainingSessionCreateDto.getTrainerId()));
        Set<Agency> agencies = new HashSet<>();

        // Add the other agencies to the set of agencies
        for (Long agencyId : trainingSessionCreateDto.getAgencyIds()) {
            if (!agencies.contains(agencyService.getOne(agencyId))) {
                agencies.add(agencyService.getOne(agencyId));
            }
        }

        trainingSession.setLocation(locationService.getOne(trainingSessionCreateDto.getShiftLocationId()));
        trainingSession.setTraining(trainingService.findById(trainingSessionCreateDto.getTrainingId()));
        trainingSession.setTrainingStatus(TrainingSessionStatus.NEW);
        trainingSession.setName(trainingSessionCreateDto.getName());
        if(nonNull(trainingSessionCreateDto.getGetFeedback()))trainingSession.setGetFeedback(trainingSessionCreateDto.getGetFeedback());
        trainingSession.setSupervisor(trainingSessionCreateDto.getSupervisor());
        trainingSession.setPublishToAllWorkers(trainingSessionCreateDto.getPublishToAllWorkers());
        trainingSession.setPublishToAllAgencies(trainingSessionCreateDto.getPublishToAllAgencies());
        trainingSession.setTrainingCost(trainingSessionCreateDto.getTrainingCost());
        trainingSession.setBreakTimeMins(trainingSessionCreateDto.getBreakTime());
        trainingSession.setPostCode(trainingSessionCreateDto.getPostCode());
        trainingSession.setAddress(trainingSessionCreateDto.getAddress());
        trainingSession.setVacancies(trainingSessionCreateDto.getVacancies());
        trainingSession.setNotes(trainingSessionCreateDto.getNotes());
        trainingSession.setIsAgencyPaying(trainingSessionCreateDto.getIsAgencyPaying());
        if(!nonNull(trainingSessionCreateDto.getIsAgencyPaying())) trainingSession.setIsAgencyPaying(false);
        if(nonNull(trainingSessionCreateDto.getPublishToAllAgencies())&&trainingSessionCreateDto.getPublishToAllAgencies()){
            trainingSession.setIsAgencyPaying(false);
        }

        if (!(trainingSessionCreateDto.getEndDateTime().isAfter(trainingSessionCreateDto.getStartDateTime()))) {
            throw new BusinessValidationException("End-date-time should always be after start-date-time");
        }

        trainingSession.setStartDateTime(trainingSessionCreateDto.getStartDateTime());
        trainingSession.setEndDateTime(trainingSessionCreateDto.getEndDateTime());
        trainingSession.setAgencies(agencies);
        trainingSessionRepository.save(trainingSession);
        return trainingSessionToTrainingSessionResultDto.apply(trainingSession);
    }



    @Transactional
    public TrainingSessionResultDto findById(Long id) {
        return trainingSessionToTrainingSessionResultDto.apply(trainingSessionRepository.getOne(id));
    }



    @Override
    public Page<TrainingSessionResultDto>   findAllByAgencyIdAndTrainingStatus(Long agencyId, TrainingSessionStatus trainingStatus, Long trainingId, LocalDate startDate, LocalDate endDate, Boolean report, PageRequest of) {
        List<TrainingSession> trainingSessionsList;
        if(nonNull(report) && report) {
              trainingSessionsList = trainingSessionRepository.findByAgencyIdAndTrainingStatus(agencyId, "NEW");
              trainingSessionsList.addAll(trainingSessionRepository.findByAgencyIdAndTrainingStatus(agencyId, "CLOSED"));
        }else{
              trainingSessionsList = trainingSessionRepository.findByAgencyIdAndTrainingStatus(agencyId, trainingStatus.toString());
        }

        if(startDate!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> checkAfter(p.getStartDateTime().toLocalDate().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> checkBefore(p.getEndDateTime().toLocalDate().toString(), endDate))
                .collect(Collectors.toList());

        if(trainingId!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> Objects.equals(p.getTraining().getId(), trainingId))
                .collect(Collectors.toList());

        List<TrainingSession> filteredList = trainingSessionsList.stream()
                .filter((trainingSession) -> {
                    LocalDateTime now = LocalDateTime.now().minusDays(30);
                    boolean isClosedAndOld = trainingSession.getTrainingStatus()
                            .equals(TrainingSessionStatus.CLOSED) &&
                            now.isAfter(trainingSession.getEndDateTime());
                    return !isClosedAndOld;
                })
                .collect(Collectors.toList());

        Page<TrainingSession> page = PaginationUtil.paginateTrainingSession(of, filteredList);
        return page.map(trainingSessionToTrainingSessionResultDto);

    }


    @Override
    @Transactional
    public Page<TrainingSessionResultDto>   findAllByTrainerIdAndTrainingStatus(Long agencyId, TrainingSessionStatus trainingStatus, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of) {

        List<TrainingSession> trainingSessionsList = trainingSessionRepository.findByTrainerIdAndTrainingStatus(agencyId, trainingStatus.toString());

        if(startDate!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> checkAfter(p.getStartDateTime().toLocalDate().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> checkBefore(p.getEndDateTime().toLocalDate().toString(), endDate))
                .collect(Collectors.toList());

        if(trainingId!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> Objects.equals(p.getTraining().getId(), trainingId))
                .collect(Collectors.toList());

        List<TrainingSession> filteredList = trainingSessionsList.stream()
                .filter((trainingSession) -> {
                    LocalDateTime now = LocalDateTime.now().minusDays(30);
                    boolean isClosedAndOld = trainingSession.getTrainingStatus()
                            .equals(TrainingSessionStatus.CLOSED) &&
                            now.isAfter(trainingSession.getEndDateTime());
                    return !isClosedAndOld;
                })
                .collect(Collectors.toList());

        Page<TrainingSession> page = PaginationUtil.paginateTrainingSession(of, filteredList);
        return page.map(trainingSessionToTrainingSessionResultDto);

    }

    @Override
    @Transactional
    public Page<TrainingSessionResultDto>   findForTrainerBilling(Long trainerId,Long agencyId, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of) {

        List<WorkerTrainingSession> workerTrainingSessionsList = workerTrainingSessionRepository.findForAgencyTrainingBilling(trainerId, agencyId);

        List<TrainingSession> trainingSessionsList = new ArrayList<>();

        for (WorkerTrainingSession workerTrainingSession : workerTrainingSessionsList) {
            if (!trainingSessionsList.contains(workerTrainingSession.getTrainingSession())) {
                trainingSessionsList.add(workerTrainingSession.getTrainingSession());
            }
        }


        if(startDate!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> checkAfter(p.getStartDateTime().toLocalDate().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> checkBefore(p.getEndDateTime().toLocalDate().toString(), endDate))
                .collect(Collectors.toList());

        if(trainingId!=null) trainingSessionsList = trainingSessionsList.stream()
                .filter(p -> Objects.equals(p.getTraining().getId(), trainingId))
                .collect(Collectors.toList());

        List<TrainingSessionResultDto> trainingSessionResultDtos = trainingSessionsList.stream().map(trainingSessionToTrainingSessionResultDto).collect(Collectors.toList());

        for (TrainingSessionResultDto trainingSession : trainingSessionResultDtos) {
            trainingSession.setBookings(
                    workerTrainingSessionsList.stream()
                            .filter(p -> Objects.equals(p.getTrainingSession().getId(), trainingSession.getId()))
                            .map(toWorkerTrainingSessionResultsDto)
                            .collect(Collectors.toList())
            );
        }
        return PaginationUtil.paginateTrainingSessionDto(of, trainingSessionResultDtos);
    }



    @Override
    @Transactional
    public void cancelTrainingSession(Long agencyId, Long trainingSessionId) {
        TrainingSessionResultDto trainingSessionResultDto = findByIdAndAgencyId(trainingSessionId, agencyId);
        TrainingSession trainingSession = getOne(trainingSessionId);
        Agency agency = agencyService.getOne(agencyId);
        Set<TrainingSession> agencyTrainingSessionSet = agency.getTrainingSessionSet();

        Set<Worker> workerSet = trainingSession.getWorkers();

        Set<Long> workerIds = workerSet.stream()
                .map(Worker::getId)
                .collect(Collectors.toSet());
        LocalDateTime endDateTime = trainingSession.getEndDateTime();
        long hourDifference = ChronoUnit.HOURS.between(LocalDateTime.now(), endDateTime);
        if(hourDifference >= 24){
            trainingSession.setTrainingStatus(TrainingSessionStatus.CANCELLED);
            agency.setTrainingSessionSet(agencyTrainingSessionSet);
            for(Long id : workerIds){
                Worker worker = workerRepository.findById(id).get();
                WorkerTrainingSession workerTrainingSession = new WorkerTrainingSession();
                workerTrainingSession.setWorker(worker);
                workerTrainingSession.setTraining(trainingSession.getTraining());
                workerTrainingSession.setTrainingSession(trainingSession);
                workerTrainingSession.setTrainingStatus(WorkerTrainingSessionStatus.CANCELLED);

                agencyRepository.save(agency);
                trainingSessionRepository.save(trainingSession);
                workerTrainingSessionRepository.save(workerTrainingSession);
            }
        }
        else{
            throw new BusinessValidationException("You can only cancel a training session with more than 24 hours to happen");
        }
    }



    @Override
    @Transactional
    public Page<TrainingSessionResultDto> findAllPaged(TrainingSessionStatus status, PageRequest of) {
        Page<TrainingSession> trainingSessionList = trainingSessionRepository.findAllByTrainingStatusOrderByStartDateTime(status, of);
        return trainingSessionList.map(trainingSessionToTrainingSessionResultDto);
    }



    @Override
    public void save(TrainingSessionUpdateDto trainingSessionUpdateDto) {
    }

    @Override
    @Transactional
    public void agencyAddWorkersToTrainingSession(Long agencyId, Long trainingSessionId, Set<Long> workerIds) throws Exception {

        TrainingSessionResultDto trainingSessionResultDto1 = findByIdAndAgencyId(agencyId, trainingSessionId);
        Agency agency = agencyService.getOne(agencyId);

        if(trainingSessionResultDto1 == null){
            throw new BusinessValidationException(String.format("No training session for agency Id: %s and trainingId: %s", agencyId, trainingSessionId));
        }

        TrainingSession trainingSession = getOne(trainingSessionId);
        Set<Worker> workerSet = trainingSession.getWorkers();
        Integer initialVacancies = trainingSession.getVacancies();

        //Find workers in a training session and filter by those who have been booked and find their count
        Integer takenVacancies = Math.toIntExact(trainingSession.getWorkerTrainingSessions().stream()
                .filter(workerTrainingSession -> workerTrainingSession.getTrainingStatus().toString().equals("BOOKED"))
                .count());

        LocalDateTime currentTime = LocalDateTime.now();
        if (currentTime.isBefore(trainingSession.getEndDateTime())){
                for(Long id: workerIds){
                    if(initialVacancies - takenVacancies <= 0 )
                        throw new BusinessValidationException(String.format("No vacancies remaining for training session %s", trainingSession.getName()));;
                    Worker worker = workerRepository.findById(id).orElseThrow(
                            ()-> new RecordNotFoundException(String.format("Worker with id: %s not found", id)));

                    if(!workerSet.contains(worker)){
                        workerSet.add(worker);

                        WorkerTrainingSession workerTrainingSession = new WorkerTrainingSession();
                        workerTrainingSession.setWorker(worker);
                        workerTrainingSession.setAgency(agency);
                        workerTrainingSession.setTrainingSession(trainingSession);
                        workerTrainingSession.setTrainingStatus(WorkerTrainingSessionStatus.BOOKED);
                        workerTrainingSessionRepository.save(workerTrainingSession);

                        if(trainingSession.getIsAgencyPaying()==null || !trainingSession.getIsAgencyPaying()) {
                            Invoice invoice = createWorkerBookingInvoice(trainingSession, worker);


                            Payment payment = new Payment();

                            payment.setRef("Booked by agency");
                            payment.setStatus(InvoiceStatus.PAID);
                            payment.setTotal(invoice.getTotalAmount());
                            payment.setInvoice(invoice);

                            invoice.payInvoice(payment);


                            invoiceRepository.save(invoice);
                            paymentRepository.save(payment);
                        }
                        trainingSession.setWorkers(workerSet);
                       takenVacancies = Math.toIntExact(trainingSession.getWorkerTrainingSessions().stream()
                                .filter(workerSession -> workerSession.getTrainingStatus().toString().equals("BOOKED"))
                                .count());
                        trainingSessionRepository.save(trainingSession);

                        workerRepository.save(worker);


                        TrainingSessionResultDto training = trainingSessionToTrainingSessionResultDto.apply(trainingSession);


                        String title = "You Have been Booked for a Training!";
                        String body =                "Hi, "+workerTrainingSession.getWorker().getFirstname()+" you have been booked for "+trainingSession.getTraining().getName()+" training. Check the details below.\n" +
                                "Training booked" +
                                "\n" +
                                "Trainer:"+training.getTrainerName()+"\n" +
                                "Location:"+training.getShiftLocationName()+"\n" +
                                "Start Time:"+training.getStartDateTime()+"\n" +
                                "End Time:"+training.getEndDateTime()+
                                "" ;
                        NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

                        notificationCreateDto.setTitle(title);
                        notificationCreateDto.setBody(body);
                        notificationCreateDto.setWorkerId(workerTrainingSession.getWorker().getId());

                        notificationService.addWorkerNotification(notificationCreateDto);

                        workerTrainingSession.getWorker().getDevices().forEach(d->{


                            CompletableFuture.runAsync(() ->
                            {
                                try {
                                    pushNotification.sendPushMessage(
                                            title,

                                            body,

                                            d.getFcmToken()

                                    );
                                } catch (FirebaseMessagingException e) {
                                    throw new RuntimeException(e);
                                }
                            }

                            );


                        });

                        CompletableFuture.runAsync(() ->
                                emailService.sendSimpleMessage(workerTrainingSession.getWorker().getEmail(), title, body,agencyId)
                        );
                    }
                }

        }
        else{
            throw new BusinessValidationException(String.format("Training Date %s passed", trainingSession.getEndDateTime()));
        }
    }


    @Override
    @Transactional
    public void agencyUpdateTrainingSession( TrainingSessionUpdateDto trainingSessionUpdateDto) {
        TrainingSession currentTrainingSession = trainingSessionRepository.findById(trainingSessionUpdateDto.getId()).get();
        Agency agency = agencyService.getOne(trainingSessionUpdateDto.getAgencyId());

        Agency trainer = currentTrainingSession.getTrainer();

        Set<TrainingSession> trainingSessionSet = agency.getTrainingSessionSet();
        trainingSessionSet.remove(currentTrainingSession);

        TrainingSession trainingSession = trainingSessionRepository.findById(trainingSessionUpdateDto.getId())
                .map(existingTrainingSession -> {
                    if(trainingSessionUpdateDto.getName() != null){
                        existingTrainingSession.setName(trainingSessionUpdateDto.getName());
                    }
                    if(trainingSessionUpdateDto.getTrainingStatus() != null){
                        existingTrainingSession.setTrainingStatus(TrainingSessionStatus.valueOf(trainingSessionUpdateDto.getTrainingStatus().name()));
                    }
                    if(trainingSessionUpdateDto.getShiftLocationId() != null){
//                        existingTrainingSession.setLocation(shiftLocationService.getOne(trainingSessionUpdateDto.getShiftLocationId()));
                        Location location = locationService.getOne(trainingSessionUpdateDto.getShiftLocationId());
                        existingTrainingSession.setLocation(location);
                    }
                    if(trainingSessionUpdateDto.getPostCode() != null){
                        existingTrainingSession.setPostCode(trainingSessionUpdateDto.getPostCode());
                    }
                    if(trainingSessionUpdateDto.getAddress() != null){
                        existingTrainingSession.setAddress(trainingSessionUpdateDto.getAddress());
                    }
                    if(trainingSessionUpdateDto.getStartDateTime() != null){
                        existingTrainingSession.setStartDateTime(LocalDateTime.parse(trainingSessionUpdateDto.getStartDateTime().toString()));
                    }
                    if(trainingSessionUpdateDto.getEndDateTime() != null){
                        existingTrainingSession.setEndDateTime(LocalDateTime.parse(trainingSessionUpdateDto.getEndDateTime().toString()));
                    }

                    existingTrainingSession.setBreakTimeMins(trainingSessionUpdateDto.getBreakTime());

                    if(trainingSessionUpdateDto.getTrainingCost() != null){
                        existingTrainingSession.setTrainingCost(Double.valueOf(trainingSessionUpdateDto.getTrainingCost().toString()));
                    }
                    if(trainingSessionUpdateDto.getVacancies() != null){
                        existingTrainingSession.setVacancies(Integer.valueOf(trainingSessionUpdateDto.getVacancies().toString()));
                    }
                    if(trainingSessionUpdateDto.getNotes() != null){
                        existingTrainingSession.setNotes(trainingSessionUpdateDto.getNotes());
                    }
                    if(trainingSessionUpdateDto.getIsAgencyPaying() != null){
                        existingTrainingSession.setIsAgencyPaying(Boolean.valueOf(trainingSessionUpdateDto.getIsAgencyPaying().toString()));
                    }
                    return existingTrainingSession;
                }).orElseThrow(()-> new RecordNotFoundException(String.format("Training Session with id: {} not found", trainingSessionUpdateDto.getId())));

        if(trainingSession.getEndDateTime().isBefore(trainingSession.getStartDateTime())){
            throw new BusinessValidationException("End-date-time should always be after start-date-time");
        }
        List<WorkerTrainingSession> workerTrainingSession = workerTrainingSessionRepository.findByAgencyIdAndTrainingSessionId(trainingSessionUpdateDto.getAgencyId(), trainingSessionUpdateDto.getId());
        Collections.sort(workerTrainingSession, new WorkerTrainingSessionComparator());

        for(WorkerTrainingSession workerTrainingSession1 : workerTrainingSession){
            workerTrainingSession1.setTrainingSession(trainingSession);

            workerTrainingSessionRepository.save(workerTrainingSession1);
        }
        trainingSessionSet.add(trainingSession);
        agency.setTrainingSessionSet(trainingSessionSet);
        agencyRepository.save(agency);
        trainingSessionRepository.save(trainingSession);
    }

    @Override
    @Transactional
    public void cancelTrainingSession(Long trainingSessionId) {
        TrainingSession trainingSession = getOne(trainingSessionId);
        List<WorkerTrainingSession> bookingsList = workerTrainingSessionRepository.findByTrainingSessionIdAndTrainingStatusNot(trainingSessionId, WorkerTrainingSessionStatus.CANCELLED);




        LocalDateTime endDateTime = trainingSession.getEndDateTime();

        Long hourDifference = ChronoUnit.HOURS.between(LocalDateTime.now(), endDateTime);

        if(hourDifference >= 24){
            trainingSession.setTrainingStatus(TrainingSessionStatus.CANCELLED);
            for( WorkerTrainingSession booking : bookingsList){
                booking.setTrainingStatus(WorkerTrainingSessionStatus.CANCELLED);

                trainingSessionRepository.save(trainingSession);
                workerTrainingSessionRepository.save(booking);

                List<Invoice> invoices = invoiceRepository.findIndividualTrainingInvoice(trainingSession.getId(), booking.getWorker().getId());

                for(Invoice invoice : invoices) {
                    invoice.cancel();
                    invoiceRepository.save(invoice);
                }


                TrainingSessionResultDto training = trainingSessionToTrainingSessionResultDto.apply(trainingSession);


                Agency trainer = trainingSession.getTrainer();

                String title = "Training cancelled!";
                String body =                "Hi, "+booking.getWorker().getFirstname()
                        +" a training that you were part of, "
                        +trainingSession.getTraining().getName()+" has been cancelled.\n"+
                        trainingSession.getTraining().getName()+" has been cancelled.\n"
                        +"Training cancelled" +
                        "\n" +
                        "Trainer:"+trainer.getName()+"\n" +
                        "Location:"+trainingSession.getLocation().getCity()+"\n" +
                        "Start Time:"+trainingSession.getStartDateTime()+"\n" +
                        "End Time:"+trainingSession.getEndDateTime()+
                        ""
                         ;
                NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

                notificationCreateDto.setTitle(title);
                notificationCreateDto.setBody(body);
                notificationCreateDto.setWorkerId(booking.getWorker().getId());

                notificationService.addWorkerNotification(notificationCreateDto);

                booking.getWorker().getDevices().forEach(d->{


                        CompletableFuture.runAsync(() -> {
                                    try {
                                        pushNotification.sendPushMessage( title, body, d.getFcmToken() );
                                    } catch (FirebaseMessagingException e) {
                                        throw new RuntimeException(e);
                                    }
                        });


                });

                CompletableFuture.runAsync(() ->
                        emailService.sendSimpleMessage(booking.getWorker().getEmail(), title, body, trainingSession.getTrainer().getId())
                );


            }
        }
        else{
            throw new BusinessValidationException("You can only cancel a training session with more than 24 hours to begin");
        }
    }

    @Override
    @Transactional
    public Page<TrainingSessionResultDto> findByAgencyId(Long agencyId, PageRequest of) {

        List<TrainingSession> trainingSessionList = null;
        try{
            trainingSessionList = trainingSessionRepository.findByAgencyId(agencyId);
        }catch(NoResultException nre){

        }
        trainingSessionList.stream()
                .filter((trainingSession) -> {
                    LocalDateTime now = LocalDateTime.now().minusDays(30);
                    boolean isClosedAndOld = trainingSession.getTrainingStatus()
                            .equals(TrainingStatus.CLOSED) &&
                            now.isAfter(trainingSession.getEndDateTime());
                    return !isClosedAndOld;
                })
                .collect(Collectors.toList());

        Page<TrainingSession> page = PaginationUtil.paginateTrainingSession(of, trainingSessionList);

        return page.map(trainingSessionToTrainingSessionResultDto);
    }

    @Override
    @Transactional
    public TrainingSessionResultDto findByIdAndAgencyId( Long agencyId, Long trainingSessionId) {
        Agency agency = agencyService.getOne(agencyId);
        TrainingSession trainingSessionOne = getOne(trainingSessionId);
        return new TrainingSessionToTrainingSessionResultDto(agencyService).apply(trainingSessionOne);
    }

    @Override
    @Transactional
    public void workerAddSelfToTrainingSession(Long agencyId, Long trainingSessionId, Long workerId) throws Exception {
        TrainingSession currentTrainingSession = getOne(trainingSessionId);

        Agency agency = agencyService.getOne(agencyId);
        Worker worker = workerRepository.findById(workerId).get();

//        this just throws an exception in case there is no agency
        TrainingSession trainingSession = getOne(trainingSessionId);
        Agency trainer = trainingSession.getTrainer();


        if( !currentTrainingSession.getWorkers().contains(worker)) {

            if (agency.getWorkers().contains(worker)) {
                WorkerTrainingSession workerTrainingSession = new WorkerTrainingSession();
                workerTrainingSession.setWorker(worker);
                workerTrainingSession.setAgency(agency);
//                workerTrainingSession.setTrainerId(trainingSession.getTrainerId());
                workerTrainingSession.setTraining(currentTrainingSession.getTraining());
                workerTrainingSession.setTrainingSession(currentTrainingSession);
                workerTrainingSession.setTrainingStatus(WorkerTrainingSessionStatus.NEW);

                currentTrainingSession.getWorkers().add(worker);
                String body;
                if (nonNull(trainingSession.getIsAgencyPaying()) && !trainingSession.getIsAgencyPaying()) {
                    Invoice invoice = createWorkerBookingInvoice(trainingSession, worker);
                    invoiceRepository.save(invoice);
                     body = "Hi, "+workerTrainingSession.getWorker().getFirstname()+" you have applied for "+trainingSession.getTraining().getName()+" training.\n" +
                            "An invoice has been created for this training. Please pay the invoice at your earliest convenience to secure a booking." +
                            "" ;
                }else{
                     body = "Hi, "+workerTrainingSession.getWorker().getFirstname()+" you have applied for "+trainingSession.getTraining().getName()+" training.\n" +
                            "Please wait for your application to be approved by your Agency to secure a booking." +
                            "" ;
                }


                workerTrainingSessionRepository.save(workerTrainingSession);
                trainingSessionRepository.save(currentTrainingSession);
                workerRepository.save(worker);







                TrainingSessionResultDto training = trainingSessionToTrainingSessionResultDto.apply(trainingSession);


                String title = trainingSession.getTraining().getName()+ " training application received";

                NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

                notificationCreateDto.setTitle(title);
                notificationCreateDto.setBody(body);
                notificationCreateDto.setWorkerId(workerTrainingSession.getWorker().getId());

                notificationService.addWorkerNotification(notificationCreateDto);

                workerTrainingSession.getWorker().getDevices().forEach(d->{



                        CompletableFuture.runAsync(() ->
                        {
                            try {
                                pushNotification.sendPushMessage(
                                        title,

                                        body,

                                        d.getFcmToken()

                                );
                            } catch (FirebaseMessagingException e) {
                                throw new RuntimeException(e);
                            }
                        });


                });

                CompletableFuture.runAsync(() ->
                        emailService.sendSimpleMessage(workerTrainingSession.getWorker().getEmail(), title, body,agencyId)
                );


            } else
                throw new BusinessValidationException(String.format("Its either worker with id: %s is already in the training session or they are not a worker of agencyId: %s", workerId, agencyId));
        }
    }

    @Override
    @Transactional
    public void approveOrRejectWorkerBooking(Long bookingId, Boolean approveBooking, Boolean addPayment) {
        WorkerTrainingSession workerTrainingSession = workerTrainingSessionRepository.findById(bookingId).orElseThrow(
                ()-> new BusinessValidationException(String.format("No worker booking with id : %s exists", bookingId))
        );

        TrainingSession trainingSession = workerTrainingSession.getTrainingSession();
        Integer initialVacancies = trainingSession.getVacancies();
        Integer takenVacancies = Math.toIntExact(trainingSession.getWorkerTrainingSessions()
                .stream()
                .filter(workerTrainingSession1 -> workerTrainingSession1.getTrainingStatus().toString().equals(WorkerTrainingSessionStatus.BOOKED.toString()))
                .count());

        if(approveBooking){
            if(!workerTrainingSession.getTrainingStatus().toString().equals("BOOKED")){
                if(initialVacancies - takenVacancies >  0 ){
                    workerTrainingSession.setTrainingStatus(WorkerTrainingSessionStatus.BOOKED);

                    List<Invoice> invoices = invoiceRepository.findIndividualTrainingInvoice(trainingSession.getId(), workerTrainingSession.getWorker().getId());

                    if(addPayment) for(Invoice invoice : invoices) {
                        Payment payment = new Payment();
                        payment.setRef("Booked by agency");
                        payment.setStatus(InvoiceStatus.PAID);
                        payment.setTotal(invoice.getTotalAmount());
                        payment.setInvoice(invoice);
                        invoice.payInvoice(payment);
                        invoiceRepository.save(invoice);
                        paymentRepository.save(payment);
                    }


                }
                else{
                    throw new BusinessValidationException("There are no vacancies for the current training session.");
                }
            }
            trainingSessionRepository.save(trainingSession);
            workerTrainingSessionRepository.save(workerTrainingSession);


            TrainingSessionResultDto training = trainingSessionToTrainingSessionResultDto.apply(trainingSession);


            String title = "You Have been Booked for a Training!";
            String body =                "Hi, "+workerTrainingSession.getWorker().getFirstname()+" you have been booked for  "+trainingSession.getTraining().getName()+" training. Check the details below.\n" +
                    "Training booked" +
                    "\n" +
                    "Trainer:"+training.getTrainerName()+"\n" +
                    "Location:"+training.getShiftLocationName()+"\n" +
                    "Start Time:"+training.getStartDateTime()+"\n" +
                    "End Time:"+training.getEndDateTime()+
                    "" ;
            NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

            notificationCreateDto.setTitle(title);
            notificationCreateDto.setBody(body);
            notificationCreateDto.setWorkerId(workerTrainingSession.getWorker().getId());

            notificationService.addWorkerNotification(notificationCreateDto);

            workerTrainingSession.getWorker().getDevices().forEach(d->{
                CompletableFuture.runAsync(()-> {
                    try {
                        pushNotification.sendPushMessage(title, body, d.getFcmToken());
                    } catch (FirebaseMessagingException e) {
                        log.error("{}", e);
                    }
                });

            });

            CompletableFuture.runAsync(() ->
                    emailService.sendSimpleMessage(workerTrainingSession.getWorker().getEmail(), title, body, trainingSession.getTrainer().getId())
            );




        }
        else{
            if(workerTrainingSession.getTrainingStatus().toString().equals("BOOKED") && LocalDateTime.now().isBefore(trainingSession.getStartDateTime())){
//                trainingSession.setVacancies(trainingSession.getVacancies() + 1);
            }
            workerTrainingSession.setTrainingStatus(WorkerTrainingSessionStatus.REJECTED);
            trainingSessionRepository.save(trainingSession);
            workerTrainingSessionRepository.delete(workerTrainingSession);


            List<Invoice> invoices = invoiceRepository.findIndividualTrainingInvoice(trainingSession.getId(), workerTrainingSession.getWorker().getId());

            for(Invoice invoice : invoices) {
                invoice.cancel();
                invoiceRepository.save(invoice);
            }
        }



    }

    @Override
    public List<AvailableTraininingsResultsDto> availableTrainingsForWorker(Long workerId, Long trainingId) {
        List<TrainingSession> trainingSessionFiltered = trainingSessionRepository.findToBookByWorkerId(workerId);

        if(trainingId != null)
            trainingSessionFiltered =   trainingSessionFiltered.stream()
                    .filter((trainingSession -> Objects.equals(trainingSession.getTraining().getId(), trainingId)))
                    .collect(Collectors.toList());


        Map<Long, Long> availableTraininingsResultsDtoMap = trainingSessionFiltered
                .stream()
                .collect(Collectors.groupingBy(
                        trainingSession -> trainingSession.getTraining().getId(),
                        Collectors.counting()
                ));

        return availableTraininingsResultsDtoMap.entrySet()
                .stream()
                .map((entry) ->{
                    AvailableTraininingsResultsDto dto = new AvailableTraininingsResultsDto();
                    dto.setId(entry.getKey());
                    dto.setAvailableTrainings(entry.getValue());
                    dto.setTrainingName(trainingService.getOne(entry.getKey()).getName());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public TrainingSession findByBooking(WorkerTrainingSession shift) {
        return trainingSessionRepository.findByBooking(shift.getId());
    }


    @Override
    public TrainingSession getOne(Long id) {
        return trainingSessionRepository.findById(id).orElseThrow(
                ()-> new BusinessValidationException(String.format("Training Session with id: %s found", id))
        );
    }



    @Override
    @Transactional
    public WorkerTrainingSessionResultsDto authorizeWorker(WorkerTrainingSessionAuthorizeDto workerTrainingSessionAuthorizeDto) {
        WorkerTrainingSession workerTrainingSession = workerTrainingSessionRepository.findById(workerTrainingSessionAuthorizeDto.getWorkerTrainingBookingId()).orElseThrow(
                ()-> new BusinessValidationException(String.format("There is no booking with id: {} ",workerTrainingSessionAuthorizeDto.getWorkerTrainingBookingId()))
        );

        if(!(workerTrainingSession.getTrainingStatus().toString().equals("WAITING_AUTHORIZATION"))){
            throw new BusinessValidationException(String.format("Booking is no longer in awaiting authorization state.", workerTrainingSession.getWorker().getFirstname() + workerTrainingSession.getWorker().getLastname()));
        }
        if(! LocalDateTime.now().isAfter(workerTrainingSession.getTrainingSession().getEndDateTime())) {
            throw new BusinessValidationException(String.format("You are trying to authorize a booking before the training session has been completed. "));
        }

        if(workerTrainingSessionAuthorizeDto.getSkippedTraining()) {
            workerTrainingSession.setSkippedTraining(true);
            workerTrainingSession.setTrainingStatus(WorkerTrainingSessionStatus.REJECTED);

            workerTrainingSessionRepository.save(workerTrainingSession);
        }
        else{
            workerTrainingSession.setTrainingScore(workerTrainingSessionAuthorizeDto.getScore());
            workerTrainingSession.setSkippedTraining(false);
            workerTrainingSession.setShowCertificate(workerTrainingSessionAuthorizeDto.getGenerateCertificate());
            workerTrainingSession.setTrainingExpiryDate(workerTrainingSessionAuthorizeDto.getTrainingExpiryDate());
            workerTrainingSession.setTrainingStatus(WorkerTrainingSessionStatus.CLOSED);
            workerTrainingSession.setPassedTraining(workerTrainingSessionAuthorizeDto.getPassedTraining());

            workerTrainingSessionRepository.save(workerTrainingSession);
        }

        if(nonNull(workerTrainingSessionAuthorizeDto.getPassedTraining())&& workerTrainingSessionAuthorizeDto.getPassedTraining()){
            WorkerTraining workerTraining = new WorkerTraining();
            workerTraining.setName(workerTrainingSession.getTrainingSession().getTraining().getName());
            workerTraining.setTraining(workerTrainingSession.getTrainingSession().getTraining());
            workerTraining.setWorkerTrainingSession(workerTrainingSession);
            workerTraining.setAgency(workerTrainingSession.getTrainingSession().getTrainer());
            workerTraining.setTrainingDate(workerTrainingSession.getTrainingSession().getStartDateTime().toLocalDate());
            workerTraining.setTrainingExpiry(workerTrainingSession.getTrainingExpiryDate());
            workerTraining.setType(TrainingType.PHYSICAL);
            workerTraining.setWorker(workerTrainingSession.getWorker());
//            workerTraining.setUploaded(String.valueOf(LocalDate.now()));
            workerTrainingSession.setDateUploaded(LocalDate.now());

            workerTraining.setDescription(workerTrainingSession.getTrainingSession().getNotes());

            workerTrainingSessionRepository.save(workerTrainingSession);
            workerTrainingRepository.save(workerTraining);
        }
        return toWorkerTrainingSessionResultsDto.apply(workerTrainingSession);
    }


    @Override
    @Transactional
    public WorkerTrainingSessionResultsDto findWorkerTrainingBookingById(Long id) {
        WorkerTrainingSession workerTrainingSession = workerTrainingSessionRepository.findById(id).orElseThrow(
                ()-> new RecordNotFoundException(String.format("Worker booking for id: %s not found", id))
        );
        return toWorkerTrainingSessionResultsDto.apply(workerTrainingSession);
    }

    @Override
    @Transactional
    public void cancelTrainingBooking(Long id) {

        WorkerTrainingSession workerTrainingSession = workerTrainingSessionRepository.findById(id).orElseThrow(
                ()-> new RecordNotFoundException(String.format("Worker Training Book with id: %s not found", id))
        );
        TrainingSession trainingSession = workerTrainingSession.getTrainingSession();

        if(workerTrainingSession.getTrainingStatus().toString() == "BOOKED"){
            trainingSessionRepository.save(trainingSession);
        }
        workerTrainingSession.setTrainingStatus(WorkerTrainingSessionStatus.CANCELLED);
        workerTrainingSessionRepository.save(workerTrainingSession);

        List<Invoice> invoices = invoiceRepository.findIndividualTrainingInvoice(trainingSession.getId(), workerTrainingSession.getWorker().getId());

        for(Invoice invoice : invoices) {
            invoice.cancel();
            invoiceRepository.save(invoice);
        }

        Agency trainer = trainingSession.getTrainer();

        String title = "Training Booking cancelled!";
        String body =                "Hi, "+workerTrainingSession.getWorker().getFirstname()
                +" your booking for the training: "
                +trainingSession.getTraining().getName()+" has been cancelled.\n"
                +"Training cancelled" +
                "\n" +
                "Trainer:"+trainer.getName()+"\n" +
                "Location:"+trainingSession.getLocation().getCity()+"\n" +
                "Start Time:"+trainingSession.getStartDateTime()+"\n" +
                "End Time:"+trainingSession.getEndDateTime()+
                ""

                ;
        NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

        notificationCreateDto.setTitle(title);
        notificationCreateDto.setBody(body);
        notificationCreateDto.setWorkerId(workerTrainingSession.getWorker().getId());

        notificationService.addWorkerNotification(notificationCreateDto);

        workerTrainingSession.getWorker().getDevices().forEach(d->{

            try {
                pushNotification.sendPushMessage(
                        title,

                        body,

                        d.getFcmToken()

                );
            } catch (FirebaseMessagingException e) {
//                log.error("{}",e);
                log.error("An error occurred in firebase.");
            }

        });

        CompletableFuture.runAsync(() ->
                emailService.sendSimpleMessage(workerTrainingSession.getWorker().getEmail(), title, body, trainer.getId())
        );


    }

    @Override
    @Transactional
    public List<WorkerTrainingSessionResultsDto> findWorkersByTrainingSessionId(Long trainingSessionId) {
        TrainingSession trainingSession = getOne(trainingSessionId);

        List<WorkerTrainingSession> workersList = workerTrainingSessionRepository.findByTrainingSessionIdAndTrainingStatusNot(trainingSessionId, WorkerTrainingSessionStatus.CANCELLED);

        Collections.sort(workersList, new WorkerTrainingSessionComparator());

        return workersList.stream()
                .map(toWorkerTrainingSessionResultsDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<TrainingSessionResultDto> findByWorkerIdAndTrainingId(Long workerId, Long trainingId) {

        List<TrainingSession> trainingSessionList = trainingSessionRepository.findToBookByWorkerIdAndTrainingId(workerId, trainingId);
        LocalDateTime now = LocalDateTime.now();

        return trainingSessionList.stream()
                .filter((trainingSession) -> trainingSession.getTrainingStatus()
                       .equals(TrainingSessionStatus.NEW) &&
                       now.isBefore(trainingSession.getEndDateTime()))
                .map(trainingSessionToTrainingSessionResultDto)
                .collect(Collectors.toList());
    }



    private boolean checkAfter(String leftDate, LocalDate rightDate) {
        return convertFromString(leftDate).compareTo(rightDate) >= 0;
    }

    private boolean checkBefore(String leftDate, LocalDate rightDate) {
        return convertFromString(leftDate).compareTo(rightDate) <= 0;
    }

    private LocalDate convertFromString(String aDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        } catch (DateTimeParseException e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        }
    }


    public Invoice createWorkerBookingInvoice( TrainingSession trainingSession, Worker worker ) throws Exception {


        Invoice invoice = new Invoice();



        invoice.setInvoiceType(InvoiceType.WORKERTRAINING);
        invoice.setAgency(trainingSession.getTrainer());

        invoice.setDueDate(trainingSession.getStartDateTime().toLocalDate());
        invoice.setWorkerId(worker.getId());

        // Generate Invoice Items
        InvoiceItem invoiceItem = new InvoiceItem();
        invoiceItem.setTrainingId(trainingSession.getId());
        invoiceItem.setNumberOfHoursWorked(1);
        invoiceItem.setRate(BigDecimal.valueOf(trainingSession.getTrainingCost()));
        invoiceItem.setTotal(BigDecimal.valueOf(trainingSession.getTrainingCost()));
        invoiceItem.setDescription(trainingSession.getTraining().getName()+" training");
        invoiceItem.setWorker(worker.getFirstname()+ " "+ worker.getLastname());
        invoice.addInvoiceItem(invoiceItem);


        invoice.setInvoiceDate(LocalDate.now());

        BigDecimal subtotalAmount = invoice.getInvoiceItems().stream()
                .map(InvoiceItem::getTotal)
                .reduce(BigDecimal::add).get();

//        invoice.setSubTotalAmount(invoice.getInvoiceItems().stream()
//                .map(InvoiceItem::getTotal)
//                .reduce(BigDecimal::add).get());

//        invoice.setTotalAmount(invoice.getInvoiceItems().stream()
//                .map(InvoiceItem::getTotal)
//                .reduce(BigDecimal::add).get());


        return invoice;



    }




    @Override
    public TrainingSessionReportStatus agencySessionsCount(Long agencyId) {

        Page<WorkerTrainingSessionResultsDto> awaitingBookings = workerTrainingSessionService.findForAgency(agencyId, WorkerTrainingSessionStatus.WAITING_AUTHORIZATION, null, null, null, PageRequest.of(0, 5));
        Page<WorkerTrainingSessionResultsDto> closedBookings = workerTrainingSessionService.findForAgency(agencyId, WorkerTrainingSessionStatus.CLOSED, null,  null, null, PageRequest.of(0, 5));
        Page<TrainingSessionResultDto> newSessions = findAllByAgencyIdAndTrainingStatus(agencyId, TrainingSessionStatus.NEW, null, null, null,false, PageRequest.of(0, 5));

        TrainingSessionReportStatus trainingSessionReportStatus = new TrainingSessionReportStatus();

        trainingSessionReportStatus.setNewTrainingSession(newSessions.getTotalElements());
        trainingSessionReportStatus.setAwaitingBookings(awaitingBookings.getTotalElements());
        trainingSessionReportStatus.setClosedBookings(closedBookings.getTotalElements());


        return trainingSessionReportStatus;
    }

    @Override
    public TrainingSessionReportStatus trainerSessionsCount(Long agencyId ) {

        Page<WorkerTrainingSessionResultsDto> awaitingBookings = workerTrainingSessionService.findForTrainer(agencyId, WorkerTrainingSessionStatus.WAITING_AUTHORIZATION, null, null, null, PageRequest.of(0, 5));
        Page<WorkerTrainingSessionResultsDto> closedBookings = workerTrainingSessionService.findForTrainer(agencyId, WorkerTrainingSessionStatus.CLOSED, null,  null, null, PageRequest.of(0, 5));
        Page<TrainingSessionResultDto> newSessions = findAllByTrainerIdAndTrainingStatus(agencyId, TrainingSessionStatus.NEW, null, null, null, PageRequest.of(0, 5));

        TrainingSessionReportStatus trainingSessionReportStatus = new TrainingSessionReportStatus();

        trainingSessionReportStatus.setNewTrainingSession(newSessions.getTotalElements());
        trainingSessionReportStatus.setAwaitingBookings(awaitingBookings.getTotalElements());
        trainingSessionReportStatus.setClosedBookings(closedBookings.getTotalElements());


        return trainingSessionReportStatus;
    }

}
