package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.PromotionType;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.*;

import jakarta.persistence.*;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static java.util.Objects.nonNull;


@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Slf4j
@SQLDelete(sql="UPDATE promotion SET deleted_at = CURRENT_TIMESTAMP WHERE id =?")
@Where(clause = "deleted_at IS NULL")
public class Promotion extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(unique = true)
    private String code;
    private String title;
    private String description;
    private String htmlBody;
    @Column(nullable = false)
    private ZonedDateTime expiryDate;
    @Column(nullable = false)
    private ZonedDateTime startDate;
//    private ZonedDateTime endDate;
    private int daysHired;

    private float discount;
    private int extraMileage;
    private int extraDays;
//    private String otherAward;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PromotionType promotionType;

    @Column(nullable = false)
    private Integer usageLimit;
    @Enumerated(EnumType.STRING)
    private Status status = Status.INACTIVE;
    private ZonedDateTime cancelledDate;
    private ZonedDateTime activatedDate;
    private Boolean adminDiscount;


    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    private Agency agency;

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "promotion", cascade = CascadeType.ALL)
    Set<VehicleBooking> bookings= new HashSet<>();


    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY)
    private Set<Invoice> invoices = new HashSet<>();

    @ManyToMany(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinTable(name = "promotion_vehicle", joinColumns = {
            @JoinColumn(name = "promotion_id", referencedColumnName = "id")}, inverseJoinColumns = {
            @JoinColumn(name = "vehicle_id", referencedColumnName = "id")})
    private Set<Vehicle> vehicles = new HashSet<>();

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToOne(mappedBy ="promotion", fetch = FetchType.EAGER)
    private VehicleFilter vehicleFilter;

    public Promotion() {
    }
    public Promotion(PromotionDto promotionDto, Agency agency) {
        this(promotionDto);
        this.agency = agency;
    }

    public Promotion(PromotionDto promotionDto) {
        this();
        this.code = promotionDto.getCode();
        this.adminDiscount = promotionDto.getAdminDiscount();
        this.description = promotionDto.getDescription();
        this.expiryDate = promotionDto.getExpiryDate();
        this.startDate = promotionDto.getStartDate();
        if(promotionDto.getPromotionType()== PromotionType.DISCOUNT)this.discount = promotionDto.getDiscount();
        this.usageLimit = promotionDto.getUsageLimit();

        if(promotionDto.getPromotionType()== PromotionType.EXTRA_DAYS) this.extraDays = promotionDto.getExtraDays();
        if(promotionDto.getPromotionType()== PromotionType.EXTRA_MILEAGE)  this.extraMileage = promotionDto.getExtraMileage();
        this.setPromotionType(promotionDto.getPromotionType());

       this.daysHired = promotionDto.getDaysHired();
//       promotionDto.getVehicleFilter().setPromotion(this);
//       this.vehicleFilter = promotionDto.getVehicleFilter();
//       this.extraMileage = promotionDto.getExtraMileage();
//       this.extraDays = promotionDto.getExtraDays();
       this.title = promotionDto.getTitle();
       this.htmlBody = promotionDto.getHtmlBody();
    }

    public void cancel() {
        this.cancelledDate = ZonedDateTime.now();
        this.status = Status.COMPLETED;
    }

    public void activate() {
        this.status = Status.ACTIVE;
        this.activatedDate = ZonedDateTime.now();
        log.info("Promotion {} activated", this);
    }

    public Integer getUsageCount() {
        return this.bookings.size();
    }

    public void setCode(String code) {
        if(nonNull(code))this.code = code.toUpperCase();
    }


    public void addVehicles(Set<Vehicle> vehicles) {
        vehicles.forEach(v->v.getPromotions().add(this));
        this.getVehicles().addAll(vehicles);
    }
}
