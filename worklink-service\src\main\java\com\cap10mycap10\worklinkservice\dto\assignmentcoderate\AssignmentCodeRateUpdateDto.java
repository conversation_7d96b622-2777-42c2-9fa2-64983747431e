package com.cap10mycap10.worklinkservice.dto.assignmentcoderate;


import lombok.Data;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalTime;

@Data
public class AssignmentCodeRateUpdateDto {

    private Long id;

    private Long assignmentCodeId;

    private Long agencyId;

    private Long clientId;

    private Long shiftTypeId;

    private DayOfWeek dayOfWeek;//Day

    private LocalTime startTime;

    private LocalTime endTime;

    private BigDecimal clientRate;

    private BigDecimal privateRate;

    private BigDecimal umbrellaRate;

    private BigDecimal payeRate;

    private Long locationId;

    private Long directorateId;

    
}
