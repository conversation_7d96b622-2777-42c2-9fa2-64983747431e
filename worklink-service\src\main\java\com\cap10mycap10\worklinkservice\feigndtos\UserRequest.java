package com.cap10mycap10.worklinkservice.feigndtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;



@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserRequest {

    private String firstName;

    private String lastName;

    private String username;

    private String email;

    private Long agentId;

    private Long clientId;

    private Long roleId;



}