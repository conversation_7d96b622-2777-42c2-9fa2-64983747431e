package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.dto.contact.ContactMessageDto;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ContactController {

    @Autowired
    private EmailService emailService;

    @Value("${env.supportEmail}")
    private String supportEmail;

    @Value("${env.companyName}")
    private String companyName;

    /**
     * Handle contact form submissions
     */
    @PostMapping(value = "contact", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> submitContactForm(@Valid @RequestBody ContactMessageDto contactDto) {
        log.info("Contact form submission received from: {} {}", contactDto.getFirstName(), contactDto.getLastName());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Create email subject and content
            String subject = String.format("New Contact Form Message from %s %s", 
                contactDto.getFirstName(), contactDto.getLastName());
            
            String emailContent = String.format(
                "New Contact Form Submission\n\n" +
                "Name: %s %s\n" +
                "Email: %s\n" +
                "Message:\n%s\n\n" +
                "---\n" +
                "This message was sent from the %s contact form at %s",
                contactDto.getFirstName(),
                contactDto.getLastName(),
                contactDto.getEmail(),
                contactDto.getMessage(),
                companyName,
                java.time.LocalDateTime.now()
            );

            // Send email to support team using agency ID 1 (default agency)
            emailService.sendSimpleMessage(supportEmail, subject, emailContent, 1L);
            
            // Send confirmation email to customer
            String confirmationSubject = String.format("Thank you for contacting %s", companyName);
            String confirmationContent = String.format(
                "Dear %s,\n\n" +
                "We have received your message and will get back to you as soon as possible.\n\n" +
                "Your message:\n%s\n\n" +
                "Best regards,\n" +
                "%s Team\n\n" +
                "Email us: %s\n" +
                "Visit our website: https://mykarlink.com",
                contactDto.getFirstName(),
                contactDto.getMessage(),
                companyName,
                supportEmail
            );
            
            // Send confirmation email (don't fail if this fails)
            try {
                emailService.sendSimpleMessage(contactDto.getEmail(), confirmationSubject, confirmationContent, 1L);
            } catch (Exception e) {
                log.warn("Failed to send confirmation email to customer: {}", e.getMessage());
            }
            
            response.put("success", true);
            response.put("message", "Message sent successfully! We'll get back to you soon.");
            
            log.info("Contact form message sent successfully to support email: {}", supportEmail);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing contact form submission", e);
            response.put("success", false);
            response.put("message", "Failed to send message. Please try again later.");
            return ResponseEntity.status(500).body(response);
        }
    }
}
