package com.cap10mycap10.worklinkservice.dto.worker;


import com.cap10mycap10.worklinkservice.enums.Gender;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;

@Data

public class WorkerCreateDto {

    private String firstname;

    private String lastname;

    private Gender gender;

    private String phoneNumber;

    private String email;

    private String username;

    private String postcode;

    private String address;

    private String profilePic;

    private Long assignmentCode;

    private String employmentNumber;

    private String nationality;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dob;

    private String cv;

    private Long agencyId;
    private Long deputyId;
    private Instant createdDate;



}
