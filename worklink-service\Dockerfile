FROM eclipse-temurin:21-jre-alpine

EXPOSE 8300

# Create app directory
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Copy the jar file
COPY target/worklink-service-0.0.1-SNAPSHOT.jar /opt/worklink-service-0.0.1-SNAPSHOT.jar

# Change ownership of the jar file
RUN chown appuser:appgroup /opt/worklink-service-0.0.1-SNAPSHOT.jar

WORKDIR /opt

# Switch to non-root user
USER appuser

# Use exec form for better signal handling
ENTRYPOINT ["java", "-jar", "worklink-service-0.0.1-SNAPSHOT.jar"]
