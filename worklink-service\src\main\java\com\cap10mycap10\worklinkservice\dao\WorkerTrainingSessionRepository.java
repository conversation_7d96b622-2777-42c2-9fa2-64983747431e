package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.cap10mycap10.worklinkservice.model.TrainingSession;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.model.WorkerTrainingSession;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface WorkerTrainingSessionRepository extends JpaRepository<WorkerTrainingSession, Long> {

    Optional<WorkerTrainingSession> findById(Long id);
    List<WorkerTrainingSession> findAll();



    Optional<WorkerTrainingSession> findByWorkerIdAndTrainingSessionId(Long workerId, Long trainingSessionId);

    List<WorkerTrainingSession> findByAgencyIdAndTrainingStatus(Long agencyId, WorkerTrainingSessionStatus trainingStatus);

    List<WorkerTrainingSession> findByAgencyIdAndTrainingSessionId(Long agencyId, Long trainingSessionId);

    List<WorkerTrainingSession> findByWorkerId(Long workerId);

    List<WorkerTrainingSession> findByWorkerAndTrainingStatus(Worker worker, WorkerTrainingSessionStatus trainingStatus);

    List<WorkerTrainingSession> findByTrainingSessionIdAndTrainingStatusNot(Long trainingSessionId, WorkerTrainingSessionStatus workerTrainingSessionStatus);


    @Query(value = "select id from worker_training_session where agency_id = ?1 and is_admin_billed = false", nativeQuery = true)
    List<Long> findByAgencyIdAndNotAdminBilled(Long trainerId);


    @Query(value = "select * from worker_training_session where training_session_id in  \n" +
            "(select id from training_session where end_date_time <= CURRENT_TIMESTAMP() and training_status <> 'CANCELLED') \n" +
            "and training_status = 'BOOKED'", nativeQuery = true)
    List<WorkerTrainingSession> findAllCompleteBooked();

    @Query(value = "select * from worker_training_session where training_session_id in  \n" +
            "(select id from training_session where end_date_time <= CURDATE() and training_status = 'CANCELLED') \n" +
            "and worker_training_session.training_status = 'BOOKED'", nativeQuery = true)
    List<WorkerTrainingSession> findWhereTrainingSessionCancelled();

    @Query(value = "select * from worker_training_session  \n" +
            "                      inner join training_session ts on ts.id = training_session_id \n" +
            "        where is_agency_billed = false \n" +
            "                      and is_agency_paying = true  \n" +
            "                      and agency_id = ?2 and trainer_id = ?1 \n" +
            "                      and ts.end_date_time <= CURDATE() \n" +
            "and worker_training_session.training_status in ('NEW',  'WAITING_AUTHORIZATION',  'BOOKED','CLOSED') ", nativeQuery = true)
    List<WorkerTrainingSession> findForAgencyTrainingBilling(Long trainerId, Long agency_id);

    @Query(value = "SELECT * FROM `worker_training_session`   as wt\n" +
            "LEFT JOIN training_feedback tf ON tf.booking_id = wt.id\n" +
            "where wt.worker_id = ?1 \n" +
            "and wt.training_status = 'CLOSED' \n" +
            "and (wt.feedback_skipped = false or wt.feedback_skipped is null) \n" +
            "and tf.id is null \t\n" +
            "and training_session_id in (\n" +
            " select id from training_session as ts where ts.start_date_time  > DATE_ADD(NOW(), INTERVAL -30 DAY)\n" +
            ");", nativeQuery = true)
    List<WorkerTrainingSession> findFeedbackPending(Long workerId);

    @Query(value = "select * from worker_training_session  \n" +
            "inner join training_session ts on ts.id = training_session_id \n" +
            "where  trainer_id = ?1 and worker_training_session.training_status = ?2  \n" +
            "and worker_training_session.training_status in ('NEW',  'WAITING_AUTHORIZATION',  'BOOKED','CLOSED') ", nativeQuery = true)
    List<WorkerTrainingSession> findForTrainer(Long trainerId, String trainingStatus);



    List<WorkerTrainingSession> findByWorkerIdAndTrainingId(Long workerId, Long trainingId);

    WorkerTrainingSession findByWorkerAndTrainingSession(Worker worker, TrainingSession trainingSession);

    List<WorkerTrainingSession> findAllByWorkerIdAndTrainingStatus(Long worker, WorkerTrainingSessionStatus status);

}
