package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.billing.VatRateCreateDto;
import com.cap10mycap10.worklinkservice.dto.billing.VatRateDto;
import com.cap10mycap10.worklinkservice.model.VatRate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface VatRateService {

    void save(VatRateCreateDto vatRateDto);

    void update(VatRateDto vatRateDto);

    VatRate findById(Long id);

    List<VatRate> findAll();

    Page<VatRate> findAllPaged(PageRequest of);

    void deleteById(Long id);

    VatRate getOne(Long id);


}
