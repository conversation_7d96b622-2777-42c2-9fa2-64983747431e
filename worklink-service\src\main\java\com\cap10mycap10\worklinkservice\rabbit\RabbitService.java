package com.cap10mycap10.worklinkservice.rabbit;

import lombok.extern.slf4j.Slf4j;

/*@Service*/
@Slf4j
public class RabbitService {

    /*@Autowired
    private RabbitTemplate rabbitTemplate;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static boolean isValidJson(Shift string) throws JsonProcessingException {


        ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
        String json = ow.writeValueAsString(string);
        log.info("This is the string: {}", json);

        String debit = OBJECT_MAPPER.writeValueAsString(json);

        log.info("This is the json object: {}", debit);
        try {
            OBJECT_MAPPER.readTree(debit);
            return true;

        } catch (Exception e) {
            return false;
        }


    }


    public void publish(String exchange, String routingKey, Shift message) throws JsonProcessingException {
        if (!isValidJson(message)) {
            throw new BusinessValidationException("Not a transaction");
        } else {
            try {
                ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
                String json = ow.writeValueAsString(message);
                rabbitTemplate.convertAndSend(exchange, routingKey, json);
            } catch (Exception e) {
                log.error("Error when publishing : {}", e.getMessage());
                throw new BusinessValidationException(e.getMessage());
            }
        }
    }*/


}
