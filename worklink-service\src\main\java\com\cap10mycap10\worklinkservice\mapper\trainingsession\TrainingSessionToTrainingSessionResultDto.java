package com.cap10mycap10.worklinkservice.mapper.trainingsession;

import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionResultDto;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.TrainingSession;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;


@Service
public class TrainingSessionToTrainingSessionResultDto implements Function<TrainingSession, TrainingSessionResultDto> {
    private final AgencyService agencyService;


    public TrainingSessionToTrainingSessionResultDto(AgencyService agencyService) {
        this.agencyService = agencyService;
    }

    @Override
    public TrainingSessionResultDto apply(TrainingSession trainingSession) {

        TrainingSessionResultDto trainingSessionResultDto = new TrainingSessionResultDto();
        trainingSessionResultDto.setId(trainingSession.getId());
        trainingSessionResultDto.setName(trainingSession.getName());
        trainingSessionResultDto.setTrainingId(trainingSession.getTraining().getId());
        trainingSessionResultDto.setTrainingName(trainingSession.getTraining().getName());
        trainingSessionResultDto.setTrainerName(trainingSession.getTrainer().getName());
        trainingSessionResultDto.setTrainerId(trainingSession.getTrainer().getId());
        trainingSessionResultDto.setTrainingCost(trainingSession.getTrainingCost());
        trainingSessionResultDto.setTrainingStatus(trainingSession.getTrainingStatus().toString());
        trainingSessionResultDto.setBreakTime(trainingSession.getBreakTimeMins());
        trainingSessionResultDto.setStartDateTime(trainingSession.getStartDateTime());
        trainingSessionResultDto.setEndDateTime(trainingSession.getEndDateTime());
        trainingSessionResultDto.setAddress(trainingSession.getAddress());
        trainingSessionResultDto.setShiftLocationId(trainingSession.getLocation().getId());
        trainingSessionResultDto.setShiftLocationName(trainingSession.getLocation().getCity());
        trainingSessionResultDto.setVacancies(trainingSession.getVacancies());
        trainingSessionResultDto.setPostCode(trainingSession.getPostCode());
        trainingSessionResultDto.setApplicantCount(trainingSession.getApplicantCount());
        trainingSessionResultDto.setNotes(trainingSession.getNotes());
        trainingSessionResultDto.setIsAgencyPaying(trainingSession.getIsAgencyPaying());
        trainingSessionResultDto.setPublishToAllWorkers(trainingSession.getPublishToAllWorkers());
        trainingSessionResultDto.setPublishToAllAgencies(trainingSession.getPublishToAllAgencies());
        List<Long> agencyIds = new ArrayList<>();
        for (Agency agency : trainingSession.getAgencies()) {
            agencyIds.add(agency.getId());
        }
        trainingSessionResultDto.setAgencyIds(agencyIds);

        return trainingSessionResultDto;
    }
}
