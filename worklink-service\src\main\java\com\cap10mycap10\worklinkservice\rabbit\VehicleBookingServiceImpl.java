//package com.cap10mycap10.worklinkservice.rabbit;
//
//import com.cap10mycap10.worklinkservice.dao.*;
//import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDto;
//import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
//import com.cap10mycap10.worklinkservice.enums.*;
//import com.cap10mycap10.worklinkservice.events.email.EmailService;
//import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
//import com.cap10mycap10.worklinkservice.helpers.PaynowHelper;
//import com.cap10mycap10.worklinkservice.helpers.PushNotification;
//import com.cap10mycap10.worklinkservice.mapper.invoice.InvoiceToInvoiceResult;
//import com.cap10mycap10.worklinkservice.mapper.shift.ShiftToShiftResultDto;
//import com.cap10mycap10.worklinkservice.mapper.vehiclebooking.VehicleBookingToVehicleBookingDto;
//import com.cap10mycap10.worklinkservice.model.*;
//import com.cap10mycap10.worklinkservice.service.*;
//import com.stripe.Stripe;
//import com.stripe.exception.StripeException;
//import com.stripe.model.Charge;
//import com.stripe.model.PaymentIntent;
//import com.stripe.param.PaymentIntentCreateParams;
//import com.stripe.param.checkout.SessionRetrieveParams;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.domain.Page;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.client.RestTemplate;
//import zw.co.paynow.exceptions.HashMismatchException;
//import zw.co.paynow.responses.StatusResponse;
//import zw.co.paynow.responses.WebInitResponse;
//
//import java.math.BigDecimal;
//import java.time.*;
//import java.time.temporal.ChronoUnit;
//import java.util.*;
//import java.util.concurrent.CompletableFuture;
//import java.util.stream.Collectors;
//
//
//import static com.cap10mycap10.worklinkservice.config.AppConfiguration.dateTimeFormat;
//import static java.util.Objects.nonNull;
//
//@Service
//@Slf4j
//@Transactional(rollbackFor = Exception.class)
//public class VehicleBookingServiceImpl {
//    @Value("${env.companyName}")
//    private String companyName;
//    @Value("${env.supportEmail}")
//    private String supportEmail;
//    @Value("${env.systemCurrency}")
//    private String systemCurrency;
//    @Value("${env.systemCurrency3}")
//    private String systemCurrency3;
//
//    @Value("${env.STRIPE_SECRET_KEY}")
//    private String STRIPE_SECRET_KEY;
//
//    @Value("${env.STRIPE_PUBLIC_KEY}")
//    private String STRIPE_PUBLIC_KEY;
//    @Value("${env.website}")
//    private String website;
//    @Autowired
//    private VehicleBookingToVehicleBookingDto toVehicleBookingDto;
//    @Autowired
//    private ClientService clientService;
//    @Autowired
//    private InvoiceService invoiceService;
//    @Autowired
//    private ClientRepository clientRepository;
//
//    @Autowired
//    private AgencyRepository agencyRepository;
//    @Autowired
//    private InvoiceToInvoiceResult toInvoiceResult;
//    @Autowired
//    private InvoiceRepository invoiceRepository;
//    @Autowired
//    private EmailService emailService;
//    @Autowired
//    private VehicleService vehicleService;
//    @Autowired
//    private PromoCodeService promoCodeService;
//    @Autowired
//    private PushNotification pushNotification;
//    @Autowired
//    private WorkerService workerService;
//    @Autowired
//    private ShiftDirectorateService shiftDirectorateService;
//    @Autowired
//    private NotificationService notificationService;
//    @Autowired
//    private VehicleBookingRepository vehicleBookingRepository;
//    @Autowired
//    private ShiftRepository transportBookingRepository;
//    @Autowired
//    private ShiftToShiftResultDto toBookingResultDto;
//    @Autowired
//    private AuthenticationFacadeService authenticationFacadeService;
//    @Autowired
//    private PaynowHelper paynowHelper;
//
//
//
//    private void validateBookingRequest(VehicleBookingDto vehicleBookingDto) {
//        if (!nonNull(vehicleBookingDto.getEmail())) {
//            throw new BusinessValidationException("Please specify the hirer email to use");
//        }
//    }
//
//    private Client handleClient(VehicleBookingDto vehicleBookingDto) {
//        Client client = clientRepository.findByEmail(vehicleBookingDto.getEmail());
//        if (!nonNull(client)) {
//            client = new Client();
//            client.setName(vehicleBookingDto.getFirstname() + " " + vehicleBookingDto.getSurname());
//            client.setEmail(vehicleBookingDto.getEmail());
//            client.setBillingEmail(vehicleBookingDto.getEmail());
//            client.setTelephone(vehicleBookingDto.getPhone());
//            client.setStatus(Status.ACTIVE);
//            client = clientRepository.save(client);
//        } else if (!nonNull(client.getTelephone())) {
//            client.setTelephone(vehicleBookingDto.getPhone());
//            client = clientRepository.save(client);
//        }
//        return client;
//    }
//
//    private Promotion handlePromotion(Vehicle vehicle, VehicleBooking vehicleBooking, VehicleBookingDto vehicleBookingDto) {
//        Promotion promotion = null;
//        if (nonNull(vehicleBookingDto.getPromoCode())) {
//            promotion = promoCodeService.getOneByCode(vehicleBookingDto.getPromoCode());
//        } else {
//            promotion = vehicle.getLatestValidPublicPromotion(vehicleBooking);
//        }
//
//        if (nonNull(promotion)) {
//            vehicle.validatePromo(promotion, vehicleBooking);
//        }
//        return promotion;
//    }
//
//    private Invoice handleInvoice(VehicleBooking vehicleBooking, Promotion promotion, VehicleBookingDto vehicleBookingDto) {
//        if (nonNull(promotion)) {
//            vehicleBooking.generateInvoice(promotion);
//        } else {
//            vehicleBooking.generateInvoice();
//        }
//
//        Invoice invoice = (new ArrayList<>(vehicleBooking.getInvoices())).get(0);
//
//        if (vehicleBookingDto.getByAgency()) {
//            if (nonNull(vehicleBookingDto.getDiscount()) && vehicleBookingDto.getDiscount().compareTo(BigDecimal.ZERO) > 0) {
//                invoice.setDiscount(vehicleBookingDto.getDiscount());
//            }
//            if (vehicleBookingDto.getPaidAmount() > 0) {
//                Payment payment = new Payment(vehicleBookingDto.getPaidAmount(), "Manual payment");
//                invoice.payInvoice(payment);
//                log.info("Payment was successful ref: " + payment.getRef() + " Invoice id: " + invoice.getId());
//            }
//        }
//        return invoice;
//    }
//
//    private VehicleBooking finalizeBooking(VehicleBooking vehicleBooking, Invoice invoice, VehicleBookingDto vehicleBookingDto) {
//        if (vehicleBookingDto.getByAgency()) {
//            invoice.getVehicleBooking().setStatus(VehicleBookingStatus.BOOKED);
//            VehicleBooking booking = vehicleBookingRepository.save(invoice.getVehicleBooking());
//            try {
//                invoice.setVehicleBooking(booking);
//                emailBookingReserved(invoice);
//                return booking;
//            } catch (Exception e) {
//                log.error("A minor error occurred while creating booking emails but booking and payment were saved: " + e.toString() + "\n");
//                log.error(Arrays.toString(e.getStackTrace()));
//                return booking;
//            }
//        }
//
//        VehicleBooking booking = vehicleBookingRepository.save(vehicleBooking);
//        if (!vehicleBookingDto.getByAgency()) {
//            instantiateOnlinePayment(invoice, vehicleBookingDto.getGatewayType(), vehicleBookingDto.isFullPayment());
//        }
//        log.info("Created booking, id: " + invoice.getVehicleBooking().getId() + " invoice id: {}, poll url: {}", invoice.getId(), invoice.getPollUrl());
//        return booking;
//    }
//
//    public VehicleBookingDto book(VehicleBookingDto vehicleBookingDto) {
//        validateBookingRequest(vehicleBookingDto);
//
//        Vehicle vehicle = vehicleService.getOne(vehicleBookingDto.getVehicleId());
//        Client client = handleClient(vehicleBookingDto);
//
//        vehicleBookingDto.setLocation(vehicle.getLocation());
//        vehicleBookingDto.setClientModel(client);
//
//        Agency agency = vehicle.getAgency();
//        agency.addClient(client);
//        agencyRepository.save(agency);
//
//        VehicleBooking vehicleBooking = vehicle.book(vehicleBookingDto);
//        Promotion promotion = handlePromotion(vehicle, vehicleBooking, vehicleBookingDto);
//
//        Invoice invoice = handleInvoice(vehicleBooking, promotion, vehicleBookingDto);
//        VehicleBooking booking = finalizeBooking(vehicleBooking, invoice, vehicleBookingDto);
//
//        return toVehicleBookingDto.apply(booking);
//    }
//
//
//
//
//
//
//}
