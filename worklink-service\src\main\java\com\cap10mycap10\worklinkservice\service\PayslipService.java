package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.payslip.PayslipResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.model.Payslip;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface PayslipService {

    Payslip addPayslip(Long agencyId, Long workerId, MultipartFile file);

    Page<WorkerResultDto> findAllWorkersPaylipPaged(Long agencyId, PageRequest of);
    Page<WorkerResultDto> searchAllWorkersPaylipPaged(Long agencyId,String query, PageRequest of);

    void deletePayslip(Long id);

    Payslip findById(Long id);

    Page<PayslipResultDto> findWorkerPayslips(Long workerId, PageRequest of);

    Page<Payslip> findAllPaged(PageRequest of);

//    Payslip save(PayslipCreateDto payslipCreateDto);

    Payslip getOne(Long id);

}
