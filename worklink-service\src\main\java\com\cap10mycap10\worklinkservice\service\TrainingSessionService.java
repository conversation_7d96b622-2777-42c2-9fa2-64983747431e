package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.trainingsession.AvailableTraininingsResultsDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionCreateDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionResultDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionUpdateDto;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.TrainingSessionReportStatus;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionAuthorizeDto;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionResultsDto;
import com.cap10mycap10.worklinkservice.enums.TrainingSessionStatus;
import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.cap10mycap10.worklinkservice.model.TrainingSession;
import com.cap10mycap10.worklinkservice.model.WorkerTrainingSession;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;


public interface TrainingSessionService {

    TrainingSessionResultDto addTrainingSession( TrainingSessionCreateDto trainingSessionCreateDto);

    TrainingSessionResultDto findById(Long id);


    Page<TrainingSessionResultDto> findAllPaged(TrainingSessionStatus status, PageRequest of);

    void save(TrainingSessionUpdateDto trainingSessionUpdateDto);

    TrainingSession getOne(Long id);

    void agencyAddWorkersToTrainingSession(Long agencyId, Long trainingSessionId, Set<Long> workerIds) throws Exception;


    void agencyUpdateTrainingSession(TrainingSessionUpdateDto trainingSessionUpdateDto);


    Page<TrainingSessionResultDto>   findForTrainerBilling(Long trainerId ,Long agencyId, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of);

    void cancelTrainingSession(Long agencyId, Long trainingSessionId);

    void cancelTrainingSession(Long trainingSessionId);

    TrainingSessionResultDto findByIdAndAgencyId(Long agencyId, Long trainingSessionId);

    void workerAddSelfToTrainingSession(Long agencyId, Long trainingSessionId, Long workerId) throws Exception;

    Page<TrainingSessionResultDto> findByAgencyId(Long agencyId, PageRequest of);

    Page<TrainingSessionResultDto> findAllByAgencyIdAndTrainingStatus(Long agencyId, TrainingSessionStatus trainingStatus, Long trainingId, LocalDate startDate, LocalDate endDate,Boolean report, PageRequest of);
    Page<TrainingSessionResultDto> findAllByTrainerIdAndTrainingStatus(Long agencyId, TrainingSessionStatus trainingStatus, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of);

    WorkerTrainingSessionResultsDto authorizeWorker(WorkerTrainingSessionAuthorizeDto workerTrainingSessionAuthorizeDto);

    WorkerTrainingSessionResultsDto findWorkerTrainingBookingById(Long id);

    void cancelTrainingBooking(Long id);

    List<WorkerTrainingSessionResultsDto> findWorkersByTrainingSessionId(Long trainingSessionId);

    List<TrainingSessionResultDto> findByWorkerIdAndTrainingId(Long workerId, Long trainingId);



    @Transactional
    void approveOrRejectWorkerBooking(Long bookingId, Boolean approveBooking, Boolean addPayment);

    List<AvailableTraininingsResultsDto> availableTrainingsForWorker(Long workerId, Long trainingId);

    TrainingSession findByBooking (WorkerTrainingSession workerTrainingSession);

    TrainingSessionReportStatus agencySessionsCount(Long agencyId);

    TrainingSessionReportStatus trainerSessionsCount(Long agencyId);
}
