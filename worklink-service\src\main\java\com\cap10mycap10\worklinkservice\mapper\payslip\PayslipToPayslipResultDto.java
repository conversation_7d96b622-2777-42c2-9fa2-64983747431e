package com.cap10mycap10.worklinkservice.mapper.payslip;


import com.cap10mycap10.worklinkservice.dto.payslip.PayslipResultDto;
import com.cap10mycap10.worklinkservice.model.Payslip;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class PayslipToPayslipResultDto implements Converter<Payslip, PayslipResultDto> {

//    private final VehicleToVehicleDto agencyToAgencyResultDto;

//    public PayslipToPayslipResultDto(VehicleToVehicleDto agencyToAgencyResultDto) {
//        this.agencyToAgencyResultDto = agencyToAgencyResultDto;
//    }

    @Override
    public PayslipResultDto convert(Payslip payslip) {
        PayslipResultDto resultDto = new PayslipResultDto();
        resultDto.setId(payslip.getId());
        resultDto.setPayslipPDF(payslip.getPayslipPdf());
        resultDto.setDate(payslip.getDate());
        resultDto.setAgencyId(payslip.getAgency().getId().toString());
        resultDto.setWorkerId(payslip.getWorker().getId().toString());

        return resultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
