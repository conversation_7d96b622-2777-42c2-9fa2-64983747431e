package com.cap10mycap10.worklinkservice.dao.specification;

import com.cap10mycap10.worklinkservice.enums.Operator;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.model.VehicleLocation;
import com.cap10mycap10.worklinkservice.model.Location;
import org.springframework.data.jpa.domain.Specification;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import java.util.List;

public class VehicleSpecifications {

    public static Specification<Vehicle> getSpecification(String field, List<?> values, Operator operator) {
        return (root, query, cb) -> {
            Predicate predicate = null;

            // Special handling for location field due to new many-to-many relationship
            if ("location".equals(field)) {
                return getLocationSpecification(values, operator).toPredicate(root, query, cb);
            }

            switch (operator) {
                case EQUALITY:
                    predicate = cb.equal(root.get(field), values.get(0));
                    break;
                case NON_EQUALITY:
                    predicate = cb.notEqual(root.get(field), values.get(0));
                    break;
                case GREATER_THAN:
                    predicate = cb.greaterThan(root.get(field), (Comparable) values.get(0));
                    break;
                case LESS_THAN:
                    predicate = cb.lessThan(root.get(field), (Comparable) values.get(0));
                    break;
                case GREATER_THAN_OR_EQUAL:
                    predicate = cb.greaterThanOrEqualTo(root.get(field), (Comparable) values.get(0));
                    break;
                case LESS_THAN_OR_EQUAL:
                    predicate = cb.lessThanOrEqualTo(root.get(field), (Comparable) values.get(0));
                    break;
                case CONTAINS:
                    predicate = cb.like(root.get(field), "%" + values.get(0) + "%");
                    break;
                case NOT_CONTAINS:
                    predicate = cb.notLike(root.get(field), "%" + values.get(0) + "%");
                    break;
                case IN:
                    predicate = root.get(field).in(values);
                    break;
                case NOT_IN:
                    predicate = cb.not(root.get(field).in(values));
                    break;
            }

            return predicate;
        };
    }

    /**
     * Special specification for location filtering using the new VehicleLocation junction table
     */
    private static Specification<Vehicle> getLocationSpecification(List<?> values, Operator operator) {
        return (root, query, cb) -> {
            // Join with VehicleLocation and Location entities
            Join<Vehicle, VehicleLocation> vehicleLocationJoin = root.join("vehicleLocations");
            Join<VehicleLocation, Location> locationJoin = vehicleLocationJoin.join("location");

            // Add condition for active vehicle locations
            Predicate activePredicate = cb.equal(vehicleLocationJoin.get("active"), true);

            Predicate locationPredicate = null;

            switch (operator) {
                case EQUALITY:
                    locationPredicate = cb.equal(locationJoin.get("id"), values.get(0));
                    break;
                case NON_EQUALITY:
                    locationPredicate = cb.notEqual(locationJoin.get("id"), values.get(0));
                    break;
                case IN:
                    locationPredicate = locationJoin.get("id").in(values);
                    break;
                case NOT_IN:
                    locationPredicate = cb.not(locationJoin.get("id").in(values));
                    break;
                default:
                    // For other operators, default to IN behavior
                    locationPredicate = locationJoin.get("id").in(values);
                    break;
            }

            return cb.and(activePredicate, locationPredicate);
        };
    }
}