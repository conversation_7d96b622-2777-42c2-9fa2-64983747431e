package com.cap10mycap10.worklinkservice.config;

import com.cap10mycap10.worklinkservice.converter.EncryptedStringConverter;
import com.cap10mycap10.worklinkservice.service.EncryptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * Configuration class to ensure proper dependency injection for JPA converters.
 * This class handles the manual injection of the EncryptionService into the
 * EncryptedStringConverter since JPA converters are not managed by Spring by default.
 */
@Configuration
@Slf4j
public class EncryptionConfiguration {

    @Autowired
    private EncryptionService encryptionService;

    @Autowired
    private EncryptedStringConverter encryptedStringConverter;

    /**
     * Manually inject the EncryptionService into the converter after Spring context initialization.
     * This is necessary because JPA converters are instantiated by the JPA provider,
     * not by Spring, so normal dependency injection doesn't work.
     */
    @PostConstruct
    public void configureConverter() {
        log.info("Configuring EncryptedStringConverter with EncryptionService");
        encryptedStringConverter.setEncryptionService(encryptionService);
        log.info("EncryptedStringConverter configuration completed");
    }
}
