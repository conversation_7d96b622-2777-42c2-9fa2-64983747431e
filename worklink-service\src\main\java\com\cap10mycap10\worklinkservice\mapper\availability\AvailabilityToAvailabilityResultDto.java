package com.cap10mycap10.worklinkservice.mapper.availability;

import com.cap10mycap10.worklinkservice.dto.availability.AvailabilityResultDto;
import com.cap10mycap10.worklinkservice.model.Availability;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AvailabilityToAvailabilityResultDto implements Converter<Availability, AvailabilityResultDto> {
//    AvailabilityToAvailabilityResultDto availabilityToAvailabilityResultDto;
//
//    public AvailabilityToAvailabilityResultDto(AvailabilityToAvailabilityResultDto availabilityToAvailabilityResultDto) {
//        this.availabilityToAvailabilityResultDto = availabilityToAvailabilityResultDto;
//    }


    @Override
    public AvailabilityResultDto convert(Availability availability) {
        AvailabilityResultDto availabilityResultDto = new AvailabilityResultDto();
        availabilityResultDto.setId(availability.getId());
        availabilityResultDto.setReason(availability.getReason());
        availabilityResultDto.setDate(availability.getDate().toString());
        availabilityResultDto.setEndTime(availability.getEndTime().toString());
        availabilityResultDto.setStartTime(availability.getStartTime().toString());
        return availabilityResultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
