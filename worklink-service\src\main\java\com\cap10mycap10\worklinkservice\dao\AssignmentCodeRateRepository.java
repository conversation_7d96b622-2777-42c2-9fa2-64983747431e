package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.AssignmentRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AssignmentCodeRateRepository extends JpaRepository<AssignmentRate, Long> {

    List<AssignmentRate> findByAgent_Id(Long agencyId);
    List<AssignmentRate> findByClient_Id(Long agencyId);

    List<AssignmentRate> findByAgent_IdAndDayOfTheWeek(Long agencyId, String day);



    @Query(value = "select * from assignment_rate\n" +
            "where client_id = ?1\n" +
            "and shift_directorate_id = ?2\n" +
            "and shift_type_id =?3\n" +
            "and day_of_the_week =?4\n" +
            "and assignment_code_id = ?5", nativeQuery = true)
    List<AssignmentRate> findByClientIdAndShiftDirectorateIdAndShiftTypeIdAndDayOfTheWeekAndAssignmentCodeId(Long clientId, Long directorateId, Long shiftTypeId, String dayOfWeek, Long assignmentCode);
}
