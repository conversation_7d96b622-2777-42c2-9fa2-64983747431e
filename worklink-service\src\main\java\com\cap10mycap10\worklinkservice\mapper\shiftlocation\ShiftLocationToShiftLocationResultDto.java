package com.cap10mycap10.worklinkservice.mapper.shiftlocation;

import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationResultDto;
import com.cap10mycap10.worklinkservice.model.Location;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class ShiftLocationToShiftLocationResultDto implements Converter<Location, ShiftLocationResultDto> {
    @Override
    public ShiftLocationResultDto convert(Location location) {
        ShiftLocationResultDto resultDto = new ShiftLocationResultDto();
        resultDto.setId(location.getId());
        resultDto.setCity(location.getCity());
        resultDto.setName(location.getName());
        resultDto.setCityAscii(location.getCityAscii());
        resultDto.setCountry(location.getCountry());
        resultDto.setCapital(location.getCapital());
        resultDto.setLng(location.getLng());
        resultDto.setLat(location.getLat());
        resultDto.setIso2(location.getIso2());
        resultDto.setIso3(location.getIso3());
        resultDto.setName(location.getName());
        resultDto.setAddress(location.getAddress());
        resultDto.setPostalCode(location.getPostalCode());
        resultDto.setState(location.getState());
        resultDto.setDescription(location.getDescription());
        resultDto.setTimeZone(location.getTimeZone());
        resultDto.setLocationType(location.getLocationType() != null ? location.getLocationType().name() : null);
        if (location.getAgency() != null) {
            resultDto.setAgencyId(location.getAgency().getId());
            resultDto.setAgencyName(location.getAgency().getName());
        }
        return resultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
