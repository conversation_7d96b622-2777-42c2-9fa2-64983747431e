package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.dto.file.FileDto;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.shift.ShiftReportStatus;
import com.cap10mycap10.worklinkservice.dto.transport.*;
import com.cap10mycap10.worklinkservice.enums.BookingType;
import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.FileNotFoundException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.mapper.transport.TransportToTransportDto;
import com.cap10mycap10.worklinkservice.mapper.vehiclelog.VehicleLogToVehicleLogDto;
import com.cap10mycap10.worklinkservice.model.TransportWorkerSpec;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.service.*;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.cap10mycap10.worklinkservice.config.AppConfiguration.dateTimeFormat;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Service
@Slf4j
@Transactional
public class TransportServiceImpl implements TransportService {
    @Autowired
    private   DataBucketUtil dataBucketUtil;
    @Autowired
    private EmailService emailService;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private AuthenticationFacadeService authenticationFacadeService;
    @Autowired
    private ShiftDirectorateService directorateService;
    @Autowired
    private ClientRepository clientRepository;
    @Autowired
    private InvoiceRepository invoiceRepository;
    @Autowired
    private   TransportRatingRepository transportRatingRepository;

    @Autowired
    private VehicleLogToVehicleLogDto toVehicleLogDto;
    @Autowired
    private   AssignmentCodeService assignmentCodeService;
    @Autowired
    private   TransportWorkerSpecRepository transportWorkerSpecRepository;
    @Autowired
    private   TransportRepository transportRepository;
    @Autowired
    private   ShiftRepository transportBookingRepository;
    @Autowired
    private  ShiftDirectorateService shiftDirectorateService;
    @Autowired
    private PushNotification pushNotification;
    @Autowired
    private  WorkerService workerService;
    @Autowired
    private AgencyService agencyService;

    @Autowired
    private AgencyRepository agencyRepository;
    @Autowired
    private TransportToTransportDto toTransportResultDto;
    @Autowired
    private ClientService clientService;
    @Autowired
    private VehicleLogRepository vehicleLogRepository;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private VehicleLogService vehicleLogService;


    @Override
    @Transactional
    public TransportDto create(TransportDto transportDto) throws JSONException {
        if(LocalDateTime.now().isAfter(transportDto.getDateTimeRequired()))
            throw new BusinessValidationException("Your secure-transport-request date is already passed");
        Transport transport =  toTransport(transportDto);
        transport = transportRepository.save(transport);
        emailPotentialTransporters(transport);
        if(transport.getTransportStatus()==TransportStatus.BOOKED)
            emailJobAcceptedClient(transport);
        else
            emailJobCreatedClient(transport);

        if(nonNull(transport.getAgency()))
            notifyWorkersNewTransport(transport);
        return toTransportResultDto.apply(transport);
    }

    @Override
    public TransportDto update(TransportDto transportDto) throws JSONException {
        Transport transport = transportRepository.save(toTransport(transportDto));
        emailUpdated(transport);
        return toTransportResultDto.apply(transport);
    }

    @Override
    @Transactional
    public Page<TransportDto> findByDriver(Long driverId, TransportStatus status, PageRequest of) {
        switch (status) {
            case NEW:
                return transportRepository.findDriverNew(driverId,of).map(toTransportResultDto);
            case WAITING:
                return transportRepository.findDriverAwaiting(driverId,of).map(toTransportResultDto);
            default:
                return transportRepository.findDriverClosed(driverId, of).map(toTransportResultDto);
        }
    }

    @Override
    public Page<VehicleLogDto> findByWorkerAndStatus(Long agencyId, LogStatus status, PageRequest of) {
        return vehicleLogRepository.findByWorkerIdAndStatus(agencyId, status,of).map(toVehicleLogDto);
    }

    @Override
    public Page<VehicleLogDto> findLogsByAgencyAndStatus(Long agencyId, LogStatus status, PageRequest of) {
        return vehicleLogRepository.findByVehicleAgencyIdAndStatus(agencyId, status,of).map(toVehicleLogDto);
    }



    @Override
    public Page<TransportDto> findByTeamLeader(Long leaderId, TransportStatus status, PageRequest of) {
        Page<Transport> transport = transportRepository.findByTeamLeaderIdAndTransportStatus(leaderId, status, of);
        return transport.map(toTransportResultDto);
    }

    @Override
    public VehicleLogDto updateMileage(VehicleLogDto vehicleLogDto) {
        Transport transport = getOne(vehicleLogDto.getTransportId());
        VehicleLog vlogq;
        Optional<VehicleLog> vlog = vehicleLogService.getOneByTransportId(vehicleLogDto.getTransportId());
        if(vlog.isPresent()) {
            vlogq = vlog.get();
            vlogq =  vehicleLogRepository.save(vlogq);
        }
        else{
            VehicleLog log = new VehicleLog(transport, workerService.getAuthenticatedWorker());
            transport.setVehicleLog(log);
            vlogq = transportRepository.saveAndFlush(transport).getVehicleLog();
        }

        if(nonNull(vehicleLogDto.getStartMileage()))
            vlogq.setStartMileage(vehicleLogDto.getStartMileage());
        if(nonNull(vehicleLogDto.getEndMileage())) {
            vlogq.setEndMileage(vehicleLogDto.getEndMileage());
            vlogq.setStatus(LogStatus.WAITING_APPROVAL);
        }
        if(nonNull(vehicleLogDto.getFeedback()))
            vlogq.setFeedback(vehicleLogDto.getFeedback());

        return toVehicleLogDto.apply(vehicleLogRepository.save(vlogq));
    }

    @Override
    @Transactional
    public VehicleLogDto updateVehicleCheck(VehicleLogDto vehicleLogDto) {
        Transport transport = getOne(vehicleLogDto.getTransportId());
        VehicleLog vlogq;
        Optional<VehicleLog> vlog = vehicleLogService.getOneByTransportId(vehicleLogDto.getTransportId());
        if(vlog.isPresent()) {
            vlogq = vlog.get();
            vlogq.updateVehicleCheck(vehicleLogDto);
            vlogq =  vehicleLogRepository.save(vlogq);
        } else{
            VehicleLog log = new VehicleLog(transport, workerService.getAuthenticatedWorker());
            log.updateVehicleCheck(vehicleLogDto);
            transport.setVehicleLog(log);
            vlogq = transportRepository.save(transport).getVehicleLog();
        }


        return toVehicleLogDto.apply(vlogq);
    }

    @Override
    public TransportDto teamLeaderUpdate(TransportTeamLeaderUpdateDto transportDto) {

        Transport transport = getOne(transportDto.getId());

        transport.setCashHandover(transportDto.getCashHandover());
        transport.setPDroppedOff(transportDto.getPDroppedOff());
        transport.setPComment(transportDto.getPComment());
        transport.setPfCleanliness(transportDto.getPfCleanliness());
        transport.setPfCourtesy(transportDto.getPfCourtesy());
        transport.setPfKnowledge(transportDto.getPfKnowledge());
        transport.setPfTreatment(transportDto.getPfTreatment());
        transport.setPfAdvice(transportDto.getPfAdvice());
        transport.setPfComfort(transportDto.getPfComfort());
        transport.setPfExperience(transportDto.getPfExperience());

        if(nonNull(transportDto.getMedicationList())) transport.setMedicationItems(String.join(",", transportDto.getMedicationList()));
        if(nonNull(transportDto.getPropertyList()))transport.setPropertyItems(String.join(",", transportDto.getPropertyList()));

        transport.setDropTime(transportDto.getDropTime());
        transport.setPatientRecipient(transportDto.getPatientRecipient());
        transport.setRecipientContact(transportDto.getRecipientContact());
        transport.setRecipientRole(transportDto.getRecipientRole());
        transport.setRecipientSignature(transportDto.getRecipientSignature());
        transport.setNewAddress(transportDto.getNewAddress());
        transport.setNewPostCode(transportDto.getNewPostCode());
        transport.setNewPhone(transportDto.getNewPhone());
        transport.setNewEmail(transportDto.getNewEmail());


        return toTransportResultDto.apply(transportRepository.save(transport));
    }

    @Override
    public VehicleLogDto updateCleaningCheck(VehicleLogDto vehicleLogDto) {
        VehicleLog vlog;
        try {
            vlog = vehicleLogService.getByTransportId(vehicleLogDto.getTransportId());
        }catch (BusinessValidationException e){
            vlog = new VehicleLog(getOne(vehicleLogDto.getTransportId()), workerService.getAuthenticatedWorker());
        }

        vlog.updateCleaningCheck(vehicleLogDto);
        return toVehicleLogDto.apply(vehicleLogRepository.save(vlog));
    }


    @Override
    public TransportDto teamLeaderWorkerTimesUpdate(TransportWorkerTimesDto vehicleLogDto) {
        Transport transport = getOne(vehicleLogDto.getId());
        transport.updateBookingTimes(vehicleLogDto);
        transportRepository.save(transport);
        transport.startJobByForce();
        return toTransportResultDto.apply(transport);
    }


    @Override
    public void cancelJob(Long id) {
        Transport job = getOne(id);
        job.cancelJob();
        emailJobCancelled(job);
    }



    private void emailJobCancelled(Transport transport) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
        String title = "Secure Transport Job Cancelled";
        String body = "The secure transport " +
                " on " + transport.getDateTimeRequired().format(formatter)+". Has been cancelled.\n" +
                "Job Ref: "+transport.getRef();

        List<String> emails = new ArrayList<>();

        if(nonNull(transport.getSemail()))
            emails.add(transport.getSemail());
        if(nonNull(transport.getAgency()))
            emails.add(transport.getAgency().getEmail());
        emails.addAll(transport.getAllWorkers().stream().map(Worker::getEmail).collect(Collectors.toSet()));

        CompletableFuture.runAsync(()-> {emailService.sendEmailAsUserReply(emails, title, body, transport.getAgency().getEmail(), transport.getAgency().getName(),transport.getAgency().getId());});
    }

    private void emailUpdated(Transport transport) {

            String title = "Secure Transport Job Updated";
            String body = "Your secure job transport has been updated.\n" +
                    "Job Ref: "+transport.getRef() ;
        List<String> emails = new ArrayList<>();
        if(nonNull(transport.getAgency())){
            String senderEmail = transport.getAgency().getEmail();
            String senderName =  transport.getAgency().getName();
            if(nonNull(transport.getSemail()))
                CompletableFuture.runAsync(()->emailService.sendEmailAsUserReply(Collections.singletonList(transport.getSemail()), title, body, senderEmail, senderName, transport.getAgency().getId()));
        }else{
            if(nonNull(transport.getSemail()))
                CompletableFuture.runAsync(()-> emailService.sendEmailAsUserReply(Collections.singletonList(transport.getSemail()), title, body, null, null, transport.getAgency().getId()));
        }
    }

    private void emailJobCreatedClient(Transport transport) {
        String body;
        if(nonNull(transport.getAgency()))
            body = "Thank you for requesting secure transport with "+transport.getAgency().getName()+". " +
                    "We are reviewing your job and will be in touch very soon.\n" +
                    "Job Ref: "+transport.getRef();
        else
            body = "Thank you for requesting secure transport. " +
                    "We are reviewing your job and will be in touch very soon.\n" +
                    "Job Ref: "+transport.getRef();

        String bodyFinal = body;
        String title = "Secure Transport Job Request Received";

        if(nonNull(transport.getAgency())){
            String senderEmail = transport.getAgency().getEmail();
            String senderName =  transport.getAgency().getName();
            if(nonNull(transport.getSemail()))
                CompletableFuture.runAsync(()->emailService.sendEmailAsUserReply(Collections.singletonList(transport.getSemail()), title, bodyFinal, senderEmail, senderName, transport.getAgency().getId()));
        }else{
            if(nonNull(transport.getSemail()))
                CompletableFuture.runAsync(()-> emailService.sendEmailAsUserReply(Collections.singletonList(transport.getSemail()), title, bodyFinal, null, null, transport.getAgency().getId()));
        }


    }

    private void emailJobAcceptedClient(Transport transport) {
            String title = "Secure Transport Job Accepted";
            String body = "Thank you for booking a secure transport job with "+transport.getAgency().getName()+"." +
                    " We have accepted your job and will be dispatching our team.\n" +
                    "Job Ref: "+transport.getRef()+"\n\n";
            if(nonNull(transport.getSemail()))
              CompletableFuture.runAsync(()-> emailService.sendEmailAsUserReply(Collections.singletonList(transport.getSemail()), title, body, transport.getAgency().getEmail(), transport.getAgency().getName(), transport.getAgency().getId()));
    }



    private void emailPotentialTransporters(Transport transport) {
        if (!isNull(transport.getAgency())){
            String title = "Transport job booked";
            String body = "Good day,\n A new job was booked. Check job details below.\n" +
                    "Check the job details below.\n" +
                    "Job Ref: "+transport.getRef() +
                    "Date:"+transport.getDateTimeRequired()+"\n" ;
            CompletableFuture.runAsync(()-> emailService.sendEmailAsUserReply(Collections.singletonList(transport.getAgency().getEmail()), title, body, transport.getAgency().getEmail(), transport.getAgency().getName(), transport.getAgency().getId()));
        }else if(!isNull(transport.getAgencies())&& transport.getAgencies().size()>0){
            String title = "New open job posted.";
            String body = "Good day,\n A new job was posted. Check job details and accept if interested.\n\n" +
                    "Check the job details below.\n" +
                    "Job Ref: "+transport.getRef() +
                    "\nDate: "+transport.getDateTimeRequired().format(dateTimeFormat)+"\n";
            List<String> emails = transport.getAgencies().stream().map(Agency::getEmail).collect(Collectors.toList());

            CompletableFuture.runAsync(()-> emailService.sendEmailAsUserReply(emails, title, body, null, null, transport.getAgency().getId()));
        } else{
            String title = "New open job posted.";
            String body = "Good day,\n A new job was posted on MyWorklink. Check job details and apply if interested.\n\n" +
                    "Check the job details below.\n" +
                    "Job Ref: "+transport.getRef() +
                    "\nDate:"+transport.getDateTimeRequired().format(dateTimeFormat)+"\n" ;
            List<AgencyResultDto> agencies = agencyService.findByIsTransporter(true);
            agencies.forEach(s-> CompletableFuture.runAsync(()-> emailService.sendEmailAsUserReply(Collections.singletonList(s.getEmail()), title, body, transport.getAgency().getEmail(), transport.getAgency().getName(), transport.getAgency().getId())));
        }
    }

    @Override
    @Transactional
    public Page<TransportDto> findPaged(PageRequest of, TransportStatus status) {
        Page<Transport> transportPage = transportRepository.findAll(of);
        List<Transport> transportList = transportPage.stream()
                .filter((transport) -> {
            LocalDateTime now = LocalDateTime.now().minusDays(30);
            boolean isClosedAndOld = transport.getTransportStatus()
                    .equals(TransportStatus.CLOSED) &&
                    now.isAfter(transport.getDateTimeRequired());
            return !isClosedAndOld;
        }).collect(Collectors.toList());

        if(status != null)
            transportList = transportList.stream().filter(training -> training.getTransportStatus().toString().equals(status.toString())).collect(Collectors.toList());
        Page<Transport> secureTransportPage = PaginationUtil.paginateTransport(of, transportList);
        return secureTransportPage.map(toTransportResultDto);
    }

    @Override
    @Transactional
    public TransportDto findById(Long id) {
        return toTransportResultDto.apply(getOne(id));
    }

    @Override
    @Transactional
    public Transport getOne(Long id) {
       return transportRepository.findById(id).orElseThrow(()->
                new BusinessValidationException(String.format("There is no secure transport request with id: %s", id)));

    }

    @Override
    public Transport save(Transport transport) {
        return transportRepository.save(transport);
    }

    @Override
    public void uploadRiskDocument(Long transportId, MultipartFile file) {
        Transport transport = getOne(transportId);
        uploadDocument(file, transport, "damageDoc");
    }

    @Override
    @Transactional
    public void authorize(AuthorizeTransportDto authorizeTransportDto) {
        Transport transport = getOne(authorizeTransportDto.getTransportId());
        Client client = clientRepository.findByEmail(transport.getBemail());
        if(nonNull(client)&&!nonNull(transport.getClient())) transport.setClient(client);
        transport.authorize(authorizeTransportDto, true);
        clientRepository.save(transport.getClient());
        invoiceRepository.save(transport.getClientInvoice());
        transportRepository.save(transport);
    }


    @Override
    public VehicleLogDto authorizeVehicleLog(Long logId, String authorizer) {
        VehicleLog transport = vehicleLogService.getOne(logId);
        transport.setStatus(LogStatus.APPROVED);
        transport.setApprovedBy(authenticationFacadeService.getAuthUsername());
        transport.setApprovedByName(authorizer);
        return toVehicleLogDto.apply(vehicleLogService.save(transport));
    }


    @Override
    public VehicleLogDto commentVehicleLog(Long logId, String comment) {
        VehicleLog vehicleLog = vehicleLogService.getOne(logId);
        vehicleLog.setStatus(LogStatus.NEW);
        vehicleLog.setComment(comment);
        vehicleLog.setApprovedBy(authenticationFacadeService.getAuthUsername());
        return toVehicleLogDto.apply(vehicleLogService.save(vehicleLog));
    }

    @Override
    public void beginPendingJobs() {
        List<Transport> transports = transportRepository.findAllByTransportStatusAndDateTimeRequiredLessThan(TransportStatus.PENDING, LocalDateTime.now());
        transports.forEach(Transport::startJob);
        transportRepository.saveAll(transports);
    }

    @Override
    @Transactional
    public ShiftReportStatus findAllAgencyShiftsByStatus(Long agencyId) {
        Page<TransportDto> authShifts = findAgencyJobs(agencyId, PageRequest.of(0, 25), TransportStatus.AUTHORIZED, false,"");
        Page<TransportDto> awaitingShifts = findAgencyJobs(agencyId, PageRequest.of(0, 25), TransportStatus.WAITING, false,"");
        Page<TransportDto> bookedShifts = findAgencyJobs(agencyId, PageRequest.of(0, 25), TransportStatus.BOOKED, false,"");
        Page<TransportDto> pendingShifts = findAgencyJobs(agencyId, PageRequest.of(0, 25), TransportStatus.PENDING, false,"");
        Page<TransportDto> cancelledShifts = findAgencyJobs(agencyId, PageRequest.of(0, 25), TransportStatus.CANCELLED, false,"");
        Page<TransportDto> newShifts = findAgencyJobs(agencyId, PageRequest.of(0, 25), TransportStatus.NEW, false,"");

        ShiftReportStatus shiftReportStatus = new ShiftReportStatus();

        shiftReportStatus.setNewShift(newShifts.getTotalElements());
        shiftReportStatus.setAuthorized(authShifts.getTotalElements());
        shiftReportStatus.setBooked(bookedShifts.getTotalElements());
        shiftReportStatus.setPending(pendingShifts.getTotalElements());
        shiftReportStatus.setCancelled(cancelledShifts.getTotalElements());
        shiftReportStatus.setAwaiting(awaitingShifts.getTotalElements());
        return shiftReportStatus;
    }


    @Override
    @Transactional
    public ShiftReportStatus findAllAgencyLogsShiftsByStatus(Long agencyId) {
        Page<VehicleLogDto> closed = findLogsByAgencyAndStatus(agencyId, LogStatus.APPROVED, PageRequest.of(0, 25));
        Page<VehicleLogDto> awaitingShifts = findLogsByAgencyAndStatus(agencyId, LogStatus.WAITING_APPROVAL, PageRequest.of(0, 25));
        Page<VehicleLogDto> newShifts = findLogsByAgencyAndStatus(agencyId, LogStatus.NEW, PageRequest.of(0, 25));

        ShiftReportStatus shiftReportStatus = new ShiftReportStatus();

        shiftReportStatus.setNewShift(newShifts.getTotalElements());
        shiftReportStatus.setClosed(closed.getTotalElements());
        shiftReportStatus.setAwaiting(awaitingShifts.getTotalElements());
        return shiftReportStatus;
    }




    @Override
    @Transactional
    public ShiftReportStatus findAllClientShiftsByStatus(Long agencyId) {
        Page<TransportDto> authShifts = findByClientId(agencyId, PageRequest.of(0, 10), TransportStatus.AUTHORIZED);
        Page<TransportDto> appliedShifts = findByClientId(agencyId, PageRequest.of(0, 10), TransportStatus.BOOKED);
        Page<TransportDto> awaitingShifts = findByClientId(agencyId, PageRequest.of(0, 10), TransportStatus.WAITING);
        Page<TransportDto> bookedShifts = findByClientId(agencyId, PageRequest.of(0, 10), TransportStatus.BOOKED);
//        Page<TransportResultDto> billedShifts = findByAgencyId(agencyId, PageRequest.of(0, 10), TransportStatus.AUTHORIZED, false);
        Page<TransportDto> cancelledShifts = findByClientId(agencyId, PageRequest.of(0, 10), TransportStatus.CANCELLED);
        Page<TransportDto> newShifts = findByClientId(agencyId, PageRequest.of(0, 10), TransportStatus.NEW);
//        Page<TransportResultDto> queryShifts = findByAgencyId(agencyId, PageRequest.of(0, 10), TransportStatus., false);

        ShiftReportStatus shiftReportStatus = new ShiftReportStatus();

        shiftReportStatus.setNewShift(newShifts.getTotalElements());
        shiftReportStatus.setAuthorized(authShifts.getTotalElements());
//        shiftReportStatus.setQueried(queryShifts.getTotalElements());
        shiftReportStatus.setBooked(bookedShifts.getTotalElements());
//        shiftReportStatus.setBilled(billedShifts.getTotalElements());
        shiftReportStatus.setCancelled(cancelledShifts.getTotalElements());
        shiftReportStatus.setApplied(appliedShifts.getTotalElements());
        shiftReportStatus.setAwaiting(awaitingShifts.getTotalElements());
        return shiftReportStatus;
    }


    @Override
    @Transactional
    public Page<TransportDto> findAgencyJobs(Long agencyId, PageRequest of, TransportStatus status, boolean isFullyBooked, String searchCriteria) {
        if (status.equals(TransportStatus.NEW))
            return transportRepository.findAllByAgenciesAndTransportStatusAndSearch(agencyId, TransportStatus.NEW.toString(), searchCriteria, of).map(toTransportResultDto);
        else
            return transportRepository.findByAgencyAndStatusAndSearch(agencyId, status.toString(),searchCriteria, of).map(toTransportResultDto);
    }


    @Override
    public TransportDto pickVehicle(Long id, Long transportId) {
        Transport transport = getOne(transportId);
        transport.setVehicle(vehicleService.getOne(id));
        return toTransportResultDto.apply(transportRepository.save(transport));
    }

    @Override
    public TransportDto pickTeamLeader(Long id, Long transportId) {
        Transport transport = getOne(transportId);
        transport.setTeamLeader(workerService.getOne(id));
        return toTransportResultDto.apply(transportRepository.save(transport));
    }

    @Override
    public TransportDto pickDriver(Long workerId, Long transportId) {
        Transport transport = getOne(transportId);
        return  toTransportResultDto.apply(transportRepository.save(transport));
    }

    @Override
    public List<LegibleWorkersDto> getEligibleWorkers(Long transportId) {
        Transport transport = getOne(transportId);
        if(!nonNull(transport.getAgency()))
            throw  new BusinessValidationException("No transporter assigned to this job yet");
        Set<Worker> workers  = workerService.getAgencyWorkers(transport.getAgency().getId());
        List<LegibleWorkersDto> legibleWorkersDtos = transport.sortWorkers(workers);

        legibleWorkersDtos.forEach(e->{
            e.getWorkers().forEach(wk->{
                boolean isAvailable = workerService.getWorkerConflicts(wk.getWorkerId(), transport.getDateTimeRequired(), transport.getDateTimeRequired().plusMinutes(10));
                    wk.setAvailable(isAvailable);
            });
        });
        return legibleWorkersDtos;
    }


    @Override
    public TransportDto commitJob(Long transportId) {
        Transport transport = getOne(transportId);
        Client client = clientRepository.findByEmail(transport.getBemail());

        if(nonNull(client)&&!nonNull(transport.getClient())) transport.setClient(client);

        if(!nonNull(client) && !nonNull(transport.getClient()))
            transport.setClient( new Client(transport));


        transport.commitJob();
        transportRepository.saveAndFlush(transport);
        notifyWorkers(transport);
        transport.markNotifiedBookings();
        return  toTransportResultDto.apply(transportRepository.save(transport));
    }

    private void notifyWorkers(Transport transport) {
        transport.getWorkersToNotify().forEach(
                w->{
                    String title = "You have been assigned to a transport job";
                    String body = "Good day "+w.getFirstname()+ "," +
                            "\n You were assigned to a transport job required on "+transport.getDateTimeRequired()+
                            ". Please login and check full details\n\n" +
                            "Check the job details below.\n" +
                            "Date:"+transport.getDateTimeRequired()+"\n" ;
                    CompletableFuture.runAsync(()-> emailService.sendSimpleMessage(w.getEmail(), title, body, transport.getAgency().getId()));

                    CompletableFuture.runAsync(() ->
                            w.getDevices().forEach(d->{
                                try {
                                    pushNotification.sendPushMessage(title,body,d.getFcmToken());
                                } catch (FirebaseMessagingException e) {
                                    log.error("An error occurred while trying to send notification:",e);
                                }
                            })
                    );
                    NotificationCreateDto notificationCreateDto = new NotificationCreateDto(w.getId(), title, body);
                    notificationService.addWorkerNotification(notificationCreateDto);
                }
        );


        // Send team leader email
        if(true){
            String title = "You are the job team leader";
            String body = "Good day\n"+transport.getTeamLeader().getFirstname()+ "," +
                    "You were set as the team lead for the  transport job: "+transport.getRef()+" " +
                    "required on "+transport.getDateTimeRequired()+
                    ".\nUse the following link to update the job information as the team leader\n" +
                    "https://online.myworklink.uk/guest/team-leader/entry/"+transport.getId()+
                    " . Please login and check full details\n\n" +
                    "Check the job details below.\n" +
                    "Date:"+transport.getDateTimeRequired()+"\n" ;
            CompletableFuture.runAsync(()-> emailService.sendSimpleMessage(transport.getTeamLeader().getEmail(), title, body, transport.getAgency().getId()));

            CompletableFuture.runAsync(() ->
                    transport.getTeamLeader().getDevices().forEach(d->{
                        try {
                            pushNotification.sendPushMessage(title,body,d.getFcmToken());
                        } catch (FirebaseMessagingException e) {
                            log.error("An error occurred while trying to send notification:",e);
                        }
                    })
            );
            NotificationCreateDto notificationCreateDto = new NotificationCreateDto(transport.getTeamLeader().getId(), title, body);
            notificationService.addWorkerNotification(notificationCreateDto);
        }


    }
    private void notifyWorkersNewTransport(Transport transport) {
        Set<TransportWorkerSpec> transportWorkerSpecs = transport.getWorkerSpec();
        transport.getAgencies().stream()
                .flatMap(agency -> agency.getWorkers().stream())
                .filter(worker -> isWorkerEligible(worker, transportWorkerSpecs))
                .forEach(worker -> sendNotification(worker, transport));
    }

    private boolean isWorkerEligible(Worker worker, Set<TransportWorkerSpec> transportWorkerSpecs) {
        return transportWorkerSpecs.stream()
                .anyMatch(spec -> spec.getAssignmentCode().getId().longValue() == worker.getAssignmentCode().getId() &&
                        spec.getGender().toString().equals(Gender.NO_PREFERENCE.toString()) ||
                        spec.getGender().toString().equals(worker.getGender().toString()));
    }

    private void sendNotification(Worker worker, Transport transport){
        String title = "New transport job created that you are eligible for";
        String body = "A new transport job was created for "+transport.getDateTimeRequired()+
                ". Please check full details\n\n" ;

        CompletableFuture.runAsync(() ->
                worker.getDevices().forEach(d->{
                    try {
                        pushNotification.sendPushMessage(title,body,d.getFcmToken());
                    } catch (FirebaseMessagingException e) {
                        log.error("An error occurred while trying to send notification:",e);
                    }
                })
        );
        NotificationCreateDto notificationCreateDto = new NotificationCreateDto(worker.getId(), title, body);
        notificationService.addWorkerNotification(notificationCreateDto);

        log.info("Sending Transport Job Notification to worker {}", worker);

    }


    @Override
    @Transactional
    public Page<TransportDto> findByLegibleForAgency(Long agencyId, PageRequest of, TransportStatus status) {
        agencyService.getOne(agencyId);
        List<Transport> transportList = transportRepository.findByAgenciesContain(agencyId);
        transportList = transportList.stream()
                .filter((transport) -> {
                    LocalDateTime now = LocalDateTime.now().minusDays(30);
                    boolean isClosedAndOld = transport.getTransportStatus()
                            .equals(TransportStatus.CLOSED) &&
                            now.isAfter(transport.getDateTimeRequired());
                    return !isClosedAndOld;
                }).collect(Collectors.toList());
        if(status != null )
            transportList = transportList.stream().filter(transport -> transport.getTransportStatus().toString().equals(status.toString())).collect(Collectors.toList());
        Page<Transport> transportPage = PaginationUtil.paginateTransport(of, transportList);
        return transportPage.map(toTransportResultDto);
    }

    @Override
    @Transactional
    public Page<TransportDto> findByClientId(Long clientId, PageRequest of, TransportStatus status) {
        clientService.getOne(clientId);
        List<Transport> transportList = transportRepository.findByClientId(clientId);
        transportList = transportList.stream()
                .filter((transport) -> {
                    LocalDateTime now = LocalDateTime.now().minusDays(30);
                    boolean isClosedAndOld = transport.getTransportStatus()
                            .equals(TransportStatus.CLOSED) &&
                            now.isAfter(transport.getDateTimeRequired());
                    return !isClosedAndOld;
                })
                .collect(Collectors.toList());

        if(status != null){
            transportList = transportList.stream()
                    .filter(training -> training.getTransportStatus().toString().equals(status.toString()))
                    .collect(Collectors.toList());
        }
        Page<Transport> secureTransportPage = PaginationUtil.paginateTransport(of, transportList);

        return secureTransportPage.map(toTransportResultDto);
    }


    @Override
    @Transactional
    public TransportDto acceptJob(Long transportId, Long agencyId) {
        Transport transport = transportRepository.findById(transportId).orElseThrow(
                ()-> new RecordNotFoundException(String.format("Transport with id: %s not found", transportId)));

        if(!Objects.equals(transport.getTransportStatus().toString(), TransportStatus.NEW.toString())){
            throw  new BusinessValidationException(String.format("You cannot accept a booking that is already: %s", transport.getTransportStatus().toString()));
        }
        Agency agency = agencyService.getOne(agencyId);
        if(!transport.getAgencies().contains(agency)){
            throw new BusinessValidationException(String.format("Agency : %s was not selected for this transport job by the client.", agency.getName()));
        }
        transport.setDateTimeBooked(LocalDateTime.now());
        transport.setTransportStatus(TransportStatus.BOOKED);
        transport.setAgency(agency);
        transportRepository.save(transport);

        emailJobAcceptedClient(transport);
        return toTransportResultDto.apply(transport);
    }

    @Override
    public List<BookingResultDto> workerEligibleTransport(Long workerId){
        List<Agency> agencies = agencyRepository.findByWorkerId(workerId);
        Worker worker = workerService.getOne(workerId);

        List<Transport> transportList = new ArrayList<>();
        for(Agency agency: agencies){
            List<Transport> transports = transportRepository.findByAgencyId(agency.getId());
            List<Transport> filteredList = transports.stream()
                    .distinct()
                    .filter(transport -> transport.getAgency().getWorkers().contains(worker))
                    .filter(transport -> transport.getDateTimeRequired().isAfter(LocalDateTime.now()))
                    .filter(booking -> booking.getWorkerSpec().stream()
                            .anyMatch(e -> (e.getGender() == Gender.NO_PREFERENCE || e.getGender() == worker.getGender())
                                    && (Objects.equals(e.getAssignmentCode(), worker.getAssignmentCode().getId()))))
                    .collect(Collectors.toList());
            transportList.addAll(filteredList);
        }
        Set<Transport> transportSet = new HashSet<>(transportList);
        List<Transport> transports = new ArrayList<>(transportSet);

        //Check if worker is in booking, if not let bookingStatus be new
        Iterator<Transport> iterator = transports.iterator();

        // Iterate over the transports
        while (iterator.hasNext()) {
            Transport transport = iterator.next();
            Shift booking = transportBookingRepository.findByWorkerIdAndTransportId(worker.getId(), transport.getId());
            if (booking != null) {
                // If the booking is not null, remove the current transport from the iterator
                iterator.remove();
            } else {
                transport.setTransportStatus(TransportStatus.NEW);
            }
        }

        return transports.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<BookingResultDto> workerEligibleTransport(Long workerId, TransportStatus transportStatus) {
        List<Agency> agencies = agencyRepository.findByWorkerId(workerId);
        Worker worker = workerService.getOne(workerId);

        List<Transport> transportList = new ArrayList<>();
        for(Agency agency: agencies){
            List<Transport> transports = transportRepository.findByAgencyId(agency.getId());
            List<Transport> filteredList = transports.stream()
                    .distinct()
                    .filter(transport -> transport.getAgency().getWorkers().contains(worker))
                    .filter(transport -> transport.getDateTimeRequired().isAfter(LocalDateTime.now()))
                    .filter(booking -> booking.getWorkerSpec().stream()
                            .anyMatch(e -> (e.getGender() == Gender.NO_PREFERENCE || e.getGender() == worker.getGender())
                                    && (Objects.equals(e.getAssignmentCode(), worker.getAssignmentCode().getId()))))
                    .collect(Collectors.toList());
            transportList.addAll(filteredList);
        }
        Set<Transport> transportSet = new HashSet<>(transportList);
        List<Transport> transports = new ArrayList<>(transportSet);

        if(transportStatus != null){
            transports = transports.stream()
                    .filter(transport -> transport.getTransportStatus().toString().equals(transportStatus.toString()))
                    .distinct()
                    .collect(Collectors.toList());
        }
        return transports.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TransportDto transportRating(Long transportId, Byte rating) {
        Transport transport = transportRepository.findById(transportId).orElseThrow(
                ()-> new BusinessValidationException(String.format("No transport with id: %s found", transportId)));

        if(transport.getAgency() == null){
            throw  new BusinessValidationException("Transport job hasn't been accepted by an agency yet");
        }
        if(LocalDateTime.now().isBefore(transport.getDateTimeRequired())){
            throw  new BusinessValidationException("You cannot rate a transport request before it has been done");
        }
        if(rating < 0 || rating > 5){
            throw  new BusinessValidationException("Transport Ratings can only between 0 and 5");
        }
        Rating transportRating = new Rating();
        transportRating.setTransport(transport);
        transportRating.setRating(rating);
        transport.setRating(transportRating);
        transportRatingRepository.save(transportRating);
        return toTransportResultDto.apply(transport);
    }

    @Override
    public void expireUnbookedJobs(){
        List<Transport> transports = transportRepository.findAllByTransportStatusAndDateTimeRequiredLessThan(TransportStatus.NEW,LocalDateTime.now());
        transports.forEach(Transport::expireJob);
        transportRepository.saveAll(transports);
    }

    @Override
    @Transactional
    public void uploadDocument(MultipartFile file, Transport transport, String documentType){

        log.info("Start " + documentType +" uploading service");

        List.of(file).forEach(file1 -> {
            String originalFileName = file1.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            try {
                String contentType = Files.probeContentType(path);
                FileDto fileDto = dataBucketUtil.uploadFile(file, "/t/"+  documentType+ "/" +originalFileName, contentType);

                if (fileDto != null) {
                    if(documentType.equals("patientDocOne")){
                        transport.setPatientDocumentOne(fileDto.getFileUrl());
                        transportRepository.save(transport);
                    }

                    if(documentType.equals("patientDocTwo")){
                        transport.setPatientDocumentTwo(fileDto.getFileUrl());
                        transportRepository.save(transport);
                    }

                    if(documentType.equals("patientDocThree")){
                        transport.setPatientDocumentThree(fileDto.getFileUrl());
                        transportRepository.save(transport);
                    }

                    if(documentType.equals("damageDoc")){
                        transport.setRiskDoc(fileDto.getFileUrl());
                        transportRepository.save(transport);
                    }
                    log.debug("Patient " + documentType +" uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
                }
            } catch (Exception e) {
                log.error("Error occurred while uploading "+ documentType + " ", e);
                throw new FileNotFoundException("Error occurred while uploading "+ documentType);
            }
        });
        log.debug( documentType + " document successfully saved in the database");
    }


    @Override
    @Transactional
    public void uploadDamageReport(MultipartFile file, Long logId){


        VehicleLog vlog = vehicleLogRepository.getOne(logId);



        List.of(file).forEach(file1 -> {
            String originalFileName = file1.getOriginalFilename();
            if(originalFileName == null)
                throw new BusinessValidationException("Original file city is null");
            Path path = new File(originalFileName).toPath();

            try {
                String contentType = Files.probeContentType(path);
                FileDto fileDto = dataBucketUtil.uploadFile(file, "/v/"+logId+"/rp/" +originalFileName, contentType);

                if (fileDto != null) {
                        vlog.setDamageDoc(fileDto.getFileUrl());
                        vehicleLogRepository.save(vlog);
                }
            } catch (Exception e) {
                throw new FileNotFoundException("Error occurred while uploading ");
            }
        });
    }

    @Transactional
    public BookingResultDto convert(Transport transport) {
        BookingResultDto bookingResultDto = new BookingResultDto();
        ShiftDirectorate shiftDirectorate = transport.getPickupLocationDirectorate();

        if(nonNull(shiftDirectorate)){
            bookingResultDto.setDirectorate(shiftDirectorate.getName());
            Location location = shiftDirectorate.getLocation();
            bookingResultDto.setPostCode(shiftDirectorate.getPostCode());
            bookingResultDto.setShiftLocation(location.getCity());
        }

        if (nonNull(transport.getAgency())) {
            bookingResultDto.setAgency(transport.getAgency().getName());
        }

        bookingResultDto.setId(transport.getId());
        bookingResultDto.setPhoneNumber(transport.getDestinationContactNumber());
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        DateTimeFormatter patternTime = DateTimeFormatter.ofPattern("HH:mm");
        bookingResultDto.setStart(transport.getDateTimeRequired());
        bookingResultDto.setEnd(transport.getDateTimeRequired());
//        bookingResultDto.setShiftStartTime(transport.getDateTimeRequired().toLocalTime());
//        bookingResultDto.setShiftEndTime(transport.getDateTimeRequired().toLocalTime());
        bookingResultDto.setBookingType(BookingType.TRANSPORT);

        bookingResultDto.setShiftStatus(transport.getTransportStatus().toString());


        return bookingResultDto;
    }
    private Transport toTransport(TransportDto transportDto) throws JSONException {
        Transport transport;
        if(nonNull(transportDto.getId())) {
            transport = getOne(transportDto.getId());
            for(TransportWorkerSpecDto transportWorkerSpec : transportDto.getTransportWorkerSpecList()){
                transport.updateWorkerSpec(transportWorkerSpec);
            }
        } else {
            transport = new Transport();
            Set<TransportWorkerSpec> transportWorkerSpecList = new HashSet<>();

            for(TransportWorkerSpecDto transportWorkerSpec : transportDto.getTransportWorkerSpecList()){
                if(transportWorkerSpec.getAssignmentCode() == null)
                    throw new BusinessValidationException("Provide an assignment code");
                if(transportWorkerSpec.getGender() ==null)
                    throw new BusinessValidationException("Provide gender for staff list");

                TransportWorkerSpec transportEscorts = new TransportWorkerSpec();
                transportEscorts.setAssignmentCode(assignmentCodeService.getOne(transportWorkerSpec.getAssignmentCode()));
                transportEscorts.setGender(transportWorkerSpec.getGender());
                transportEscorts.setTransport(transport);
                transportEscorts.setNumberOfStaff(transportWorkerSpec.getNumberOfStaff());
            }
        }

        Set<Agency> agencySet = new HashSet<>();
        if(transportDto.getTransportLegibleAgencyIds().size() > 0 ){
            for(Long id: transportDto.getTransportLegibleAgencyIds()){
                Agency agency = agencyService.getOne(id);
                agencySet.add(agency);
            }
        }
        transport.setAgencies(agencySet);

        if(transportDto.getAgencyId() != null ){
            Agency agency = agencyService.getOne(transportDto.getAgencyId());
            transport.setAgency(agency);
            transport.setTransportStatus(TransportStatus.BOOKED);
        }


        if(nonNull(transportDto.getClientId())) {
            Client client = clientService.getOne(transportDto.getClientId());
            transport.setClient(client);
        }
        transport.setPickupLocationContactNumber(transportDto.getPickupLocationContactNumber());
        transport.setDestination(transportDto.getDestination());

        transport.setRapidStatus(transportDto.getRapidStatus());



        JSONObject json = new JSONObject();
        json.put("assaultStaffDesc", transportDto.getAssaultStaffDesc());
        json.put("selfHarmDesc",transportDto.getSelfHarmDesc());
        json.put("physicalAggressionDesc",transportDto.getPhysicalAggressionDesc());
        json.put("mentalHealthStatus",transportDto.getMentalHealthStatus());
        json.put("verballyAggressiveDesc",transportDto.getVerballyAggressiveDesc());
        json.put("absconsionRiskDesc",transportDto.getAbsconsionRiskDesc());
        json.put("genderIssuesDesc",transportDto.getGenderIssuesDesc());
        json.put("racialIssuesDesc",transportDto.getRacialIssuesDesc());
        json.put("sexuallyInappropriateDesc",transportDto.getSexuallyInappropriateDesc());
        json.put("selfNeglectDesc",transportDto.getSelfNeglectDesc());

        transport.setRiskDescriptions(String.valueOf(json));

        transport.setSelfNeglect(transportDto.getSelfNeglect());

        transport.setDestinationPostCode(transportDto.getDestinationPostCode());
        transport.setDestinationContactNumber(transportDto.getDestinationContactNumber());
        transport.setDateTimeRequired(transportDto.getDateTimeRequired());
        transport.setBpostCode(transportDto.getBpostCode());
        transport.setPickupPostCode(transportDto.getPickupPostCode());
        transport.setWardEscort(transportDto.getWardEscort());
        transport.setRacialIssues(transportDto.getRacialIssues());
        transport.setPassengerGender(transportDto.getPassengerGender());
        transport.setAssaultStaff(transportDto.getAssaultStaff());
        transport.setPhysicalAggression(transportDto.getPhysicalAggression());
        transport.setIsPassengerAwareOfTransport(transportDto.getIsPassengerAwareOfTransport());
        transport.setVerballyAggressive(transportDto.getVerballyAggressive());
        transport.setSelfHarm(transportDto.getSelfHarm());
        transport.setAbsconsionRisk(transportDto.getAbsconsionRisk());
        transport.setSexuallyInappropriate(transportDto.getSexuallyInappropriate());
        transport.setOtherRisks(transportDto.getOtherRisks());
        transport.setPassengerRequiresRestraints(transportDto.getPassengerRequiresRestraints());
        transport.setReasonsForRestrains(transportDto.getReasonsForRestrains());
        transport.setTransportReason(transportDto.getReasonForTransport());
        transport.setPatientName(transportDto.getPatientName());
        transport.setSpecialRequests(transportDto.getSpecialRequests());
        transport.setMha(transportDto.getMha());
        transport.setPcaddress(transportDto.getPcaddress());
        transport.setPcemail(transportDto.getPcemail());
        transport.setPcbusiness(transportDto.getPcbusiness());
        transport.setPward(transportDto.getPward());
        transport.setPname(transportDto.getPname());
        transport.setPdob(transportDto.getPdob());
        transport.setNhs(transportDto.getNhs());
        transport.setDiagnosis(transportDto.getDiagnosis());
        transport.setDname(transportDto.getDname());
        transport.setDbusiness(transportDto.getDbusiness());
        transport.setDward(transportDto.getDward());
        transport.setDcontact(transportDto.getDcontact());
        transport.setDemail(transportDto.getDemail());
        transport.setGenderIssues(transportDto.getGenderIssues());
        transport.setRacialIssues(transportDto.getRacialIssues());
        transport.setMedication(transportDto.getMedication());
        transport.setPhysicalHealth(transportDto.getPhysicalHealth());
        transport.setRapidTranq(transportDto.getRapidTranq());
        transport.setInfectionControl(transportDto.getInfectionControl());
        transport.setCovid(transportDto.getCovid());
        transport.setOfferFood(transportDto.getOfferFood());
        transport.setAllergies(transportDto.getAllergies());
        transport.setSubmittedBy(transportDto.getSubmittedBy());
        transport.setSemail(transportDto.getSemail());
        transport.setSphone(transportDto.getSphone());
        transport.setCanOfferFood(transportDto.getCanOfferFood());
        transport.setPOrderNum(transportDto.getPOrderNum());
        transport.setSbsCode(transportDto.getSbsCode());
        transport.setBname(transportDto.getBname());
        transport.setBaddress(transportDto.getBaddress());
        transport.setBinvoice(transportDto.getBinvoice());
        transport.setBphone(transportDto.getBphone());
        transport.setPmeds(transportDto.getPmeds());
        transport.setBemail(transportDto.getBemail());
        transport.setAuthority(transportDto.getAuthority());

        transport.setWalk(transportDto.getWalk());
        transport.setWalkInfo(transportDto.getWalkInfo());




        if(transportDto.getRiskDocFile() != null)
            uploadDocument(transportDto.getRiskDocFile(), transport, "damageDoc");
        if(transportDto.getMultipartPatientDocumentOne() != null)
            uploadDocument(transportDto.getMultipartPatientDocumentOne(), transport, "patientDocOne");
        if(transportDto.getMultipartPatientDocumentTwo() != null)
            uploadDocument(transportDto.getMultipartPatientDocumentTwo(), transport, "patientDocTwo");
        if(transportDto.getMultipartPatientDocumentThree() != null)
            uploadDocument(transportDto.getMultipartPatientDocumentThree(), transport, "patientDocThree");
        if(nonNull(transportDto.getPickupDirectorateId()))
            transport.setPickupLocationDirectorate(directorateService.getOne(transportDto.getPickupDirectorateId()));

        return  transport;
    }



}
