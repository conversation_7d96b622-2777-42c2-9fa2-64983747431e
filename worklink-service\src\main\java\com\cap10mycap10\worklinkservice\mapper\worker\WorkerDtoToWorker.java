package com.cap10mycap10.worklinkservice.mapper.worker;


import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerCreateDto;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.AssignmentCodeService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class WorkerDtoToWorker implements Converter<WorkerCreateDto, Worker> {

    private final AssignmentCodeService codeService;
    private final AuthenticationFacade authenticationFacade;

    public WorkerDtoToWorker(AssignmentCodeService codeService, AuthenticationFacade authenticationFacade) {

        this.codeService = codeService;
        this.authenticationFacade = authenticationFacade;
    }

    @Override
    public Worker convert(WorkerCreateDto workerCreateDto) {
        Worker worker = new Worker();
        worker.setFirstname(workerCreateDto.getFirstname());
        worker.setLastname(workerCreateDto.getLastname());
        worker.setEmail(workerCreateDto.getEmail());
        worker.setGender(workerCreateDto.getGender());
        worker.setEmploymentNumber(workerCreateDto.getEmploymentNumber());
        worker.setNationality(workerCreateDto.getNationality());
        worker.setCv(workerCreateDto.getCv());
        worker.setDeputyId(workerCreateDto.getDeputyId());
        worker.setDob(workerCreateDto.getDob());
        worker.setPhoneNumber(workerCreateDto.getPhoneNumber());
        worker.setPostcode(workerCreateDto.getPostcode());
        worker.setAddress(workerCreateDto.getAddress());
        worker.setProfilePic(workerCreateDto.getProfilePic());
        worker.setUsername(workerCreateDto.getUsername());
        worker.setAssignmentCode(codeService.getOne(workerCreateDto.getAssignmentCode()));
        // Worker may be created by system
//        worker.setCreatedBy(authenticationFacade.getAuthentication().getCity());
        return worker;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
