package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.dto.deputy.DeputyTimesheetRespDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import javax.swing.text.html.parser.ContentModel;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.Principal;
import java.util.*;


@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class DeputyController {

    @PostMapping(value = "deputy/shift")
    public ResponseEntity<Object> create(ContentModel request, HttpServletResponse response) throws ServletException, IOException, JSONException {

//        log.info("Deputy webhook sent us post data: \n{}",request.getContentLength());


        log.info("Deputy webhook sent us post data: \n{}",request);
        JSONObject requestBody = doPost( request,  response);
        log.info("Deputy webhook sent us post data: \n{}",requestBody);

        return ResponseEntity.ok("Data was received");
    }


    public JSONObject doPost(ContentModel request, HttpServletResponse response) throws ServletException, IOException, JSONException {
        StringBuffer jb = new StringBuffer();
        String line = null;

//        try {
//        String reader = request.getParameterMap().toString();
//        String reader2 = request.getParameterMap().entrySet().toString();
//        Map<String, String[]> reader3 = request.getParameterMap();
//            while ((line = reader.readLine()) != null) {
//                jb.append(line);
//            }
//        } catch (Exception e) {
//            // Handle the error (e.g., log it or return an error response)
//        }

//        try {
        log.info("Deputy webhook sent us post data: \n{}", jb);

        JSONObject jsonObject = new JSONObject("reader3");
        return jsonObject;
        // Work with the data using methods like:
        // int someInt = jsonObject.getInt("intParamName");
        // String someString = jsonObject.getString("stringParamName");
        // JSONObject nestedObj = jsonObject.getJSONObject("nestedObjName");
        // JSONArray arr = jsonObject.getJSONArray("arrayParamName");
        // etc.
//        } catch (JSONException e) {
//            // Handle the error (e.g., log it or return an error response)
//            throw new IOException("Error parsing JSON request string");
//        }

        // Continue processing the request as needed
    }




}
