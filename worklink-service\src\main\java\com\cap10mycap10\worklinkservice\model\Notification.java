package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;


@EqualsAndHashCode(callSuper = true)
@Entity
@AllArgsConstructor
@Data

public class Notification extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String title;

    @Column(length = 1000)
    private String body;
    private String file;

    @Enumerated(EnumType.STRING)
    private WorklinkUserType senderType;
    @Enumerated(EnumType.STRING)
    private WorklinkUserType recipientType;
    private Long senderId;

    @JsonIgnore
    @ManyToOne
    private AssignmentCode recipientAssCode;

    private boolean sendToAll;


    private String token;
    private LocalDateTime sentTime = LocalDateTime.now();

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    private Worker worker;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    private Agency senderAgency;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    private Client senderClient;

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @ManyToMany(fetch = FetchType.LAZY, cascade = {
            CascadeType.ALL,
    })
    @JoinTable(name = "notification_agency",
            joinColumns = @JoinColumn(name = "notification_id"),
            inverseJoinColumns = @JoinColumn(name = "agency_id")
    )
    private Set<Agency> agencys = new HashSet<>();

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @ManyToMany(fetch = FetchType.LAZY, cascade = {
            CascadeType.ALL,
    })
    @JoinTable(name = "client_agency",
            joinColumns = @JoinColumn(name = "client_id"),
            inverseJoinColumns = @JoinColumn(name = "agency_id")
    )
    private Set<Client> clients = new HashSet<>();

    public Notification() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Worker getWorker() {
        return worker;
    }

    public void setWorker(Worker worker) {
        this.worker = worker;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Notification{");
        sb.append("id=").append(id);
        sb.append(", title='").append(title).append('\'');
        sb.append(", body='").append(body).append('\'');
        sb.append(", token='").append(token).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
