package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.VehicleLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VehicleLocationRepository extends JpaRepository<VehicleLocation, Long> {

    /**
     * Find all vehicle locations for a specific vehicle
     */
    List<VehicleLocation> findByVehicleId(Long vehicleId);

    /**
     * Find all active vehicle locations for a specific vehicle
     */
    List<VehicleLocation> findByVehicleIdAndActiveTrue(Long vehicleId);

    /**
     * Find all vehicle locations for a specific location
     */
    List<VehicleLocation> findByLocationId(Long locationId);

    /**
     * Find all active vehicle locations for a specific location
     */
    List<VehicleLocation> findByLocationIdAndActiveTrue(Long locationId);

    /**
     * Find a specific vehicle-location relationship
     */
    Optional<VehicleLocation> findByVehicleIdAndLocationId(Long vehicleId, Long locationId);

    /**
     * Find active vehicle-location relationship
     */
    Optional<VehicleLocation> findByVehicleIdAndLocationIdAndActiveTrue(Long vehicleId, Long locationId);

    /**
     * Check if a vehicle is assigned to a specific location
     */
    boolean existsByVehicleIdAndLocationId(Long vehicleId, Long locationId);

    /**
     * Check if a vehicle is actively assigned to a specific location
     */
    boolean existsByVehicleIdAndLocationIdAndActiveTrue(Long vehicleId, Long locationId);

    /**
     * Delete all vehicle locations for a specific vehicle
     */
    void deleteByVehicleId(Long vehicleId);

    /**
     * Delete a specific vehicle-location relationship
     */
    void deleteByVehicleIdAndLocationId(Long vehicleId, Long locationId);

    /**
     * Get all vehicle IDs for a specific location
     */
    @Query("SELECT vl.vehicle.id FROM VehicleLocation vl WHERE vl.location.id = :locationId AND vl.active = true")
    List<Long> findActiveVehicleIdsByLocationId(@Param("locationId") Long locationId);

    /**
     * Get all location IDs for a specific vehicle
     */
    @Query("SELECT vl.location.id FROM VehicleLocation vl WHERE vl.vehicle.id = :vehicleId AND vl.active = true")
    List<Long> findActiveLocationIdsByVehicleId(@Param("vehicleId") Long vehicleId);
}
