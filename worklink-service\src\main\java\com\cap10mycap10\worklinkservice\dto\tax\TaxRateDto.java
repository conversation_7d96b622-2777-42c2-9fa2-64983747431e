package com.cap10mycap10.worklinkservice.dto.tax;

import lombok.Data;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class TaxRateDto {

    private Long id;

    @NotBlank(message = "Tax rate name is required")
    private String name;

    @NotNull(message = "Tax percentage is required")
    @DecimalMin(value = "0.00", message = "Tax percentage must be at least 0%")
    @DecimalMax(value = "100.00", message = "Tax percentage cannot exceed 100%")
    private BigDecimal percentage;

    private String description;

    private boolean active = true;

    @NotNull(message = "Agency ID is required")
    private Long agencyId;
}
