package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.BankRepository;
import com.cap10mycap10.worklinkservice.dto.bank.BankCreateDto;
import com.cap10mycap10.worklinkservice.dto.bank.BankResultDto;
import com.cap10mycap10.worklinkservice.dto.bank.BankUpdateDto;
import com.cap10mycap10.worklinkservice.dto.file.FileDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.model.Bank;
import com.cap10mycap10.worklinkservice.service.BankService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static java.time.LocalDateTime.now;

//import static java.time.Instant.now;

//import static java.time.LocalDateTime.now;


@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class BankServiceImpl implements BankService {

    private final BankRepository bankRepository;
    private final DataBucketUtil dataBucketUtil;
    @Autowired
    private WorkerService workerService;

    public BankServiceImpl(BankRepository bankRepository, DataBucketUtil dataBucketUtil) {
        this.bankRepository = bankRepository;
        this.dataBucketUtil = dataBucketUtil;
    }


    @Override
    public Bank save(BankCreateDto bankCreateDto) {
        Bank bank = bankRepository.findByWorkerId(bankCreateDto.getWorkerId());
        if(bank==null){
            bank = new Bank();
        }

        bank.setBank(bankCreateDto.getBank());
        bank.setName(bankCreateDto.getName());
        bank.setCode(bankCreateDto.getCode());
        bank.setLastModifiedDate(now());

        bank.setAccount(bankCreateDto.getAccount());
        bank.setSignDate(bankCreateDto.getSignDate());
        bank.setFullname(bankCreateDto.getFullname());
        bank.setWorker(workerService.getOne(bankCreateDto.getWorkerId()));
        bankRepository.save(bank);

        return bank;
    }

    @Override
    public BankResultDto findById(Long id) {
        return null;
    }

    @Override
    public BankResultDto findByWorkerId(Long id) {
        Bank bank = bankRepository.findByWorkerId(id);
        if(bank==null){
            return null;
        }
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        if(bank.getLastModifiedDate()!=null)bank.setLastModifiedBy(bank.getLastModifiedDate().format(pattern));

        return convert(bank);
    }

    @Override
    public Bank saveUpdate(BankUpdateDto bankCreateDto) {
        Bank bank = getOne(bankCreateDto.getId());

        bank.setBank(bankCreateDto.getBank());
        bank.setName(bankCreateDto.getName());
        bank.setCode(bankCreateDto.getCode());
        bank.setAccount(bankCreateDto.getAccount());
        bank.setSignDate(bankCreateDto.getSignDate());
        bank.setFullname(bankCreateDto.getFullname());
        bank.setWorker(workerService.getOne(bankCreateDto.getWorkerId()));


        return bankRepository.save(bank);
    }



    @Override
    public void deleteById(Long id) {
        Bank bank = getOne(id);
        try {
            bankRepository.deleteById(id);
            bankRepository.flush();
        }catch (Exception ex){
            throw new BusinessValidationException("Bank cannot be deleted");
        }
    }


    @Override
    public Bank getOne(Long id) {
        return bankRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Bank not found"));
    }


    @Override
    public void addGeneralSignature(Long workerId, MultipartFile files){


        log.info("Start file uploading service");
        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

//            try {
            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId +"/"+originalFileName, contentType);

            if (fileDto != null) {
                Bank bank = bankRepository.findByWorkerId(workerId);
                if(bank==null){
                    bank = new Bank();
                    bank.setWorker(workerService.getOne(workerId));
                }


                bank.setSigned(fileDto.getFileUrl());

                bankRepository.save(bank);
//                    inputFiles.add(new InputFile(fileDto.getFileName(), fileDto.getFileUrl()));
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
//            } catch (Exception e) {
//                log.error("Error occurred while uploading. Error: ", e);
//
//                    throw new FileNotFoundException("Error occurred while uploading");
//
//            }
        });

//        fileRepository.saveAll(inputFiles);
        log.debug("File details successfully saved in the database");





    }

    private BankResultDto convert(Bank hmrc) {

        BankResultDto bankResultDto = new BankResultDto();


        bankResultDto.setId(hmrc.getId());
        bankResultDto.setBank(hmrc.getBank());
        bankResultDto.setName(hmrc.getName());
        bankResultDto.setCode(hmrc.getCode());
        bankResultDto.setAccount(hmrc.getAccount());
        bankResultDto.setSignDate(hmrc.getSignDate());
        bankResultDto.setSigned(hmrc.getSigned());
        bankResultDto.setFullname(hmrc.getFullname());


        bankResultDto.setLastModifiedDate(hmrc.getLastModifiedBy());
        bankResultDto.setWorkerId(hmrc.getWorker().getId());



        return bankResultDto;
    }




}
