package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.dto.note.NoteCreateDto;
import com.cap10mycap10.worklinkservice.dto.note.NoteUpdateDto;
import com.cap10mycap10.worklinkservice.enums.AssetType;
import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.Note;
import com.cap10mycap10.worklinkservice.model.VehicleLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Optional;
import java.util.Set;

public interface VehicleLogService {

    VehicleLog getOne(Long id);
    VehicleLog save(VehicleLog id);

    VehicleLog getByTransportId(Long id) throws BusinessValidationException;

    Optional<VehicleLog> getOneByTransportId(Long id);

    Page<VehicleLogDto> findForVehicle(Long id, LogStatus status, PageRequest of);
}
