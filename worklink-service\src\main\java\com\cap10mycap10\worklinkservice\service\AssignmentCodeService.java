package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dao.AssignmentCodeRepository;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeCreateDto;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeResultDto;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeUpdateDto;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface AssignmentCodeService {
    AssignmentCodeResultDto save(AssignmentCodeCreateDto assignmentCodeCreateDto);

    AssignmentCodeResultDto save(AssignmentCodeUpdateDto assignmentCodeUpdateDto);

    AssignmentCodeResultDto findById(Long id);

    List<AssignmentCodeResultDto> findAll();

    Page<AssignmentCodeResultDto> findAllPaged(PageRequest of);

    void deleteById(Long id);

    AssignmentCode getOne(Long id);


    List<AssignmentCodeResultDto> findByAgencyId(Long id);

    List<AssignmentCodeResultDto> findByClientId(Long id);

    AssignmentCodeResultDto findByCode(String code);
}
