package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.dto.shift.ShiftReportStatus;
import com.cap10mycap10.worklinkservice.dto.trainingsession.AvailableTraininingsResultsDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionCreateDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionResultDto;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionUpdateDto;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.TrainingSessionReportStatus;
import com.cap10mycap10.worklinkservice.enums.TrainingSessionStatus;
import com.cap10mycap10.worklinkservice.service.TrainingSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class TrainingSessionController {

    @Autowired
    private TrainingSessionService trainingSessionService;
    public static final String ROLE_AGENCY = "ROLE_AGENCY_ADMIN";

    @PostMapping(value = "training-session", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TrainingSessionResultDto> create( @RequestBody TrainingSessionCreateDto trainingSessionCreateDto){
        log.info("Request to add training session with : {}", trainingSessionCreateDto);
        return ResponseEntity.ok(trainingSessionService.addTrainingSession(trainingSessionCreateDto));
    }


    @GetMapping(value = "training-session/{id}")
    public ResponseEntity<TrainingSessionResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get training session with id : {}", id);
        return ResponseEntity.ok(trainingSessionService.findById(id));
    }


    @GetMapping(value = "training-sessions/{status}/{page}/{size}")
    public ResponseEntity<Page<TrainingSessionResultDto>> findAllPaged(
            @PathVariable("page") int page,
            @PathVariable("status") TrainingSessionStatus status,
                                                       @PathVariable("size") int size) {
        log.info("Request to get paged training sessions : {}, {}", page, size);
        return ResponseEntity.ok(trainingSessionService.findAllPaged(status, PageRequest.of(page, size)));
    }


    @PutMapping(value = "training-session", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> update( @RequestBody TrainingSessionUpdateDto trainingSessionUpdateDto) {
        log.info("Request to update training session with : {}",  trainingSessionUpdateDto);
        trainingSessionService.agencyUpdateTrainingSession(trainingSessionUpdateDto);
        return ResponseEntity.ok().build();
    }


    @DeleteMapping(value = "training-session/{id}")
    public ResponseEntity<Object> cancel(@PathVariable("id") Long id){
        log.info("Request to cancel a training session for trainingSessionID: {}", id );
        trainingSessionService.cancelTrainingSession( id);
        return ResponseEntity.noContent().build();
    }


    @GetMapping(value = "training-sessions/worker/trainings/{workerId}")
    public ResponseEntity<List<AvailableTraininingsResultsDto>> availableTrainingsForWorker(@PathVariable("workerId") Long workerId,
                                                                                             @RequestParam(required = false) Long trainingId)
    {
        log.info("Request to view list of trainings by workerId and optional TrainingId of : {} {}", workerId, trainingId);

        return ResponseEntity.ok(trainingSessionService.availableTrainingsForWorker(workerId, trainingId));
    }



    @GetMapping(value = "training-sessions/agency/{agencyId}/{status}/{page}/{size}")
    public ResponseEntity<Page<TrainingSessionResultDto>> findForAgency(@PathVariable("page") int page,
                                                                          @PathVariable("size") int size,
                                                                          @PathVariable("agencyId") Long agencyId,
                                                                          @PathVariable("status") TrainingSessionStatus status,
                                                                          @RequestParam(required = false) Long trainingId,
                                                                          @RequestParam(required = false) Boolean reports,
                                                                          @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                          @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to view paged  training sessions by status : {}, {}, {}, {}", page, size, agencyId, status);

        return ResponseEntity.ok(trainingSessionService.findAllByAgencyIdAndTrainingStatus(agencyId, status, trainingId,  startDate, endDate, reports, PageRequest.of(page, size)));
    }

    @GetMapping(value = "training-sessions/trainer/{agencyId}/{status}/{page}/{size}")
    public ResponseEntity<Page<TrainingSessionResultDto>> findForTrainer(@PathVariable("page") int page,
                                                                          @PathVariable("size") int size,
                                                                          @PathVariable("agencyId") Long agencyId,
                                                                          @PathVariable("status") TrainingSessionStatus status,
                                                                          @RequestParam(required = false) Long trainingId,
                                                                          @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                          @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to view paged  training sessions by status : {}, {}, {}, {}", page, size, agencyId, status);

        return ResponseEntity.ok(trainingSessionService.findAllByTrainerIdAndTrainingStatus(agencyId, status, trainingId,  startDate, endDate, PageRequest.of(page, size)));
    }



    @GetMapping(value = "training-sessions/trainer/billing/{trainerId}/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<TrainingSessionResultDto>> findForTrainerBilling(@PathVariable("page") int page,
                                                                          @PathVariable("size") int size,
                                                                          @PathVariable("agencyId") Long agencyId,
                                                                          @PathVariable("trainerId") Long trainerId,
                                                                          @RequestParam(required = false) Long trainingId,
                                                                          @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                          @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to view paged  training sessions by status : {}, {}, {}", page, size, agencyId);

        return ResponseEntity.ok(trainingSessionService.findForTrainerBilling(trainerId, agencyId, trainingId,  startDate, endDate, PageRequest.of(page, size)));
    }

    @GetMapping(value = "training-sessions/worker/{workerId}/{trainingId}")
    public ResponseEntity<List<TrainingSessionResultDto>> findForWorker(@PathVariable("workerId") Long workerId,
                                                                        @PathVariable("trainingId") Long trainingId) {
        log.info("Request to view list of training sessions by workerId and trainingId : {}, {}", workerId, trainingId);
        return ResponseEntity.ok(trainingSessionService.findByWorkerIdAndTrainingId(workerId, trainingId));
    }


    @GetMapping(value = "training-session/trainer/report/{trainerId}")
    public ResponseEntity<TrainingSessionReportStatus> findTrainerSessionCount(
            @PathVariable("trainerId") Long trainerId
    ) {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(trainingSessionService.trainerSessionsCount(trainerId));
    }

    @GetMapping(value = "training-session/agency/report/{agencyId}")
    public ResponseEntity<TrainingSessionReportStatus> findAgencySessionCount(
            @PathVariable("agencyId") Long agencyId
    ) {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(trainingSessionService.agencySessionsCount(agencyId));
    }



}
