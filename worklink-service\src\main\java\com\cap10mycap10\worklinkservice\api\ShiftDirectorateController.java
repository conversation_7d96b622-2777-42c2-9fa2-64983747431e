package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.DirectorateInformation;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateCreateDto;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateResultDto;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateUpdateDto;
import com.cap10mycap10.worklinkservice.service.ShiftDirectorateService;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;
import java.time.LocalTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ShiftDirectorateController {


    private final ShiftDirectorateService shiftDirectorateService;

    public ShiftDirectorateController(final ShiftDirectorateService shiftDirectorateService) {
        this.shiftDirectorateService = shiftDirectorateService;
    }

    /* @CreateShiftDirectorate*/
    @PostMapping(value = "shift-directorate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ShiftDirectorateResultDto> create(@RequestBody ShiftDirectorateCreateDto shiftDirectorateCreateDto) {
        log.info("Request to add shift-directorate with : {}", shiftDirectorateCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(shiftDirectorateService.save(shiftDirectorateCreateDto));
    }

    /*@ViewShiftDirectorate*/
    @GetMapping(value = "shift-directorate/{id}")
    public ResponseEntity<ShiftDirectorateResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get shift-directorate with id : {}", id);
        return ResponseEntity.ok(shiftDirectorateService.findById(id));
    }

    /*@ViewShiftDirectorate*/
    @GetMapping(value = "shift-directorate-by-location/{id}")
    public ResponseEntity<List<ShiftDirectorateResultDto>> findDirectorateByLocationById(@PathVariable("id") Long id) {
        log.info("Request to get shift-directorate with id : {}", id);
        return ResponseEntity.ok(shiftDirectorateService.findByLocationId(id));
    }

    /*@ViewShiftDirectorate*/
//    @GetMapping(value = "shift-directorates")
//    public ResponseEntity<List<ShiftDirectorateResultDto>> findById() {
//        log.info("Request to get all  shift-directorates");
//        return ResponseEntity.ok(shiftDirectorateService.findAll());
//    }

    @GetMapping(value = "shift-directorates/filter/")
    public ResponseEntity<List<ShiftDirectorateResultDto>> findByAllWithFilter(@RequestParam(required = false) String location) {
        log.info("Request to get all  shift-directorates and filter by: {} ", location);
        return ResponseEntity.ok(shiftDirectorateService.findAllWithFilter(location));
    }

    /* @ViewShiftDirectorate*/
    @GetMapping(value = "shift-directorates/{page}/{size}")
    public ResponseEntity<Page<ShiftDirectorateResultDto>> findAll(@PathVariable("page") int page,
                                                                    @RequestParam(value = "searchCriteria", required = false) String searchCriteria,
                                                                    @RequestParam(value = "locationId", required = false) Long locationId,
                                                                    @PathVariable("size") int size) {
        log.info("Request to get paged shift-directorates with : {}, {}", page, size);
        return ResponseEntity.ok(shiftDirectorateService.findAllPaged(searchCriteria, locationId, PageRequest.of(page, size)));
    }

    @GetMapping(value = "shift-directorates-client/{payerId}/{page}/{size}")
    public ResponseEntity<Page<ShiftDirectorateResultDto>> findByClientId(@PathVariable("payerId") Long clientId,
                                                                          @PathVariable("page") int page,
                                                                          @PathVariable("size") int size,
                                                                          @RequestParam(value = "searchCriteria", required = false) String searchCriteria) {
        log.info("Request to get paged shift-directorates with : {}, {} and search criteria of {}", page, size, searchCriteria);
        return ResponseEntity.ok(shiftDirectorateService.findAllPagedByClient(clientId, searchCriteria ,PageRequest.of(page, size)));
    }


    @GetMapping(value = "shift-directorates-agency/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<ShiftDirectorateResultDto>> findByAgencyId(@PathVariable("agencyId") Long agencyId,
                                                                       @PathVariable("page") int page,
                                                                       @PathVariable("size") int size,
                                                                      @RequestParam(value = "searchCriteria", required = false) String searchCriteria) {
        log.info("Request to get paged shift-directorates with : {}, {} and search criteria of {}", page, size, searchCriteria);
        return ResponseEntity.ok(shiftDirectorateService.findAllPagedByAgency(agencyId, searchCriteria, PageRequest.of(page, size)));
    }



    /* @UpdateShiftDirectorate*/
    @PutMapping(value = "shift-directorate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ShiftDirectorateResultDto> update(@RequestBody ShiftDirectorateUpdateDto shiftDirectorateUpdateDto) {
        log.info("Request to update shift-directorate with : {}", shiftDirectorateUpdateDto);
        return ResponseEntity.ok(shiftDirectorateService.save(shiftDirectorateUpdateDto));
    }

    /*@DeleteShiftDirectorate*/
    @DeleteMapping(value = "shift-directorate/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to edit shift-directorate with id  : {}", id);
        shiftDirectorateService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
