package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.dto.transport.TransportWorkerTimesDto;
import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;


@Entity
@Data
@Slf4j
public class TransportWorkerSpec {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(cascade = CascadeType.PERSIST)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinColumn(nullable = false)
    @JsonIgnore
    @JsonBackReference
    private Transport transport;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinColumn(nullable = false)
    private AssignmentCode assignmentCode;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private Gender gender = Gender.NO_PREFERENCE;

    @Column(nullable = false)
    private Integer numberOfStaff;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "workerSpec", cascade = CascadeType.ALL)
    private Set<Shift> bookings = new HashSet<>();


    public boolean isFullyBooked() {
        long totalBooked = this.bookings.stream().filter((e) -> !(e.getStatus().equals(ShiftStatus.APPLIED)||e.getStatus().equals(ShiftStatus.CANCELLED))).count();
        return totalBooked >= this.numberOfStaff;
    }

    public void createBooking(Worker worker) throws BusinessValidationException {
        if (!this.workerIsValid(worker))
            throw new BusinessValidationException("Worker is not qualified to book this job");
        if (this.isFullyBooked())
            throw new BusinessValidationException("This job role is fully booked");

        Set<Shift> transportBookings = new HashSet<>();
        this.getTransport().getWorkerSpec().forEach(ws -> transportBookings.addAll(ws.getBookings()));

        if (transportBookings.stream().anyMatch(e -> Objects.equals(e.getWorker().getId(), worker.getId())&&!e.getStatus().equals(ShiftStatus.CANCELLED)))
            return;

        Shift transportBooking = new Shift();
        transportBooking.setWorker(worker);
        transportBooking.setAgency(transport.getAgency());
        transportBooking.setStatus(ShiftStatus.BOOKED);
        transportBooking.setWorkerSpec(this);
        this.bookings.add(transportBooking);
    }

    public boolean addApplicant(Worker worker) throws BusinessValidationException {
        if (!this.workerIsValid(worker))
            throw new BusinessValidationException("Applicant is not qualified to book this job. Check gender, assignment code and compliance");
        if (this.isFullyBooked())
            throw new BusinessValidationException("This job role is already fully booked");
        if (this.bookings.stream().anyMatch(e -> Objects.equals(e.getWorker().getId(), worker.getId())&&!e.getStatus().equals(ShiftStatus.CANCELLED)))
            return true;

        Shift transportBooking = new Shift();
        transportBooking.setWorker(worker);
        transportBooking.setStatus(ShiftStatus.APPLIED);
        transportBooking.setWorkerSpec(this);
        this.bookings.add(transportBooking);
        return true;
    }

    public boolean workerIsValid(Worker worker) {
        if (this.gender == Gender.NO_PREFERENCE)
            return true;
        return worker.getGender().equals(this.gender);
    }

    public void removeBooking(Shift booking) {
        if (Objects.equals(this.getTransport().getTeamLeader(), booking.getWorker())) {
            this.getTransport().refreshState();
            this.getTransport().setTeamLeader(null);
        }
        this.bookings.remove(booking);
    }

    public Shift getNewBooking() {
        Shift booking = new Shift();
        booking.setWorkerSpec(this);
        return booking;
    }

//    public void authorize(LocalDateTime start, LocalDateTime end) {
//        this.bookings.forEach(e->e.authorize(start, end));
//    }


    public void setTransport(Transport transport) {
        this.transport = transport;
        transport.getWorkerSpec().add(this);
    }

    void startJob() {
        if(this.transport.getDateTimeRequired().isBefore(LocalDateTime.now())){
            this.bookings.forEach(Shift::startJob);
        }else
            log.error("Attempt to begin job before required start time. Job Details: {}", this);
    }


    void startJobByForce() {
            this.bookings.forEach(Shift::startJob);
    }

    public void expireBookings() {
        this.bookings.forEach(Shift::expire);
    }

    protected void cancelJob(){
        this.bookings.forEach(Shift::cancelByJob);
    }

    public void updateBookingTimes(TransportWorkerTimesDto vehicleLogDto) {
        this.bookings.forEach(b->b.updateBookingTimes(vehicleLogDto));
    }
}