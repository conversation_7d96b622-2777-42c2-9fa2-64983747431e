package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
//import com.cap10mycap10.worklinkservice.dto.notification.NotificationUpdateDto;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationResultDto;
import com.cap10mycap10.worklinkservice.model.Notification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface NotificationService {
    void addWorkerNotification(NotificationCreateDto notificationCreateDto);
    void saveNotification(NotificationCreateDto notificationCreateDto);
    void deleteNotification(Long id);
    Notification findById(Long id);
    List<NotificationResultDto> findWorkerNotifications(Long workerId, PageRequest of);
    Page<Notification> findAllPaged(PageRequest of);
    Notification getOne(Long id);
}
