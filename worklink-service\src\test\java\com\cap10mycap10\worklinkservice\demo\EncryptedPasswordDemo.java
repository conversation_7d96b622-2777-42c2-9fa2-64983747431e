package com.cap10mycap10.worklinkservice.demo;

import com.cap10mycap10.worklinkservice.dto.email.AgencyEmailConfigurationPublicDto;
import com.cap10mycap10.worklinkservice.dto.email.AgencyEmailConfigurationResponseDto;
import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import com.cap10mycap10.worklinkservice.service.EncryptionService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Demo test to show how SMTP password encryption works in API responses
 */
@SpringBootTest
public class EncryptedPasswordDemo {

    @Autowired
    private EncryptionService encryptionService;

    @Test
    public void demonstratePasswordEncryption() throws Exception {
        System.out.println("\n=== SMTP Password Encryption Demo ===\n");

        // Simulate a password that would be stored in the database
        String originalPassword = "MySecretPassword123";
        System.out.println("1. Original Password: " + originalPassword);

        // Encrypt the password (this is what gets stored in database)
        String encryptedPassword = encryptionService.encrypt(originalPassword);
        System.out.println("2. Encrypted Password (stored in DB): " + encryptedPassword);
        System.out.println("   Length: " + encryptedPassword.length() + " characters");

        // Create a mock email configuration
        AgencyEmailConfiguration config = AgencyEmailConfiguration.builder()
                .id(1L)
                .agencyId(1L)
                .smtpHost("smtp.hostinger.com")
                .smtpPort(465)
                .smtpUsername("<EMAIL>")
                .smtpPassword(originalPassword) // This would be decrypted by JPA converter
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .smtpAuth(true)
                .smtpStarttlsEnable(true)
                .isActive(true)
                .isVerified(true)
                .build();

        System.out.println("\n=== API Response Comparison ===\n");

        // External API Response (with encrypted password)
        String encryptedForApi = encryptionService.encrypt(config.getDecryptedSmtpPassword());
        AgencyEmailConfigurationPublicDto publicDto = AgencyEmailConfigurationPublicDto.fromEntity(config, encryptedForApi);
        System.out.println("3. External API Response (GET /agency/1):");
        System.out.println("   smtpPassword: " + publicDto.getSmtpPassword());
        System.out.println("   This encrypted password can be decrypted by the receiving service");

        // Internal API Response (with decrypted password)
        AgencyEmailConfigurationResponseDto responseDto = AgencyEmailConfigurationResponseDto.fromEntity(config);
        System.out.println("\n4. Internal API Response (GET /agency/1/active):");
        System.out.println("   smtpPassword: " + responseDto.getSmtpPassword());
        System.out.println("   This is the actual password for SMTP usage");

        // Verify decryption works
        String decryptedFromApi = encryptionService.decrypt(publicDto.getSmtpPassword());
        System.out.println("\n5. Decryption Verification:");
        System.out.println("   Encrypted from API: " + publicDto.getSmtpPassword());
        System.out.println("   Decrypted at destination: " + decryptedFromApi);
        System.out.println("   Passwords match: " + originalPassword.equals(decryptedFromApi));

        System.out.println("\n=== Summary ===");
        System.out.println("✅ External API consumers get encrypted passwords");
        System.out.println("✅ They can decrypt the password at their end");
        System.out.println("✅ Internal services get decrypted passwords directly");
        System.out.println("✅ Database stores encrypted passwords securely");

        System.out.println("\n=== Demo Complete ===\n");
    }
}
