package com.cap10mycap10.worklinkservice.dto.agencyworkertraining;

public interface IAgencyWorkerProperties {
    Long getId();

    Long getAgencyId();

    Long getWorkerId();

//    List<Training> trainings = new ArrayList<>();

    String getPaymentMethod();

    String getEmploymentStartDate();

    String getContractEndDate();

    String getNextCheckDate();

    String getTrainingDate();

    String getTrainingExpiry();

    String getRightToWork();

    String getDbsNumber();

    String getDbsExpiry();

    String getExpiry();

    String getRestrictions();

    String getRestrictionExpiry();


}
