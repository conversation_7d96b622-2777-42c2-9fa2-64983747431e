package com.cap10mycap10.worklinkservice.dao;

public interface ApprovedAgencyRepository /*extends JpaRepository<ApprovedAgency, ClientAgencyId>*/ {

   /* List<ApprovedAgency> findApprovedAgenciesByClient_Id(Long payerId);

    @Query(value = "select agency_email from approved_agency where client_id=?1", nativeQuery = true)
    List<String> findEmailsByClient_Id(Long payerId);

    @Query(value = "select client_id from approved_agency where agency_id=?1", nativeQuery = true)
    List<Long> findClientIds(Long agencyId);

    int countApprovedAgenciesByAgency_Id(Long agencyId);*/
}
