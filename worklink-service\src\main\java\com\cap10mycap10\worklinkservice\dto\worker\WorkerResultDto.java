package com.cap10mycap10.worklinkservice.dto.worker;

import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.enums.WorkerStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class WorkerResultDto {

    private Long id;

    private String firstname;

    private String lastname;

    private Gender gender;

    private String phoneNumber;

    private String email;

    private String username;

    private Long assignmentCodeId;

    private String assignmentCode;

    private String employmentNumber;

    private String postcode;
    private Long iHascoId;

    private String address;

    private String profilePic;

    private String nationality;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
    private LocalDate dob;

    private String cv;
    private Boolean compliant;

    private String assignmentName;

    private WorkerStatus status;

    private String agencyName;

    private Long agencyId;

    private String createdBy;

    private String totalPayslips;
    private String totalPayAdvices;

    private String grossPay;

}
