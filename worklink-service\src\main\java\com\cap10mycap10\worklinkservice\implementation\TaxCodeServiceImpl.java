package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.TaxCodeRepository;
import com.cap10mycap10.worklinkservice.dto.taxCode.TaxCodeDto;
import com.cap10mycap10.worklinkservice.dto.taxCode.TaxCodeUpdateDto;
import com.cap10mycap10.worklinkservice.model.TaxCode;
import com.cap10mycap10.worklinkservice.service.TaxCodeService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import java.util.List;


@Service
@Slf4j
public class TaxCodeServiceImpl implements TaxCodeService {
    private final TaxCodeRepository taxCodeRepository;
    private ObjectMapper objectMapper = new ObjectMapper();


    public TaxCodeServiceImpl(TaxCodeRepository taxCodeRepository) {
        this.taxCodeRepository = taxCodeRepository;
    }

    @Override
    public void addTaxCode(TaxCodeDto taxCodeDto) {
        TaxCode taxCode = new TaxCode();
        taxCode.setCode(taxCodeDto.getCode());
        taxCode.setDeduction(taxCodeDto.getDeduction());
        taxCodeRepository.save(taxCode);
    }

    @Override
    public void deleteTaxCode(Long id) {
        TaxCode taxCode = getOne(id);
        taxCodeRepository.delete(taxCode);
    }

    @Override
    public TaxCode findById(Long id) {
        return getOne(id);
    }

    @Override
    public List<TaxCode> findAll() {
        return taxCodeRepository.findAll();
    }

    @Override
    public Page<TaxCode> findAllPaged(PageRequest of) {
        return taxCodeRepository.findAll(of);
    }

    @Override
    public void save(TaxCodeUpdateDto taxCodeUpdateDto) {
        TaxCode taxCode = getOne(taxCodeUpdateDto.getTaxId());
        taxCode.setCode(taxCodeUpdateDto.getCode());
        taxCode.setDeduction(taxCodeUpdateDto.getDeduction());
        taxCodeRepository.save(taxCode);
    }

    @Override
    public TaxCode getOne(Long id) {
        return taxCodeRepository.findById(id).orElseThrow(
                () -> new RecordNotFoundException("Tax code does not exist")
        );
    }
}
