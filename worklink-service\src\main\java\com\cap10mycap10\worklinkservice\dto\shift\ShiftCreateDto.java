package com.cap10mycap10.worklinkservice.dto.shift;


import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import lombok.Data;

import jakarta.validation.constraints.Min;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data

public class ShiftCreateDto {

    private Long id;
    private Long shiftDirectorateId;
    private Boolean noneAttendance;
    private String queryResponse;

    private Long shiftLocationId;

    private LocalDateTime start;

    private LocalDateTime end;

    private LocalTime shiftStartTime;

    private LocalTime shiftEndTime;

    private String breakTime;

    private Gender gender;

    private Long shiftType;

    private Long assignmentCodeId;

    private String notes;

    private Boolean showNoteToFw;

    private Boolean showNoteToAgency;

    private Boolean publishToAllWorkers;

    private Boolean requireApplicationByWorkers;
    private Boolean directBooking;

    @Min(value = 1, message = "Minimum value is 1 hr before broadcasting")
    private int hoursBeforeBroadcasting;

    private ShiftStatus shiftStatus;

    private Long clientId;
    private Long workerId;

    private Long agency;

    private List<Long> agentIdList;
}
