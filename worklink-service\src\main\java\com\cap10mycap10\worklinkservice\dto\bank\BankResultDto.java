package com.cap10mycap10.worklinkservice.dto.bank;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class BankResultDto {
    private Long id;
    private String bank;
    private String name;
    private String code;
    private String account;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate signDate;
    private String lastModifiedDate;
    private String signed;
    private String fullname;
    private Long workerId;

}
