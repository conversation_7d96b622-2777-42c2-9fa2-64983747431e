package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AgencyExpenseRateRepository;
import com.cap10mycap10.worklinkservice.dto.agencyexpenses.AgencyExpenseRateDto;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.AgencyExpenseRate;
import com.cap10mycap10.worklinkservice.service.AgencyExpenseRateService;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ExpenseRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class AgencyExpenseRateServiceImpl implements AgencyExpenseRateService {

    private final AgencyExpenseRateRepository agencyExpenseRateRepository;
    private final AgencyService agencyService;
    private final ExpenseRateService expenseRateService;

    public AgencyExpenseRateServiceImpl(AgencyExpenseRateRepository expenseRateRepository, AgencyService agencyService, ExpenseRateService expenseRateService) {
        this.agencyExpenseRateRepository = expenseRateRepository;
        this.agencyService = agencyService;
        this.expenseRateService = expenseRateService;
    }

    @Override
    public void addAgencyExpenseRate(AgencyExpenseRateDto expenseRateDto) {
        AgencyExpenseRate expenseRate = new AgencyExpenseRate();
        expenseRate.setAgency(agencyService.getOne(expenseRateDto.getAgencyId()));
        expenseRate.setExpenseRate(expenseRateService.getOne(expenseRateDto.getExpenseRateId()));
        expenseRate.setRate(expenseRateDto.getRate());
        agencyExpenseRateRepository.save(expenseRate);
    }

    @Override
    public List<AgencyExpenseRate> findAgencyRates(Long agencyId) {
        return agencyExpenseRateRepository.findAllByAgency(agencyService.getOne(agencyId));
    }

    @Override
    public AgencyExpenseRate save(AgencyExpenseRateDto agencyExpenseRateDto) {
        AgencyExpenseRate expenseRate = getOne(agencyExpenseRateDto.getId());
        expenseRate.setRate(agencyExpenseRateDto.getRate());
        return agencyExpenseRateRepository.save(expenseRate);
    }

    @Override
    public AgencyExpenseRate getOne(Long id) {
        return agencyExpenseRateRepository.findById(id).orElseThrow(
                () -> new RecordNotFoundException("Agency Expense rate not found")
        );
    }
}
