package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.Promotion;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

public interface PromoCodeRepository extends JpaRepository<Promotion, Long>, JpaSpecificationExecutor<Promotion> {
    Optional<Promotion> findByCode(String code);
    Page<Promotion> findByStatusAndStartDateBefore(Status status, ZonedDateTime date, Pageable of);
    Page<Promotion> findByStatusAndExpiryDateBefore(Status status, ZonedDateTime date, Pageable of);
    Page<Promotion> findByStatus(Status status, Pageable of);

    @Query("SELECT p FROM Promotion p " +
            "JOIN p.vehicles v " +
            "WHERE v.id = :vehicleId " +
            "AND p.status = 'ACTIVE' " +
            "AND p.code IS NULL " +
            "AND p.daysHired <= :daysHired " +
            "AND (p.startDate IS NULL OR p.startDate <= CURRENT_TIMESTAMP) " +
            "AND (p.expiryDate IS NULL OR p.expiryDate > CURRENT_TIMESTAMP) " +
            "AND (p.usageLimit IS NULL OR p.usageLimit > SIZE(p.bookings)) " +
            "ORDER BY p.id DESC")
    List<Promotion> getLatestValidPublicPromotion(@Param("vehicleId") Long vehicleId,
                                                  @Param("daysHired") int daysHired);


    @Query("SELECT p FROM Promotion p " +
            "JOIN p.vehicles v " +
            "WHERE v.id = :vehicleId " +
            "AND p.status = 'ACTIVE' " +
            "AND p.code IS NULL " +
            "AND ((:searchStartDate IS NULL OR :searchEndDate IS NULL) AND " +
            "    (p.startDate IS NULL OR p.startDate <= CURRENT_TIMESTAMP) AND " +
            "    (p.expiryDate IS NULL OR p.expiryDate > CURRENT_TIMESTAMP) " +
            "    OR " +
            "    (:searchStartDate IS NOT NULL AND :searchEndDate IS NOT NULL) AND " +
            "    (p.startDate IS NULL OR p.startDate <= :searchStartDate) AND " +
            "    (p.expiryDate IS NULL OR p.expiryDate > :searchEndDate)) " +
            "AND (p.usageLimit IS NULL OR p.usageLimit > SIZE(p.bookings)) " +
            "ORDER BY p.id DESC")
    Page<Promotion> getLatestPublicPromotions(
            @Param("vehicleId") Long vehicleId,
            @Param("searchStartDate") ZonedDateTime searchStartDate,
            @Param("searchEndDate") ZonedDateTime searchEndDate,
            Pageable of);




    @Query("SELECT p FROM Promotion p " +
            "JOIN p.vehicles v " +
            "WHERE v.id = :vehicleId " +
            "AND p.status = 'ACTIVE' " +
            "AND ((:searchStartDate IS NULL OR :searchEndDate IS NULL) AND " +
            "    (p.startDate IS NULL OR p.startDate <= CURRENT_TIMESTAMP) AND " +
            "    (p.expiryDate IS NULL OR p.expiryDate > CURRENT_TIMESTAMP) " +
            "    OR " +
            "    (:searchStartDate IS NOT NULL AND :searchEndDate IS NOT NULL) AND " +
            "    (p.startDate IS NULL OR p.startDate <= :searchEndDate) AND " +
            "    (p.expiryDate IS NULL OR p.expiryDate > :searchStartDate)) " +
            "AND (p.usageLimit IS NULL OR p.usageLimit > SIZE(p.bookings)) " +
            "ORDER BY p.id DESC")
    Page<Promotion> getLatestPromotions(
            @Param("vehicleId") Long vehicleId,
            @Param("searchStartDate") ZonedDateTime searchStartDate,
            @Param("searchEndDate") ZonedDateTime searchEndDate,
            Pageable of);
}