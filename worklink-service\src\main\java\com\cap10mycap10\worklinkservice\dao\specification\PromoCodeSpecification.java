package com.cap10mycap10.worklinkservice.dao.specification;

import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.Promotion;
import org.springframework.data.jpa.domain.Specification;

import java.util.Set;

public class PromoCodeSpecification {


    public static Specification<Promotion> hasAgencyId(Long agencyId) {
        return (root, query, criteriaBuilder) -> {
            if (agencyId == null) {
                return criteriaBuilder.isNull(root.get("agency"));
            } else {
                return criteriaBuilder.equal(root.get("agency").get("id"), agencyId);
            }
        };
    }


    public static Specification<Promotion> hasStatuses(Set<Status> statuses) {
        return (root, query, criteriaBuilder) -> criteriaBuilder.in(root.get("status")).value(statuses);
    }

    public static Specification<Promotion> containsQuery(String searchQuery) {
        return (root, query, criteriaBuilder) -> criteriaBuilder.like(root.get("title"), "%" + searchQuery + "%");    }
}