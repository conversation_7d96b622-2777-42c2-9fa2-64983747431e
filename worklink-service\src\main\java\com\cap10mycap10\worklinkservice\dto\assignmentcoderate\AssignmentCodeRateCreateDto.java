package com.cap10mycap10.worklinkservice.dto.assignmentcoderate;

import lombok.Data;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalTime;

@Data
/*
Select client
Select Directorate
Select Shift Type
 */
public class AssignmentCodeRateCreateDto {

    private Long assignmentCodeId;//positions

    private Long agencyId;

    private Long clientId;

    private Long shiftTypeId;

    private DayOfWeek dayOfWeek;//Day

    private LocalTime startTime;

    private LocalTime endTime;

    private BigDecimal clientRate;//Agency bill client per hour

    private BigDecimal privateRate;//

    private BigDecimal umbrellaRate;//Companies but individuals

    private BigDecimal payeRate;//to be paid to worker

    private Long locationId;

    private Long directorateId;



}
