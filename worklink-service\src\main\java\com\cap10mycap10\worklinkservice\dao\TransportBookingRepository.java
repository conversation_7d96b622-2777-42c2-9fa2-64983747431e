//package com.cap10mycap10.worklinkservice.dao;
//
//import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
//import com.cap10mycap10.worklinkservice.enums.TransportBookingStatus;
//import com.cap10mycap10.worklinkservice.model.Transport;
//import com.cap10mycap10.worklinkservice.model.Shift;
//import com.cap10mycap10.worklinkservice.model.TransportWorkerSpec;
//import com.cap10mycap10.worklinkservice.model.Worker;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//
//import java.util.Collection;
//import java.util.List;
//
//public interface TransportBookingRepository extends JpaRepository<Shift, Long> {
//
//}
