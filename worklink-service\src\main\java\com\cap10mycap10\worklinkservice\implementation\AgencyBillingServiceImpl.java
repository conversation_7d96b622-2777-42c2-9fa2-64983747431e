package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AgencyBillRepository;
import com.cap10mycap10.worklinkservice.dao.ChargeRateRepository;
import com.cap10mycap10.worklinkservice.dao.ShiftRepository;
import com.cap10mycap10.worklinkservice.dao.VatRateRepository;
import com.cap10mycap10.worklinkservice.dto.billing.AgencyBillDto;
import com.cap10mycap10.worklinkservice.dto.billing.AgencyBillStatusDto;
import com.cap10mycap10.worklinkservice.dto.billing.ShiftBillDto;
import com.cap10mycap10.worklinkservice.model.AgencyBill;
import com.cap10mycap10.worklinkservice.model.ChargeRate;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.model.VatRate;
import com.cap10mycap10.worklinkservice.enums.BillStatus;
import com.cap10mycap10.worklinkservice.mapper.billing.AgencyBillToAgencyBillDto;
import com.cap10mycap10.worklinkservice.service.AgencyBillingService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import jakarta.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AgencyBillingServiceImpl implements AgencyBillingService {

    @Autowired
    private AgencyBillRepository agencyBillRepository;
    @Autowired
    private ShiftRepository shiftRepository;
    @Autowired
    private ChargeRateRepository chargeRateRepository;
    @Autowired
    private VatRateRepository vatRateRepository;
    @Autowired
    private AgencyBillToAgencyBillDto toAgencyBillDto;


    @Override
    public AgencyBillDto findById(Long id) {
        return toAgencyBillDto.convert(agencyBillRepository.findById(id).orElseThrow(
                () -> new EntityNotFoundException("Debit Note does not exist")
        ));
    }

    @Override
    public List<AgencyBillDto> findAllPaged(PageRequest of, Long agentId, LocalDate startDate, LocalDate endDate) {
        List<AgencyBill> agencyBills = agencyBillRepository.findAllPaged(of).toList();
        return filterAgencyBills(agencyBills, agentId, startDate, endDate);
    }

    @Override
    public List<AgencyBillDto> findAllPendingPaged(PageRequest of, Long agentId, LocalDate startDate, LocalDate endDate) {
        List<AgencyBill> agencyBills = agencyBillRepository.findAllPendingPaged(of).toList();
        return filterAgencyBills(agencyBills, agentId, startDate, endDate);
    }

    @SneakyThrows
    @Override
    public void createDebitOrders(ShiftBillDto shiftBillDto) {
        // get all shift that meet the criteria
        List<Shift> shifts = shiftRepository.findByIdIn(shiftBillDto.getShiftIds());
        CompletableFuture.runAsync(() -> createShiftDebitOrders(shiftBillDto, shifts));
    }

    private void createShiftDebitOrders(ShiftBillDto shiftBillDto, List<Shift> shifts) {
        ChargeRate chargeRate = chargeRateRepository.findAll().get(0);
        VatRate vatRate = vatRateRepository.findAll().get(0);
        for (Shift shift: shifts){
            AgencyBill agencyBill = new AgencyBill();
            agencyBill.setAgency(shift.getAgency());
            agencyBill.setClient(shift.getClient());
            agencyBill.setStatus(BillStatus.PENDING);
            agencyBill.setChargeRate(chargeRate.getChargeRate());
            agencyBill.setIssueDate(new Date());
            agencyBill.setNotes(shiftBillDto.getNotes());
            agencyBill.setDueDate(shiftBillDto.getDueDate());
            // todo: get discount from shift or somewhere
            agencyBill.setDiscountCharge(BigDecimal.ZERO);
            agencyBill = getHoursWorked(shift, agencyBill, chargeRate);
            agencyBill.setSubTotal(agencyBill.getTotalCharge().subtract(agencyBill.getDiscountCharge()));
            agencyBill.setVatRate(vatRate.getVatRate().multiply(agencyBill.getSubTotal()));
            agencyBill.setTotalDue(agencyBill.getSubTotal().add(agencyBill.getVatRate()));

            agencyBill.setShift(shift);
            agencyBillRepository.save(agencyBill);

            // todo: send email to billing address
        }
    }

    @Override
    public ResponseEntity SetDebitNotePaidStatus(AgencyBillStatusDto agencyBillStatusDto) {
        Optional<AgencyBill> agencyBillOptional = agencyBillRepository.findById(agencyBillStatusDto.getBillId());
        if (agencyBillOptional.get() != null){
            AgencyBill agencyBill = agencyBillOptional.get();
            try {
                if (agencyBill.getPaid()) {
                    return ResponseEntity.ok(HttpStatus.BAD_REQUEST);
                }
            }catch (NullPointerException npex){
                log.info("Bill status was null for bill id {}", agencyBill.getId());
            }
            agencyBill.setPaid(agencyBillStatusDto.isPaid());
            agencyBill.setPaymentRef(agencyBillStatusDto.getPaymentRef());
            if (agencyBillStatusDto.isPaid()){
                agencyBill.setStatus(BillStatus.PAID);
                agencyBill.setPaidDate(new Date());
            }
            agencyBillRepository.save(agencyBill);
            return ResponseEntity.ok(HttpStatus.ACCEPTED);
        }else{
            return ResponseEntity.ok(HttpStatus.NOT_FOUND);
        }
    }


    private List<AgencyBillDto> filterAgencyBills(List<AgencyBill> agencyBills, Long agentId, LocalDate startDate, LocalDate endDate) {
        if (agentId != null){
            agencyBills = agencyBills
                    .stream()
                    .filter(agencyBill -> agencyBill.getAgency() != null)
                    .filter(agencyBill -> agencyBill.getAgency().getId().equals(agentId))
                    .collect(Collectors.toList());
        }

        if (startDate != null){
            agencyBills = agencyBills
                    .stream()
                    .filter(agencyBill -> checkAfter(agencyBill.getCreatedDate(), startDate))
                    .collect(Collectors.toList());
        }

        if (endDate != null){
            agencyBills = agencyBills
                    .stream()
                    .filter(agencyBill -> checkBefore(agencyBill.getCreatedDate(), endDate))
                    .collect(Collectors.toList());
        }

        return agencyBills
                .stream()
                .map(toAgencyBillDto::convert)
                .collect(Collectors.toList());
    }

    @SneakyThrows
    private AgencyBill getHoursWorked(Shift shift, AgencyBill agencyBill, ChargeRate chargeRate) {
        // get hours from start to end of the shift
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        LocalTime date1 =  shift.getStart().toLocalTime();
        LocalTime date2 =  shift.getEnd().toLocalTime();
        long totalTime = (date2.getSecond() - date1.getSecond())*1000;

        // cater for the break
        long hours, mins;
        hours = Long.parseLong(shift.getBreakTime().split(" ")[0].replace("hr", "")) * 60 * 60 * 1000;
        mins = Long.parseLong(shift.getBreakTime().split(" ")[1].replace("mins", "")) * 60 * 1000;

        totalTime = totalTime - (hours + mins);

        agencyBill.setTotalUnits(((totalTime / (1000*60*60)) % 24) + "hr " + ((totalTime / (1000*60)) % 60) + "mins");

        float hourCharge = (totalTime / (1000*60*60)) % 24;
        float minCharge = ((totalTime / (1000*60)) % 60);

        agencyBill.setTotalCharge(chargeRate.getChargeRate().multiply(BigDecimal.valueOf(((hourCharge*60) + minCharge) / 60)));

        return agencyBill;
    }

    private boolean checkAfter(Instant leftDate, LocalDate rightDate) {
        return leftDate.compareTo(rightDate.atStartOfDay().toInstant(ZoneOffset.UTC)) >= 0;
    }

    private boolean checkBefore(Instant leftDate, LocalDate rightDate) {
        return leftDate.compareTo(rightDate.atStartOfDay().toInstant(ZoneOffset.UTC)) <= 0;
    }

}
