package com.cap10mycap10.worklinkservice.dto.transport;

import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.enums.Gender;
import lombok.*;

import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class TransportWorkerSpecDto {
    private String assignmentCodeName;
    private Long assignmentCode;
    private Long id;
    private Gender gender;
    private Integer numberOfStaff;
    private List<BookingResultDto> bookings;
}
