package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.VehicleBooking;
import com.cap10mycap10.worklinkservice.model.VehicleBookingDeposit;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface VehicleBookingDepositRepository extends JpaRepository<VehicleBookingDeposit, Long> {
    List<VehicleBookingDeposit> findByVehicleBookingId(Long vehicleBookingId);
    List<VehicleBookingDeposit> findByVehicleBooking(VehicleBooking vehicleBooking);
}
