package com.cap10mycap10.worklinkservice.mapper.invoice;

import com.cap10mycap10.worklinkservice.dto.invoice.InvoiceItemResult;
import com.cap10mycap10.worklinkservice.model.InvoiceItem;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class InvoiceItemToInvoiceItemResult implements Converter<List<InvoiceItem>, List<InvoiceItemResult>> {
    @Override
    public List<InvoiceItemResult> convert(List<InvoiceItem> invoiceItem) {

        List<InvoiceItemResult> invoiceItemResults = new ArrayList<>();
        for (InvoiceItem item:invoiceItem
             ) {
            InvoiceItemResult invoiceItemResult = new InvoiceItemResult();
            invoiceItemResult.setEndDate(item.getEndDate());
            invoiceItemResult.setStartDate(item.getStartDate());
            invoiceItemResult.setDayOfTheWeek(item.getDayOfTheWeek());
            invoiceItemResult.setId(item.getId());

            invoiceItemResult.setTrainingId(item.getTrainingId());

            invoiceItemResult.setAssignmentCode(item.getAssignmentCode());
            invoiceItemResult.setClient(item.getClient());
            invoiceItemResult.setClientId(item.getClientId());

            invoiceItemResult.setWorker(item.getWorker());
            invoiceItemResult.setDescription(item.getDescription());
            invoiceItemResult.setShiftType(item.getShiftType());
            invoiceItemResult.setDirectorate(item.getDirectorate());
            invoiceItemResult.setRate(item.getRate());
            invoiceItemResult.setEndTime(item.getEndTime());
            invoiceItemResult.setStartTime(item.getStartTime());
            invoiceItemResult.setNumberOfHoursWorked(item.getNumberOfHoursWorked());
            invoiceItemResult.setTotal(item.getTotal());
            invoiceItemResult.setShiftId(item.getShiftId());

            // Map tax-related fields
            invoiceItemResult.setTaxExempt(item.getTaxExempt());
            invoiceItemResult.setTaxRate(item.getTaxRate());
            invoiceItemResult.setTaxAmount(item.getTaxAmount());
            invoiceItemResult.setNetAmount(item.getNetAmount());
            invoiceItemResult.setTaxInclusive(item.getTaxInclusive());

            invoiceItemResults.add(invoiceItemResult);
        }
        return invoiceItemResults;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
