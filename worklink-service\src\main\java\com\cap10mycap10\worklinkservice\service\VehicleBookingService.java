package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDepositDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.model.Invoice;
import com.cap10mycap10.worklinkservice.model.Rating;
import com.cap10mycap10.worklinkservice.model.VehicleBookingDeposit;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDateTime;
import java.util.List;

public interface VehicleBookingService {
    VehicleBookingDto book(VehicleBookingDto vehicleBookingDto);


    VehicleBookingDto getBookingQuote(VehicleBookingDto vehicleBookingDto);

    VehicleBookingDto update(VehicleBookingDto vehicleBookingDto);

    VehicleBookingDto fulfillStripeCheckout(Charge sessionId) throws StripeException;

    void checkForUnpaidBookings();
    VehicleBookingDto bookingPaidPaynow(Invoice quote);
    VehicleBookingDto bookingPaidPaynow(Long quoteId);
    VehicleBookingDto handOverVehicle(VehicleBookingDto vehicleBookingDto);
    VehicleBookingDto returnVehicle(VehicleBookingDto vehicleBookingDto);
    Rating rateVehicleBooking(Rating rating, Long vehicleBookingId);
    VehicleBookingDto findById(Long id);
    VehicleBookingDto findByIdAndEmail(Long id, String email);
    Page<VehicleBookingDto> findAll(Long agencyId, List<VehicleBookingStatus> statuses, String searchCriteria, Long bookingId, LocalDateTime start, LocalDateTime end, PageRequest of);
//    Page<VehicleBookingDto> findAll(Long agencyId, List<VehicleBookingStatus> statuses, String regNumber, Long bookingId, LocalDateTime start, LocalDateTime end, PageRequest of);
    Page<VehicleBookingDto> findByClient(Long clientId, List<VehicleBookingStatus> status, String s,Long agencyId, PageRequest of);

    void sendRentalReminders();

    /**
     * Resends a specific email for a booking based on the email type
     * @param bookingId The ID of the booking
     * @param bookingEmailType The type of email to resend
     * @return The updated booking DTO
     */
    VehicleBookingDto resendBookingEmail(Long bookingId, BookingEmailType bookingEmailType);

    /**
     * Cancels a vehicle booking
     * @param bookingId The ID of the booking to cancel
     * @param cancelReason The reason for cancellation (required if within 48 hours of booking start)
     * @param cancelledByAgency Whether the cancellation was initiated by the agency (true) or client (false)
     * @return The updated booking DTO
     */
    VehicleBookingDto cancelBooking(Long bookingId, String cancelReason, Boolean cancelledByAgency);

    /**
     * Creates a deposit for a vehicle booking
     * @param depositDto The deposit information
     * @return The created deposit DTO
     */
    VehicleBookingDepositDto createDeposit(VehicleBookingDepositDto depositDto);

    /**
     * Gets all deposits for a vehicle booking
     * @param bookingId The ID of the booking
     * @return List of deposits for the booking
     */
    List<VehicleBookingDepositDto> getDepositsByBookingId(Long bookingId);
}
