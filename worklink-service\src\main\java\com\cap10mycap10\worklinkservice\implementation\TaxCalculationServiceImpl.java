package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.service.TaxCalculationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static java.util.Objects.nonNull;

@Service
@Slf4j
public class TaxCalculationServiceImpl implements TaxCalculationService {

    @Override
    public TaxCalculationResult calculateVehicleTax(Vehicle vehicle, BigDecimal rate, AgencySettings agencySettings) {
        if (!agencySettings.isChargeVat() || isVehicleTaxExempt(vehicle, agencySettings)) {
            return new TaxCalculationResult(rate, BigDecimal.ZERO, rate, BigDecimal.ZERO, true, false);
        }

        BigDecimal taxRate = getEffectiveVehicleTaxRate(vehicle, agencySettings);
        boolean taxInclusive = isVehiclePricingTaxInclusive(vehicle, agencySettings);

        return calculateTax(rate, taxRate, taxInclusive);
    }

    @Override
    public TaxCalculationResult calculateAddonTax(VehicleInventory addon, BigDecimal rate, AgencySettings agencySettings) {
        if (!agencySettings.isChargeVat() || isAddonTaxExempt(addon, agencySettings)) {
            return new TaxCalculationResult(rate, BigDecimal.ZERO, rate, BigDecimal.ZERO, true, false);
        }

        BigDecimal taxRate = getEffectiveAddonTaxRate(addon, agencySettings);
        boolean taxInclusive = isAddonPricingTaxInclusive(addon, agencySettings);

        return calculateTax(rate, taxRate, taxInclusive);
    }

    @Override
    public void applyTaxToInvoiceItem(InvoiceItem invoiceItem, Vehicle vehicle, VehicleInventory addon, AgencySettings agencySettings) {
        TaxCalculationResult result;
        
        if (nonNull(vehicle)) {
            result = calculateVehicleTax(vehicle, invoiceItem.getTotal(), agencySettings);
        } else if (nonNull(addon)) {
            result = calculateAddonTax(addon, invoiceItem.getTotal(), agencySettings);
        } else {
            // Default calculation if neither vehicle nor addon is specified
            result = new TaxCalculationResult(invoiceItem.getTotal(), BigDecimal.ZERO, invoiceItem.getTotal(), BigDecimal.ZERO, true, false);
        }

        // Apply the calculation results to the invoice item
        invoiceItem.setTaxExempt(result.isTaxExempt());
        invoiceItem.setTaxRate(result.getTaxRate());
        invoiceItem.setTaxAmount(result.getTaxAmount());
        invoiceItem.setNetAmount(result.getNetAmount());
        invoiceItem.setTaxInclusive(result.isTaxInclusive());
        invoiceItem.setTotal(result.getTotalAmount());
    }

    @Override
    public BigDecimal getEffectiveVehicleTaxRate(Vehicle vehicle, AgencySettings agencySettings) {
        // Check if vehicle has a custom tax rate
        if (nonNull(vehicle.getCustomTaxRate()) && vehicle.getCustomTaxRate().isActive()) {
            return vehicle.getCustomTaxRate().getPercentage();
        }
        
        // Use agency default tax rate
        return agencySettings.getVatPercentage();
    }

    @Override
    public BigDecimal getEffectiveAddonTaxRate(VehicleInventory addon, AgencySettings agencySettings) {
        // Check if addon has a custom tax rate
        if (nonNull(addon.getCustomTaxRate()) && addon.getCustomTaxRate().isActive()) {
            return addon.getCustomTaxRate().getPercentage();
        }
        
        // Use agency default tax rate
        return agencySettings.getVatPercentage();
    }

    @Override
    public boolean isVehicleTaxExempt(Vehicle vehicle, AgencySettings agencySettings) {
        // Check explicit tax exemption
        if (nonNull(vehicle.getTaxExempt()) && vehicle.getTaxExempt()) {
            return true;
        }
        
        // Check agency default for vehicles
        return !agencySettings.isVehiclesTaxableByDefault();
    }

    @Override
    public boolean isAddonTaxExempt(VehicleInventory addon, AgencySettings agencySettings) {
        // Check explicit tax exemption
        if (nonNull(addon.getTaxExempt()) && addon.getTaxExempt()) {
            return true;
        }
        
        // Check agency default for addons
        return !agencySettings.isAddonsTaxableByDefault();
    }

    @Override
    public boolean isVehiclePricingTaxInclusive(Vehicle vehicle, AgencySettings agencySettings) {
        // Check vehicle-specific setting
        if (nonNull(vehicle.getTaxInclusive())) {
            return vehicle.getTaxInclusive();
        }
        
        // Use agency default
        return agencySettings.isDefaultTaxInclusive();
    }

    @Override
    public boolean isAddonPricingTaxInclusive(VehicleInventory addon, AgencySettings agencySettings) {
        // Check addon-specific setting
        if (nonNull(addon.getTaxInclusive())) {
            return addon.getTaxInclusive();
        }
        
        // Use agency default
        return agencySettings.isDefaultTaxInclusive();
    }

    private TaxCalculationResult calculateTax(BigDecimal amount, BigDecimal taxRatePercent, boolean taxInclusive) {
        if (taxRatePercent.compareTo(BigDecimal.ZERO) == 0) {
            return new TaxCalculationResult(amount, BigDecimal.ZERO, amount, BigDecimal.ZERO, true, false);
        }

        BigDecimal netAmount;
        BigDecimal taxAmount;
        BigDecimal totalAmount;

        if (taxInclusive) {
            // Price includes tax - calculate net amount and tax amount
            BigDecimal divisor = BigDecimal.ONE.add(taxRatePercent.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_EVEN));
            netAmount = amount.divide(divisor, 2, RoundingMode.HALF_EVEN);
            taxAmount = amount.subtract(netAmount);
            totalAmount = amount;
        } else {
            // Price excludes tax - calculate tax amount
            netAmount = amount;
            taxAmount = amount.multiply(taxRatePercent).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_EVEN);
            totalAmount = netAmount.add(taxAmount);
        }

        return new TaxCalculationResult(netAmount, taxAmount, totalAmount, taxRatePercent, false, taxInclusive);
    }
}
