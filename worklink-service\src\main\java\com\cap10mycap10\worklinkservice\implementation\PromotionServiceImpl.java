package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.PromoCodeRepository;
import com.cap10mycap10.worklinkservice.dao.VehicleFilterRepository;
import com.cap10mycap10.worklinkservice.dao.specification.PromoCodeSpecification;
import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.mapper.promocode.PromotionToPromotionDto;
import com.cap10mycap10.worklinkservice.mapper.vehiclefilter.VehicleFilterToVehicleFilterDto;
import com.cap10mycap10.worklinkservice.model.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.PromotionService;
import com.cap10mycap10.worklinkservice.service.VehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static java.util.Objects.nonNull;
import static org.springframework.data.jpa.domain.Specification.where;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class PromotionServiceImpl implements PromotionService {
    @Autowired
    private PromoCodeRepository codeRepository;
    @Autowired
    private VehicleFilterRepository vehicleFilterRepository;
    @Autowired
    private AgencyService agencyService;
    @Autowired
    private PromotionToPromotionDto toPromoCodeDto;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private VehicleFilterToVehicleFilterDto toVehicleFilterDto;

    @Override
    public PromotionDto save(PromotionDto promotionDto) {
        if(nonNull(promotionDto.getCode())) {
            Optional<Promotion> existingCode = codeRepository.findByCode(promotionDto.getCode());
            if (existingCode.isPresent()) throw new BusinessValidationException("Promo code already exists");
        }
        VehicleFilter vehicleFilter = vehicleFilterRepository.saveAndFlush(toVehicleFilterDto.convertToEntity(promotionDto.getVehicleFilter()));

        Promotion promotion;
        if(promotionDto.getId()==null){
            if (nonNull(promotionDto.getAgency().getAgencyId())) {
                Agency agency = agencyService.getOne(promotionDto.getAgency().getAgencyId());
                promotion = new Promotion(promotionDto, agency);
            } else {
                promotion = new Promotion(promotionDto);
            }
        }else{
            promotion = getOne(promotionDto.getId());
            if (nonNull(promotionDto.getAgency().getAgencyId())) {
                Agency agency = agencyService.getOne(promotionDto.getAgency().getAgencyId());
                promotion.setAgency(agency);
            }
            promotion.setDescription(promotionDto.getDescription());
            promotion.setTitle(promotionDto.getTitle());
            promotion.setExpiryDate(promotionDto.getExpiryDate());
            promotion.setStartDate(promotionDto.getStartDate());
            if(nonNull(promotionDto.getDiscount())) promotion.setDiscount(promotionDto.getDiscount());
            promotion.setPromotionType(promotionDto.getPromotionType());
            promotion.setUsageLimit(promotionDto.getUsageLimit());
            promotion.setExtraDays(promotionDto.getExtraDays());
            promotion.setExtraMileage(promotionDto.getExtraMileage());
            promotion.setDaysHired(promotionDto.getDaysHired());
        }
        promotion = codeRepository.save(promotion);


        promotion.setVehicleFilter(vehicleFilter);
        vehicleFilter.setPromotion(promotion);


        // Limit initial vehicles to 50 instead of 500
        Page<Vehicle> vehicles = vehicleService.filterVehicles(promotion.getVehicleFilter(), PageRequest.of(0, 50));
        promotion.addVehicles(new HashSet<>(vehicles.getContent()));

        // Log if there are more vehicles to be added by the scheduler
        if(vehicles.getTotalElements() > 50) {
            log.info("Promotion {} has {} vehicles matching filter. Initial 50 added, remaining will be added by scheduler.",
                    promotion.getId(), vehicles.getTotalElements());
        }

        try{
            promotion = codeRepository.save(promotion);
        }catch (Exception e){
            log.error("Error saving promotion: {}", e.getMessage());
        }

        return toPromoCodeDto.convert(promotion);
    }

    @Override
    public PromotionDto findById(Long id) {
        return toPromoCodeDto.convert(getOne(id));
    }

    @Override
    public void modifyPendingPromotions(){
        log.info("Starting pending promotions");
        Page<Promotion> promotions = codeRepository.findByStatusAndStartDateBefore(Status.INACTIVE, ZonedDateTime.now(),  PageRequest.of(0, 50));

        log.info("Found {} pending start promotions", promotions.getTotalElements());
        for(Promotion promotion : promotions.getContent()){
            promotion.activate();
            codeRepository.save(promotion);
        }

        Page<Promotion> expiredPromotions = codeRepository.findByStatusAndExpiryDateBefore(Status.ACTIVE, ZonedDateTime.now(),  PageRequest.of(0, 50));

        log.info("Found {} pending expiry promotions", expiredPromotions.getTotalElements());
        for(Promotion promotion : expiredPromotions.getContent()){
            promotion.cancel();
            codeRepository.save(promotion);
        }
    }



    @Override
    public Page<PromotionDto> findAll(Long agencyId, Set<Status> statuses, String query, Pageable pageable) {
        return codeRepository.findAll(
                where(( PromoCodeSpecification.hasAgencyId(agencyId)))
                        .and((statuses != null && !statuses.isEmpty()) ? PromoCodeSpecification.hasStatuses(statuses) : null)
                        .and(query != null ? PromoCodeSpecification.containsQuery(query.toUpperCase()) : null),
                pageable
        ).map(toPromoCodeDto::convert);
    }




    @Override
    public PromotionDto update(PromotionDto promotionDto) {
        Promotion code = getOne(promotionDto.getId());
        code.setDescription(promotionDto.getDescription());
        code.setTitle(promotionDto.getTitle());
        code.setExpiryDate(promotionDto.getExpiryDate());
        code.setStartDate(promotionDto.getStartDate());
        code.setDiscount(promotionDto.getDiscount());
        code.setPromotionType(promotionDto.getPromotionType());
        code.setUsageLimit(promotionDto.getUsageLimit());
        code.setExtraDays(promotionDto.getExtraDays());
        return toPromoCodeDto.convert(codeRepository.save(code));
    }

    @Override
    public void cancel(Long id) {
        Promotion code = getOne(id);
        code.cancel();
        codeRepository.save(code);
    }

    @Override
    public void activate(Long id) {
        Promotion code = getOne(id);
        code.activate();
        codeRepository.save(code);
    }

    @Override
    public Promotion getOne(Long id) {
       return codeRepository.findById(id).orElseThrow(()->new BusinessValidationException("Promo code not found"));
       }

       @Override
    public Promotion getOneByCode(String id) {
       return codeRepository.findByCode(id).orElseThrow(()->new BusinessValidationException("Promo code not found"));
       }

    @Override
    public Promotion getLatestValidPublicPromotion(VehicleBooking vehicleBooking, boolean fromAdminWebsite) {
        var promos = codeRepository.getLatestValidPublicPromotion(
                vehicleBooking.getVehicle().getId(),
                vehicleBooking.getDaysHired());

        return promos.stream()
                .filter(promo -> !promo.getAdminDiscount())
                .findFirst()
                .orElse(promos.isEmpty() ? null : (fromAdminWebsite?promos.get(0): null));
    }


    @Override
    public Page<Promotion> getLatestPublicPromotions(Vehicle vehicle, ZonedDateTime searchStartDate, ZonedDateTime searchEndDate) {
        var promos = codeRepository.getLatestPublicPromotions(vehicle.getId(), searchStartDate, searchEndDate, PageRequest.of(0, 1));
        return promos;
    }

    @Override
    public Page<Promotion> getLatestPromotions(Vehicle vehicle, ZonedDateTime searchStartDate, ZonedDateTime searchEndDate) {
        var promos = codeRepository.getLatestPromotions(vehicle.getId(), searchStartDate, searchEndDate, PageRequest.of(0, 5));
        return promos;
    }

    @Override
    public void addVehiclesToPromotions() {
        log.info("Starting scheduled task to add vehicles to promotions");

        // Get active promotions
        Page<Promotion> activePromotions = codeRepository.findByStatus(Status.ACTIVE, PageRequest.of(0, 20));

        for (Promotion promotion : activePromotions.getContent()) {
            try {
                // Get total number of vehicles that match the filter
                Page<Vehicle> matchingVehicles = vehicleService.filterVehicles(
                        promotion.getVehicleFilter(),
                        PageRequest.of(0, 1));
                long totalMatchingVehicles = matchingVehicles.getTotalElements();

                // Get current number of vehicles in the promotion
                int currentVehicleCount = promotion.getVehicles().size();

                // If we have more matching vehicles than current vehicles, add more
                if (totalMatchingVehicles > currentVehicleCount) {
                    log.info("Promotion {} has {} matching vehicles but only {} are currently added",
                            promotion.getId(), totalMatchingVehicles, currentVehicleCount);

                    // Get the next batch of vehicles (skip the ones we already have)
                    Page<Vehicle> nextBatchVehicles = vehicleService.filterVehicles(
                            promotion.getVehicleFilter(),
                            PageRequest.of(currentVehicleCount / 50, 50));

                    // If we have vehicles to add
                    if (!nextBatchVehicles.getContent().isEmpty()) {
                        // Add the vehicles to the promotion
                        Set<Vehicle> vehiclesToAdd = new HashSet<>(nextBatchVehicles.getContent());

                        // Filter out vehicles that are already in the promotion
                        vehiclesToAdd.removeAll(promotion.getVehicles());

                        if (!vehiclesToAdd.isEmpty()) {
                            log.info("Adding {} vehicles to promotion {}", vehiclesToAdd.size(), promotion.getId());
                            promotion.addVehicles(vehiclesToAdd);
                            codeRepository.save(promotion);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error adding vehicles to promotion {}: {}", promotion.getId(), e.getMessage());
            }
        }

        log.info("Completed scheduled task to add vehicles to promotions");
    }
}
