package com.cap10mycap10.worklinkservice.mapper.shift;

import com.cap10mycap10.worklinkservice.dao.ShiftRepository;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.enums.BookingType;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.model.Location;
import com.cap10mycap10.worklinkservice.model.ShiftDirectorate;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import static java.util.Objects.nonNull;

@Component
@Slf4j
public class ShiftToShiftResultDto implements Converter<Shift, BookingResultDto> {

    private final ShiftRepository shiftRepository;


    public ShiftToShiftResultDto(ShiftRepository shiftRepository) {
        this.shiftRepository = shiftRepository;
    }

    @Override
    public BookingResultDto convert(Shift shift) {
        BookingResultDto bookingResultDto = new BookingResultDto();
        if(nonNull(shift.getWorkerSpec()))bookingResultDto = convertGrouped(shift);

        if(nonNull(shift.getWorker()))bookingResultDto.setWorkerId(shift.getWorker().getId());
        bookingResultDto.setId(shift.getId());
        if(nonNull(shift.getStart())) bookingResultDto.setStart(shift.getStart());
        if(nonNull(shift.getEnd()))bookingResultDto.setEnd(shift.getEnd());
        if (shift.getApplicantCount() != null) {
            bookingResultDto.setApplicantCount(shift.getApplicantCount().toString());
        }




        if (shift.getGender() != null) {
            bookingResultDto.setGender(shift.getGender());
        }
        if (shift.getShiftType() != null) {
            bookingResultDto.setShiftType(shift.getShiftType().getName());
        }


        if (shift.getAuthorizedDate() != null) {
            bookingResultDto.setAuthorizedDate(shift.getAuthorizedDate());
        }


        if (shift.getBookedDate() != null) {
            bookingResultDto.setBookedDate(shift.getBookedDate());
        }

        if (shift.getCancelReason() != null) {
            bookingResultDto.setCancelledReason(shift.getCancelReason());
        }

        if (shift.getQueryReason() != null) {
            bookingResultDto.setQueriedReason(shift.getQueryReason());
        }

        if (shift.getQueriedDate() != null) {
            bookingResultDto.setQueriedDate(shift.getQueriedDate());
        }

        if (shift.getCancelledDate() != null) {
            bookingResultDto.setCancelledDate(shift.getCancelledDate());
        }

        if (shift.getNotes() != null) {
            bookingResultDto.setNotes(shift.getNotes());
        }


            bookingResultDto.setShowNoteToAgency(shift.getShowNoteToAgency());
            if(nonNull(shift.getLastAuthorisationReminder()))bookingResultDto.setLastAuthorisationReminder(shift.getLastAuthorisationReminder().until(LocalDateTime.now(), ChronoUnit.HOURS));


        if (shift.getShowNoteToFw() != null) {
            bookingResultDto.setShowNoteToFw(shift.getShowNoteToFw());
        }

        bookingResultDto.setHoursBeforeBroadcasting(shift.getHoursBeforeBroadcasting());

        bookingResultDto.setRequireApplicationByWorkers(shift.getRequireApplicationByWorkers());
        if (shift.getStatus() != null) {
            bookingResultDto.setShiftStatus(shift.getStatus().toString());
        }
        if (shift.getCreatedBy() != null) {
            bookingResultDto.setCreatedBy(shift.getCreatedBy());
        }



        if (shift.getWorker() != null) {
            bookingResultDto.setWorker(shift.getWorker().getFirstname()+ " " + shift.getWorker().getLastname());
        }
        if (shift.getClient() != null) {
            bookingResultDto.setClient(shift.getClient().getName());
            bookingResultDto.setClientId(shift.getClient().getId());
        }

        if (shift.getAgency() != null) {
            bookingResultDto.setAgency(shift.getAgency().getName());
        }

        if (shift.getReleased() != null) {
            bookingResultDto.setReleased(shift.getReleased());
        }
        if(shift.getCarPooling() != null) {
            bookingResultDto.setCarPooling(shift.getCarPooling());
        }
        if(shift.getCarPoolingShiftSet() != null) {
            bookingResultDto.setCarPoolingShiftSet(shift.getCarPoolingShiftSet());
        }

        if(nonNull(shift.getDirectorate())){
            bookingResultDto.setDirectorate(shift.getDirectorate().getName());
            Location location = shift.getDirectorate().getLocation();
            bookingResultDto.setShiftLocation(location.getCity());
            bookingResultDto.setPhoneNumber(shift.getDirectorate().getPhoneNumber());

        }
        bookingResultDto.setPostCode(bookingResultDto.getPostCode());
        if(nonNull(shift.getAssignmentCode()))bookingResultDto.setAssignmentCode(shift.getAssignmentCode().getCode());
        bookingResultDto.setBreakTime(shift.getBreakTime());

        if (shift.getAppliedStatus() != null) {
            bookingResultDto.setAppliedStatus(shift.getAppliedStatus());
        }


            List<Long> count = new ArrayList<Long>();

            shift.getAgencies().forEach(a -> {
                count.add(a.getId());
            });

            bookingResultDto.setAgencies(count);

        return bookingResultDto;
    }

    private BookingResultDto convertGrouped(Shift shift) {
        BookingResultDto bookingResultDto = new BookingResultDto();
        bookingResultDto.setId(shift.getId());
        if(nonNull(shift.getWorkerSpec())) {
            bookingResultDto.setWorkerSpecId(shift.getWorkerSpec().getId());
            ShiftDirectorate shiftDirectorate = shift.getWorkerSpec().getTransport().getPickupLocationDirectorate();
            if(nonNull(shiftDirectorate)){
                bookingResultDto.setDirectorate(shiftDirectorate.getName());
                Location location = shiftDirectorate.getLocation();
                bookingResultDto.setShiftLocation(location.getCity());
                bookingResultDto.setPostCode(shiftDirectorate.getPostCode());
            }

            bookingResultDto.setPAddress(shift.getWorkerSpec().getTransport().getPcaddress());
            bookingResultDto.setPPostCode(shift.getWorkerSpec().getTransport().getPickupPostCode());
            bookingResultDto.setPWard(shift.getWorkerSpec().getTransport().getPward());
            bookingResultDto.setDAddress(shift.getWorkerSpec().getTransport().getDestination());
            bookingResultDto.setDPostCode(shift.getWorkerSpec().getTransport().getDestinationPostCode());
            bookingResultDto.setDWard(shift.getWorkerSpec().getTransport().getDward());
            bookingResultDto.setPhoneNumber(shift.getWorkerSpec().getTransport().getDestinationContactNumber());
            bookingResultDto.setStart(shift.getWorkerSpec().getTransport().getDateTimeRequired());
            if(nonNull(shift.getWorkerSpec().getTransport().getEnd()))
                bookingResultDto.setEnd(shift.getWorkerSpec().getTransport().getEnd());
            bookingResultDto.setActualStart(shift.getWorkerSpec().getTransport().getStart());
            if(nonNull(shift.getWorkerSpec().getTransport().getBreakTime())) bookingResultDto.setBreakTime(shift.getWorkerSpec().getTransport().getBreakTime().toString());
            bookingResultDto.setNumberOfStaff(shift.getWorkerSpec().getTransport().getRequiredStaff());
        }

        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        DateTimeFormatter patternTime = DateTimeFormatter.ofPattern("HH:mm");


        bookingResultDto.setBookingType(BookingType.TRANSPORT);
        if(nonNull(shift.getStatus()))bookingResultDto.setShiftStatus(shift.getStatus().toString());


        if (nonNull(shift.getWorker())) {
            bookingResultDto.setLastModifiedDate(shift.getWorker().getLastModifiedDate());
            bookingResultDto.setWorker(shift.getWorker().getFirstname()+ " "+ shift.getWorker().getLastname());
        }
        return bookingResultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
