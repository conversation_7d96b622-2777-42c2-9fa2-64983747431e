package com.cap10mycap10.worklinkservice.dto.payadvice;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class PayAdviceItemResult {
    private Long id;

    private Long shiftId;

    private String dayOfTheWeek;

    private String startTime;

    private String endTime;

    private String startDate;

    private String endDate;
    private String directorate;

    private double numberOfHoursWorked;

    private BigDecimal rate;

    private BigDecimal total;

    private BigDecimal getYearGross;

    private String getPayDate;
}
