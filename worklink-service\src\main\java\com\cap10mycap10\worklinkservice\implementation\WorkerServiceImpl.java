package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.agencyworkerproperties.IAgencyWorkerProperties;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.dto.file.FileDto;
import com.cap10mycap10.worklinkservice.dto.shift.AgencyList;
import com.cap10mycap10.worklinkservice.dto.worker.*;
import com.cap10mycap10.worklinkservice.dto.workerappliedshift.WorkerAppliedShiftRawResultDto;
import com.cap10mycap10.worklinkservice.dto.workerappliedshift.WorkerAppliedShiftResultDto;
import com.cap10mycap10.worklinkservice.exception.FileNotFoundException;
import com.cap10mycap10.worklinkservice.feign.RegisterAgentAdminFeignClient;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.request.UserDto;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerDtoToWorker;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerToWorkerResultDto;
import com.cap10mycap10.worklinkservice.service.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.util.stereotypes.Lazy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import org.springframework.web.multipart.MultipartFile;

import jakarta.persistence.NoResultException;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class WorkerServiceImpl implements WorkerService {

    @Autowired
    private  AgencyToAgencyResultDto toAgencyResultDto;


    @Value("${storage.volume.path}")
    private  String rootPath;

    @Autowired
    private WorkerTrainingSessionRepository workerTrainingSessionRepository;

    @Autowired
    private ShiftRepository transportBookingRepository;

    @Autowired
    AuthenticationFacadeService authenticationFacadeService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Lazy
    private final WorkerRepository workerRepository;

    private final ShiftRepository shiftRepository;
    private final WorkerToWorkerResultDto toWorkerResultDto;

    private final AgencyWorkerPropertiesRepository agencyWorkerPropertiesRepository;
    private final WorkerDtoToWorker toWorker;

    @Lazy
    private final ShiftService shiftService;
    private final RegisterAgentAdminFeignClient registerAgentAdminFeignClient;
    private final AgencyService agencyService;

    private final ShiftDirectorateService shiftDirectorateService;
    private final AgencyRepository agencyRepository;
    private final AssignmentCodeService assignmentCodeService;
    private final ClientService clientService;
    private final DataBucketUtil dataBucketUtil;
    private final ShiftTypeService shiftTypeService;

    private final LocationService locationService;

    private final WorkerAppliedShiftRepository workerAppliedShiftRepository;


    public WorkerServiceImpl(final WorkerRepository workerRepository,
                             ShiftRepository shiftRepository1, final WorkerToWorkerResultDto toWorkerResultDto,
                             AgencyWorkerPropertiesRepository agencyWorkerPropertiesRepository, final WorkerDtoToWorker toWorker,
                             @org.springframework.context.annotation.Lazy ShiftService shiftService,
                             final RegisterAgentAdminFeignClient registerAgentAdminFeignClient,
                             final AgencyService agencyService, ShiftDirectorateService shiftDirectorateService,
                             AgencyRepository agencyRepository, AgencyToAgencyResultDto toAgencyResultDto,
                             AssignmentCodeService assignmentCodeService, ClientService clientService, DataBucketUtil dataBucketUtil, ShiftTypeService shiftTypeService,
                             LocationService locationService, ShiftRepository shiftRepository,
                             WorkerAppliedShiftRepository workerAppliedShiftRepository) {
        super();
        this.workerRepository = workerRepository;
        this.shiftRepository = shiftRepository1;
        this.toWorkerResultDto = toWorkerResultDto;
        this.agencyWorkerPropertiesRepository = agencyWorkerPropertiesRepository;
        this.toWorker = toWorker;
        this.shiftService = shiftService;
        this.registerAgentAdminFeignClient = registerAgentAdminFeignClient;
        this.agencyService = agencyService;
        this.shiftDirectorateService = shiftDirectorateService;
        this.agencyRepository = agencyRepository;
        this.toAgencyResultDto = toAgencyResultDto;
        this.assignmentCodeService = assignmentCodeService;
        this.clientService = clientService;
        this.dataBucketUtil = dataBucketUtil;
        this.shiftTypeService = shiftTypeService;
        this.locationService = locationService;
        this.workerAppliedShiftRepository = workerAppliedShiftRepository;
    }

    @Override
    @Transactional
    public void save(WorkerCreateDto workerCreateDto){
        try {
            Worker worker = toWorker.convert(workerCreateDto);
            Agency agency = agencyService.getOne(workerCreateDto.getAgencyId());

            Worker savedWorker = workerRepository.save(worker);
            agency.addWorker(savedWorker);
            agencyRepository.saveAndFlush(agency);

            UserDto userCreationDto = new UserDto();
            userCreationDto.setFirstName(workerCreateDto.getFirstname());
            userCreationDto.setLastName(workerCreateDto.getLastname());
            userCreationDto.setEmail(workerCreateDto.getEmail());
            userCreationDto.setAgentId(workerCreateDto.getAgencyId());
            userCreationDto.setUsername(workerCreateDto.getEmail());
            userCreationDto.setRoleId(4L);
            userCreationDto.setUserType("WORKER");
            userCreationDto.setWorkerId(savedWorker.getId());
            CompletableFuture.runAsync(() -> registerAgentAdminFeignClient.registerUserAccountFeign(
                    userCreationDto));
        } catch (FeignException exception) {
            log.error("#########{}", exception.contentUTF8());
            throw new BusinessValidationException("An error occured while registering worker.");
        }
        catch (DataIntegrityViolationException exception) {
                log.error(exception.toString());
                throw new BusinessValidationException("User already exists");
        }

    }

    @Override
    public WorkerResultDto findById(Long id) {
        return toWorkerResultDto.convert(workerRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker not found")));
    }

    @Override
    public List<WorkerResultDto> findAll() {
        return workerRepository.findAll()
                .stream()
                .map(toWorkerResultDto::convert)
                .collect(Collectors.toList());
    }




    @Override
    public boolean getWorkerConflicts(Long workerId, LocalDateTime start, LocalDateTime end) {
        List<Long> ids = shiftRepository.findAllByWorkerIdAndStatus(workerId, ShiftStatus.BOOKED)
                .stream()
                .filter(s -> (!(( start.isAfter(s.getEnd()) && end.isAfter(s.getStart()))
                        ||( end.isBefore(s.getStart()) && start.isBefore(s.getStart()))))
                ).map(Shift::getId)
                .collect(Collectors.toList());

//        ids.addAll(workerTrainingSessionRepository.findAllByWorkerIdAndTrainingStatus(workerId, WorkerTrainingSessionStatus.BOOKED)
//                .stream()
//                .filter(s -> !((end.isAfter(s.getTrainingSession().getStartDateTime()) && start.isAfter(s.getTrainingSession().getEndDateTime()))
//                        || end.isBefore(s.getTrainingSession().getStartDateTime()))
//                ).map(WorkerTrainingSession::getId)
//                .collect(Collectors.toList()));



        return ids.size()==0;
    }

    @Override
    public Page<WorkerResultDto> findAllPaged(PageRequest of) {

        List<Worker> agentList = null;
        try {
            agentList = workerRepository.findAll();
        } catch (NoResultException nre) {

        }
        Page<Worker> page = PaginationUtil.paginateWorker(of, agentList);
        return page.map(toWorkerResultDto::convert);

    }

    @Override
    public void deleteById(Long id) {
        Worker worker = getOne(id);
        try {
            workerRepository.deleteById(id);
            workerRepository.flush();
        } catch (Exception ex) {
            throw new BusinessValidationException("Worker can not be deleted.");
        }
    }

    @Override
    public void save(WorkerUpdateDto workerUpdateDto) {
        try {
            Worker worker = getOne(workerUpdateDto.getId());

            if(workerUpdateDto.getFirstname() != null) {
                worker.setFirstname(workerUpdateDto.getFirstname());
            }

            if(workerUpdateDto.getLastname() != null) {
                worker.setLastname(workerUpdateDto.getLastname());
            }
            if(workerUpdateDto.getAssignmentCodeId() != null) {
                worker.setAssignmentCode(assignmentCodeService.getOne(workerUpdateDto.getAssignmentCodeId()));
            }
            if(workerUpdateDto.getGender() != null) {
                worker.setGender(workerUpdateDto.getGender());
            }
            if(workerUpdateDto.getAddress() != null) {
                worker.setAddress(workerUpdateDto.getAddress());
            }
            if(workerUpdateDto.getPostcode() != null) {
                worker.setPostcode(workerUpdateDto.getPostcode());
            }
            if(workerUpdateDto.getEmploymentNumber() != null) {
                worker.setEmploymentNumber(workerUpdateDto.getEmploymentNumber());
            }
            if(workerUpdateDto.getCv() != null) {
                worker.setCv(workerUpdateDto.getCv());
            }
            if(workerUpdateDto.getNationality() != null) {
                worker.setNationality(workerUpdateDto.getNationality());
            }
            if(workerUpdateDto.getDob() != null) {
                worker.setDob(workerUpdateDto.getDob());
            }
            if(workerUpdateDto.getUsername() != null) {
                worker.setUsername(workerUpdateDto.getEmail());
            }
            if(workerUpdateDto.getEmail() != null) {
                worker.setUsername(workerUpdateDto.getEmail());
            }
            if(workerUpdateDto.getPhoneNumber() != null) {
                worker.setPhoneNumber(workerUpdateDto.getPhoneNumber());
            }


            workerRepository.save(worker);
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }
    }


    @Override
    public Page<AgencyResultDto> findAllAgenciesPaged(Long workerId, PageRequest of) {

        return agencyRepository.findAllAgenciesByWorkerId(workerId, of).map(toAgencyResultDto::convert);
    }

    @Override
    @Transactional
    public Worker getOne(Long id) {
        return workerRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker not found"));
    }

    @Override
    public Worker getByDeputyId(Long workerId) {
        return workerRepository.findByDeputyId(workerId);
    }

    @Override
    @Transactional
    public Worker getAuthenticatedWorker(){
        return findByEmail(
                authenticationFacadeService
                        .getAuthUsername()
        );
    }



    @Override
    @Transactional
    public Worker findByIHascoId(Long id) {
        return workerRepository.findByHascoId(id);
    }

    @Override
    @Transactional
    public Worker findByEmail(String id) {
        return workerRepository.findByEmail(id);
    }



    @Override
    public Integer findNumberOfWorkers() {
        return workerRepository.findNumberOfWorkers();
    }

    @Override
    public Page<AgencyResultDto> getMyAgency(Long workerId, Pageable of) {

        return agencyRepository.getMyAgencies(workerId, of)
                .map(toAgencyResultDto::convert);
    }




    @Override
    public WorkerStats getStats(Long id) {

        /*int numberOfRegistereAgencies = workerAgencyRepository.countWorkerAgencyByWorker_Id(id);
        int numberOfShiftsBooked = shiftRepository.countShiftsBookedByWorkerId(id);
        int numberOfClients = shiftRepository.countClientDistinctByWorker_Id(id);
        return new WorkerStats(
                numberOfRegistereAgencies,
                numberOfShiftsBooked,
                numberOfClients
        );*/
        return null;
    }

    @Override
    public Page<WorkerResultDto> findWorkersForClient(Long clientId, Long assignmentCodeId, String gender, PageRequest of) {

        if (!gender.equalsIgnoreCase("NO_PREFERENCE")) {
            return workerRepository.findMyWorkerUnderClientAgency(clientId, assignmentCodeId, gender, of)
                    .map(toWorkerResultDto::convert);
        } else {
            return workerRepository.findMyWorkerUnderClientAgency(clientId, assignmentCodeId, of)
                    .map(toWorkerResultDto::convert);
        }

    }

    @Override
    public Set<Worker> getAgencyWorkers(Long agencyId) {
        return workerRepository.findAllByAgencySetIdAndPropertiesStatus(agencyId, WorkerStatus.APPROVED);
    }



    @Override
    public void linkAgencyWorker(Long workerId, Long agencyId) {
        try {
            Worker worker = workerRepository.getOne(workerId);
            Agency agency = agencyService.getOne(agencyId);
            agency.addWorker(worker);
            agencyRepository.saveAndFlush(agency);

        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }
    }

    @Override
    public Page<ClientDto> findAllClientsPaged(Long workerId, PageRequest of) {
        return clientService.findAllPagedClients(workerId, of);
    }

    @Override
    public Object findAllWorkersUnderClientBasedOnGender(Long workerId, PageRequest of) {
        return null;
    }

    @Override
    public List<WorkerResultDto> findWorkersForAgencies(Long assignmentCode, String gender, AgencyList agencyList) {
        List<WorkerResultDto> workerResultDtos = new ArrayList<>();
        List<Long> agency = agencyList.getAgencyId();

        if (gender.equalsIgnoreCase("NO_PREFERENCE")) {

            for (Long ag : agency
            ) {
                List<WorkerResultDto> workers = workerRepository.findByAgencyIdAndAssignmentCode(ag, assignmentCode).stream()
                        .map(toWorkerResultDto::convert)
                        .collect(Collectors.toList());
                for(WorkerResultDto w: workers){
                    w.setAgencyId(ag);
                }
                workerResultDtos.addAll(workers);
            }
        } else {
            for (Long ag : agency
            ) {
                List<WorkerResultDto> workers =
                        workerRepository.
                        findByAgencyIdAndAssignmentCode(ag, assignmentCode, gender).stream()
                                .map(toWorkerResultDto::convert)
                                .collect(Collectors.toList());

                for(WorkerResultDto w: workers){
                    w.setAgencyId(ag);
                }

                workerResultDtos.addAll(workers);
            }
        }


        log.info("Workers under agency found: {}", workerResultDtos);

        IAgencyWorkerProperties props;
        List<WorkerResultDto> filteredList = workerResultDtos.stream()
                .filter(worker ->
                        WorkerStatus.APPROVED.toString().equalsIgnoreCase(findProperties(worker.getId(), worker.getAgencyId()).getStatus())
                )
                .collect(Collectors.toList());

        return filteredList;
    }




    public IAgencyWorkerProperties findProperties(Long workerId, Long agencyId) {

        IAgencyWorkerProperties properties = agencyWorkerPropertiesRepository.findAgencyWorkerProperties(workerId, agencyId);
        if(properties == null){
            properties = new IAgencyWorkerProperties() {
                @Override
                public Long getId() {
                    return null;
                }

                @Override
                public Long getAgencyId() {
                    return null;
                }

                @Override
                public Long getWorkerId() {
                    return null;
                }

                @Override
                public String getPaymentMethod() {
                    return null;
                }

                @Override
                public String getEmploymentStartDate() {
                    return null;
                }

                @Override
                public String getContractEndDate() {
                    return null;
                }

                @Override
                public String getNextCheckDate() {
                    return null;
                }

                @Override
                public String getTrainingDate() {
                    return null;
                }

                @Override
                public String getTrainingExpiry() {
                    return null;
                }

                @Override
                public String getRightToWork() {
                    return null;
                }

                @Override
                public String getDbsNumber() {
                    return null;
                }

                @Override
                public String getDbsExpiry() {
                    return null;
                }

                @Override
                public String getExpiry() {
                    return null;
                }

                @Override
                public String getRestrictions() {
                    return null;
                }

                @Override
                public String getRestrictionExpiry() {
                    return null;
                }

                @Override
                public Boolean getEligible() {
                    return null;
                }

                @Override
                public String getProof() {
                    return null;
                }

                @Override
                public String getVisa() {
                    return null;
                }

                @Override
                public String getVisaExpiry() {
                    return null;
                }

                @Override
                public String getSignDate() {
                    return null;
                }

                @Override
                public Boolean getPaperwork() {
                    return null;
                }

                @Override
                public String getApprover() {
                    return null;
                }

                @Override
                public String getPosition() {
                    return null;
                }

                @Override
                public String getComment() {
                    return null;
                }

                @Override
                public String getSigned() {
                    return null;
                }

                @Override
                public String getStatus() {
                    return null;
                }
            };
        }
        log.info("Agency worker properties, {}", properties);
        return properties;
    }

    @Override
    public List<WorkerResultDto> convertWorkers(List<Worker> worker) {
        return worker.stream()
                .map(toWorkerResultDto::convert)
                .collect(Collectors.toList());
    }



    @Override
    public List<WorkerResultDto> getAgencyApplicantsByShiftId(Long shiftId, Long agencyId) {
        List<WorkerResultDto> workerResultDto = workerRepository.findWorkerOnShiftAndAgency(shiftId, agencyId)
                .stream()
                .map(toWorkerResultDto::convert)
                .collect(Collectors.toList());

        for (WorkerResultDto i : workerResultDto) {
            i.setAgencyName(agencyService.getOne(agencyId).getName());
        }

        return workerResultDto;
    }

    @Override
    public List<WorkerResultDto> getApplicantsByShiftId(Long shiftId) {

        List<WorkerAppliedShiftRawResultDto> workerAppliedShifts = workerRepository.findApplicants(shiftId);

        List<WorkerResultDto> workerResultDtos = new ArrayList();

        for(WorkerAppliedShiftRawResultDto w: workerAppliedShifts){
            Worker worker = getOne(w.getWorkerId());
            WorkerResultDto workerResultDto =  toWorkerResultDto.convert(worker);

            WorkerAppliedShiftResultDto workerAppliedShiftResultDto = null;

            Agency agency = agencyService.getOne(w.getAgencyId());

//            workerAppliedShiftResultDto.setWorkerResultDto(workerResultDto);
            workerResultDto.setAgencyId(agency.getId());
            workerResultDto.setAgencyName(agency.getName());

            workerResultDtos.add(workerResultDto);


        }
        log.info("Shift applicants {}:", workerResultDtos);

        return workerResultDtos;
    }



//    @Override
//    public void addProfilePic(Long workerId, MultipartFile file) {
//        log.info("Request to upload a payslip");
//        String city = "profile.png";
//
//        List<String> types = new ArrayList<String>();
//
//        types.add("image/png");
//        types.add("image/jpeg");
//        types.add("image/jpg");
//
//
//        if (!types.contains(file.getContentType())) {
//            throw new BusinessValidationException("Uploaded file type is not supported. Please upload images only.{}"+file.getContentType());
//        }
//
//        if (!file.isEmpty() ) {
//            try {
//                byte[] bytes = file.getBytes();
//
//                File dir = new File(rootPath + File.separator + "worker"+ File.separator + workerId  );
//
//                if (!dir.exists())  dir.mkdirs();
//
//                // Create the file on server
//                File serverFile = new File(dir.getAbsolutePath()
//                        + File.separator + city);
//                BufferedOutputStream stream = new BufferedOutputStream(
//                        new FileOutputStream(serverFile));
//                stream.write(bytes);
//                stream.close();
//
//
//                log.info("StripeService File Location="
//                        + serverFile.getAbsolutePath());
//
//                log.info("Uploaded");
//
//            } catch (Exception e) {
//                log.info("You failed to upload");
//                log.error(e.getMessage());
//                throw new BusinessValidationException("Upload failed.");
//
//            }
//
//        } else {
//            log.info("You failed to upload");
//            throw new BusinessValidationException("Uploaded file is not empty.");
//
//        }
//    }
//


    public void addProfilePic(Long workerId, MultipartFile files) {
        List<String> types = new ArrayList<String>();





        log.info("Start file uploading service");
        List<Worker> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            try {
                String contentType = Files.probeContentType(path);
                FileDto fileDto = dataBucketUtil.uploadFile(file, "/w/"+ workerId+"/"+originalFileName, contentType);

                if (fileDto != null) {
                    Worker worker = getOne(workerId);
                    worker.setProfilePic(fileDto.getFileUrl());
                    workerRepository.save(worker);
//                    inputFiles.add(new InputFile(fileDto.getFileName(), fileDto.getFileUrl()));
                    log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
                }
            } catch (Exception e) {
                log.error("Error occurred while uploading. Error: ", e);
                throw new FileNotFoundException("Error occurred while uploading");
            }
        });

//        fileRepository.saveAll(inputFiles);
        log.debug("File details successfully saved in the database");
//        return inputFiles;
    }

    @Override
    public Page<WorkerResultDto> search(String searchCriteria, Long agencyId, PageRequest of) {
            return workerRepository.searchWorkerForAnAgency(agencyId, searchCriteria,of).map(toWorkerResultDto::convert);
    }

    @Override
    public Page<WorkerResultDto> searchApplicant(String searchCriteria, Long agencyId, PageRequest of) {
         Page<Worker> agentList2 = workerRepository.searchApplicantForAnAgency(agencyId, searchCriteria, of);
        agentList2.forEach(w->w.setActiveAgency(agencyId));
        List<WorkerResultDto> agentList   = agentList2.stream()
                .filter(w->w.getStatus() == null || w.getStatus() == WorkerStatus.APPLICANT)
                .map(toWorkerResultDto::convert)
                .collect(Collectors.toList());


        return PaginationUtil.paginateIWorker(of, agentList);
    }

    @Override
    public Page<WorkerResultDto> searchWorkerClient(String searchCriteria, Long agencyId, PageRequest of) {
        return workerRepository.searchClientWorkers(agencyId, searchCriteria, of).map(toWorkerResultDto::convert);
    }


}
