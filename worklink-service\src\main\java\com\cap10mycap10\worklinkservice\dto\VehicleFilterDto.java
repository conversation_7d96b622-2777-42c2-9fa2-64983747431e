package com.cap10mycap10.worklinkservice.dto;

import com.cap10mycap10.worklinkservice.enums.FuelType;
import com.cap10mycap10.worklinkservice.enums.Operator;
import com.cap10mycap10.worklinkservice.enums.VehicleType;
import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class VehicleFilterDto {
    private Long id;
    private Set<Long> vehicleIds; // Assuming only IDs are needed for locations
    private Operator vehicleOperator;
    private List<String> names;
    private Operator nameOperator;
    private List<String> models;
    private Operator modelOperator;
    private List<String> colors;
    private Operator colorOperator;
    private List<FuelType> fuelTypes;
    private Operator fuelTypeOperator;
    private List<VehicleType> types;
    private Operator typeOperator;
    private Set<Long> locationIds; // Assuming only IDs are needed for locations
    private Operator locationOperator;
    private Long agencyId;
}