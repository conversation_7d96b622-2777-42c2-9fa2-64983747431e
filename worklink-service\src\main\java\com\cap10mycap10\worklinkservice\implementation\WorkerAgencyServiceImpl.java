package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.service.WorkerAgencyService;
import lombok.extern.slf4j.Slf4j;


/*@Service*/
@Slf4j
/*@Transactional(rollbackFor = Exception.class)*/
public class WorkerAgencyServiceImpl implements WorkerAgencyService {

   /* private final WorkerAgencyRepository workerAgencyRepository;

    public WorkerAgencyServiceImpl(WorkerAgencyRepository workerAgencyRepository) {
        this.workerAgencyRepository = workerAgencyRepository;
    }

    @Override
    public WorkerAgency save(AgencyCreateDto agencyCreateDto) {
        return null;
    }

    @Override
    public WorkerAgency save(AgencyUpdateDto agencyUpdateDto) {
        return null;
    }

    @Override
    public WorkerAgency findById(Long workerId, Long agencyId) {
        return null;
    }

    @Override
    public List<WorkerAgency> findAll() {
        return null;
    }

    @Override
    public Page<WorkerAgency> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public void deleteById(Long workerId, Long agencyId) {

    }

    @Override
    public Agency getOne(Long workerId, Long agencyId) {
        return null;
    }

    @Override
    public Page<Worker> findAllWorkersPaged(Long workerId, PageRequest of) {
        return null;
    }

    @Override
    public Page<Agency> findAllAgenciesPaged(Long agencyId, PageRequest of) {
        return null;
    }

    @Override
    public List<WorkerAgency> findWorkersByAgency(Long agencyId) {
        *//*return workerAgencyRepository.findWorkerAgenciesByAgency_Id(agencyId);*//*
        return null;
    }

    @Override
    public List<String> findEmailsByAgency(Long agencyId) {
        *//*return workerAgencyRepository.findEmailsByAgency_Id(agencyId);*//*
        return null;
    }*/
}
