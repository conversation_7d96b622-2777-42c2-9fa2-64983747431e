package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDepositDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.service.EmailSenderFactory;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.helpers.PaynowHelper;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.mapper.asset.agency.VehicleToVehicleDto;
import com.cap10mycap10.worklinkservice.mapper.invoice.InvoiceToInvoiceResult;
import com.cap10mycap10.worklinkservice.mapper.shift.ShiftToShiftResultDto;
import com.cap10mycap10.worklinkservice.mapper.vehiclebooking.VehicleBookingDepositToVehicleBookingDepositDto;
import com.cap10mycap10.worklinkservice.mapper.vehiclebooking.VehicleBookingToVehicleBookingDto;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.service.*;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.PaymentIntent;
import com.stripe.param.PaymentIntentCreateParams;
import com.stripe.param.checkout.SessionRetrieveParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import zw.co.paynow.exceptions.HashMismatchException;
import zw.co.paynow.responses.StatusResponse;
import zw.co.paynow.responses.WebInitResponse;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


import static com.cap10mycap10.worklinkservice.config.AppConfiguration.dateTimeFormat;
import static java.util.Objects.nonNull;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class VehicleBookingServiceImpl implements VehicleBookingService {
    @Value("${env.companyName}")
    private String companyName;
    @Value("${env.supportEmail}")
    private String supportEmail;
    @Value("${env.systemCurrency}")
    private String systemCurrency;
    @Value("${env.systemCurrency3}")
    private String systemCurrency3;

    @Value("${env.STRIPE_SECRET_KEY}")
    private String STRIPE_SECRET_KEY;

    @Value("${env.STRIPE_PUBLIC_KEY}")
    private String STRIPE_PUBLIC_KEY;
    @Value("${env.website}")
    private String website;
    @Autowired
    private VehicleBookingToVehicleBookingDto toVehicleBookingDto;
    @Autowired
    private ClientService clientService;
    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private VehicleToVehicleDto toAssetDto;
    @Autowired
    private AgencyRepository agencyRepository;
    @Autowired
    private InvoiceToInvoiceResult toInvoiceResult;
    @Autowired
    private InvoiceRepository invoiceRepository;
    @Autowired
    private EmailService emailService;
    @Autowired
    private EmailSenderFactory emailSenderFactory;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private PromotionService promotionService;
    @Autowired
    private PushNotification pushNotification;
    @Autowired
    private WorkerService workerService;
    @Autowired
    private ShiftDirectorateService shiftDirectorateService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private VehicleBookingRepository vehicleBookingRepository;
    @Autowired
    private ShiftRepository transportBookingRepository;
    @Autowired
    private ShiftToShiftResultDto toBookingResultDto;
    @Autowired
    private AuthenticationFacadeService authenticationFacadeService;
    @Autowired
    private PaynowHelper paynowHelper;

    @Autowired
    private VehicleBookingDepositRepository vehicleBookingDepositRepository;

    @Autowired
    private VehicleBookingDepositToVehicleBookingDepositDto toVehicleBookingDepositDto;

    @Autowired
    private LocationService locationService;

    @Autowired
    private TaxCalculationService taxCalculationService;

    @Autowired
    private AgencySettingsService agencySettingsService;


    private Client handleClient(VehicleBookingDto vehicleBookingDto) {
        Client client = clientRepository.findByEmail(vehicleBookingDto.getEmail());
        Agency agency = agencyRepository.getOne(vehicleBookingDto.getVehicle().getAgency().getId());
        if (!nonNull(client)) {
            client = new Client();
            client.setName(vehicleBookingDto.getFirstname() + " " + vehicleBookingDto.getSurname());
            client.setEmail(vehicleBookingDto.getEmail());
            client.setBillingEmail(vehicleBookingDto.getEmail());
            client.setTelephone(vehicleBookingDto.getPhone());
            client.setStatus(Status.ACTIVE);
            client.getAgencies().add(agency);
            client = clientRepository.save(client);

        } else if (!nonNull(client.getTelephone())) {
            client.setTelephone(vehicleBookingDto.getPhone());
            client.getAgencies().add(agency);
            client = clientRepository.save(client);
        }

        agency.addClient(client);
        agencyRepository.save(agency);


        return client;
    }

    private Promotion handlePromotion(Vehicle vehicle, VehicleBooking vehicleBooking, VehicleBookingDto vehicleBookingDto) {
        Promotion promotion = null;
        if (nonNull(vehicleBookingDto.getPromoCode())) {
            promotion = promotionService.getOneByCode(vehicleBookingDto.getPromoCode());
        } else {
            promotion = promotionService.getLatestValidPublicPromotion(vehicleBooking, vehicleBookingDto.getSource()==FrontPlatform.KARLINK);
        }

        if (nonNull(promotion)) {
            if (nonNull(promotion.getCode())) {
                vehicle.validatePromo(promotion, vehicleBooking);
            }else {
                try{
                    vehicle.validatePromo(promotion, vehicleBooking);
                }catch (BusinessValidationException e){
                    log.error("Promo code validation failed: {}", e.getMessage());
                }
            }
        }
        return promotion;
    }

    private Invoice handleInvoice(VehicleBooking vehicleBooking, Promotion promotion, VehicleBookingDto vehicleBookingDto) {

        if (vehicleBookingDto.getByAgency() && nonNull(vehicleBookingDto.getDiscount()) && vehicleBookingDto.getDiscount().compareTo(BigDecimal.ZERO) > 0) {

        }else vehicleBooking.setPromotion(promotion);

        vehicleBooking.generateInvoice();

        Invoice invoice = (new ArrayList<>(vehicleBooking.getInvoices())).get(0);

        // Apply tax calculations to the invoice
        applyTaxCalculationsToInvoice(invoice, vehicleBooking);




        if (vehicleBookingDto.getByAgency()) {
            if (nonNull(vehicleBookingDto.getDiscount()) && vehicleBookingDto.getDiscount().compareTo(BigDecimal.ZERO) > 0) {
                invoice.setDiscount(vehicleBookingDto.getDiscount());
            }else vehicleBooking.setPromotion(promotion);

            if (vehicleBookingDto.getPaidAmount() > 0) {
                Payment payment = new Payment(vehicleBookingDto.getPaidAmount(), "Manual payment");
                invoice.payInvoice(payment);
                log.info("Payment was successful ref: {} Invoice id: {}", payment.getRef(), invoice.getId());
            }
        }

        if(!vehicleBooking.getByAgency())
            invoice.addBookingFee();

        return invoice;
    }

    private VehicleBooking finalizeBooking(VehicleBooking vehicleBooking, VehicleBookingDto vehicleBookingDto) {
        if (vehicleBookingDto.getByAgency()) {
            vehicleBooking.setStatus(VehicleBookingStatus.BOOKED);
            VehicleBooking booking = vehicleBookingRepository.save(vehicleBooking);

            Invoice invoice = booking.getInvoices().stream().findFirst().get();
            try {
                emailBookingReserved(invoice);
                return booking;
            } catch (Exception e) {
                log.error("A minor error occurred while creating booking emails but booking and payment were saved: {}\n", e.toString());
                log.error(Arrays.toString(e.getStackTrace()));
                return booking;
            }
        }

        VehicleBooking booking = vehicleBookingRepository.save(vehicleBooking);
        if (!vehicleBookingDto.getByAgency()) {
            Invoice invoice = booking.getInvoices().stream().findFirst().get();
//            invoice.setAgency(booking.getVehicle().getAgency());
            instantiateOnlinePayment(invoice, vehicleBookingDto.getGatewayType(), vehicleBookingDto.isFullPayment());
        }

        Invoice invoice = booking.getInvoices().stream().findFirst().get();

        vehicleBooking.getVehicle().getAgency().addClient(vehicleBooking.getClient());
        agencyRepository.save(vehicleBooking.getVehicle().getAgency());
        log.info("Finalized booking, id: {} invoice id: {}, poll url: {}", booking.getId(), invoice.getId(), invoice.getPollUrl());
        return booking;
    }

    @Override
    public VehicleBookingDto book(VehicleBookingDto vehicleBookingDto) {
        vehicleBookingDto.validateBooking();

        Vehicle vehicle = vehicleService.getOne(vehicleBookingDto.getVehicleId());

        vehicleBookingDto.setVehicle(toAssetDto.convert(vehicle, vehicleBookingDto.getSource()==FrontPlatform.KARLINK));
        Client client = handleClient(vehicleBookingDto);

        vehicleBookingDto.setClientModel(client);

        Location location = locationService.getOne(vehicleBookingDto.getLocationId());
        vehicleBookingDto.setLocation(location);

        VehicleBooking vehicleBooking = vehicle.book(vehicleBookingDto);
        Promotion promotion = handlePromotion(vehicle, vehicleBooking, vehicleBookingDto);

        Invoice invoice = handleInvoice(vehicleBooking, promotion, vehicleBookingDto);
        vehicleBooking.addInvoice(invoice);


        VehicleBooking booking = finalizeBooking(vehicleBooking, vehicleBookingDto);

        return toVehicleBookingDto.apply(booking);
    }

    @Override
    public VehicleBookingDto getBookingQuote(VehicleBookingDto vehicleBookingDto) {
        vehicleBookingDto.validateBooking();

        Vehicle vehicle = vehicleService.getOne(vehicleBookingDto.getVehicleId());
        vehicleBookingDto.setVehicle(toAssetDto.convert(vehicle, vehicleBookingDto.getSource()==FrontPlatform.KARLINK));

        Client client = handleClient(vehicleBookingDto);

        vehicleBookingDto.setLocation(vehicle.getLocation());
        vehicleBookingDto.setClientModel(client);


        VehicleBooking vehicleBooking = vehicle.book(vehicleBookingDto);
        Promotion promotion = handlePromotion(vehicle, vehicleBooking, vehicleBookingDto);

        Invoice invoice = handleInvoice(vehicleBooking, promotion, vehicleBookingDto);

        vehicleBooking.addInvoice(invoice);


        return toVehicleBookingDto.apply(vehicleBooking);
    }




    @Override
    public VehicleBookingDto update(VehicleBookingDto vehicleBookingDto) {
        VehicleBooking booking = getOne(vehicleBookingDto.getId());

        VehicleDto vehicleDto = vehicleService.findById(vehicleBookingDto.getVehicleId());
        Vehicle vehicle = vehicleService.getOne(vehicleBookingDto.getVehicleId());
        vehicleBookingDto.setVehicle(vehicleDto);
        if(nonNull(vehicleBookingDto.getFirstname()))booking.setFirstname(vehicleBookingDto.getFirstname());
        if(nonNull(vehicleBookingDto.getSurname()))booking.setSurname(vehicleBookingDto.getSurname());
        if(nonNull(vehicleBookingDto.getStart()))booking.setStart(vehicleBookingDto.getStart().atZone(vehicle.getLocation().getTimeZoneId()));
        if(nonNull(vehicleBookingDto.getEnd()))booking.setEnd(vehicleBookingDto.getEnd().atZone(vehicle.getLocation().getTimeZoneId()));
        if(nonNull(vehicleBookingDto.getVehicle()))booking.setVehicle(vehicle);
        if(nonNull(vehicleBookingDto.getEmail()))booking.setEmail(vehicleBookingDto.getEmail());
        if(nonNull(vehicleBookingDto.getPhone()))booking.setPhone(vehicleBookingDto.getPhone());

        booking = vehicleBookingRepository.save(booking);

        return toVehicleBookingDto.apply(booking);
    }



    public Invoice instantiateOnlinePayment(Invoice invoice, PaymentGatewayType gatewayType, boolean fullPayment){
        invoice.setPaymentGatewayType(gatewayType);


        switch (gatewayType) {
            case PAYNOW:
                String agencyBaseCurrency = invoice.getAgency().getBaseCurrency();
                if(!agencyBaseCurrency.equalsIgnoreCase("USD")) throw new BusinessValidationException("Paynow only supports USD payments at the moment, please use Stripe for other currencies. Rental expected in "+agencyBaseCurrency +" currency.");
                return payViaPayNow(invoice, fullPayment);
            case STRIPE:
                return payViaStripe(invoice, fullPayment);
            default:
                throw new BusinessValidationException("Add a payment gateway field");
        }
    }

    public Invoice payViaPayNow(Invoice invoice, Boolean fullPayment){

        WebInitResponse response = paynowHelper.paynowBookingPay(invoice, fullPayment);

        if(!nonNull(response.getPollUrl())) {
            log.error("Paynow did not respond with pollUrl, check details below");
            log.error(String.valueOf(invoice.getVehicleBooking()));
            log.error(String.valueOf(response));
            log.error(String.valueOf(invoice));
            response = paynowHelper.paynowBookingPay(invoice, fullPayment);
        }

        if(!nonNull(response.getPollUrl()))
            throw new BusinessValidationException("Failed to create a booking, please try again");

        invoice.setPollUrl(response.getPollUrl());
        invoiceRepository.save(invoice);

        log.info("Created booking, id: "+invoice.getVehicleBooking().getId()+" invoice id: {}, poll url: {}", invoice.getId(), invoice.getPollUrl());
        invoice.setRedirectUrl(response.getRedirectURL());
        return invoice;
    }

    public Invoice payViaStripe(Invoice invoice, Boolean fullPayment){

        Stripe.apiKey = STRIPE_SECRET_KEY;


        BigDecimal multiplicative = fullPayment? BigDecimal.valueOf(1) :BigDecimal.valueOf(0.07);

//        CreatePayment postBody = gson.fromJson(request, CreatePayment.class);

        String agencyBaseCurrency = invoice.getAgency().getBaseCurrency();
        if (agencyBaseCurrency == null) {
            agencyBaseCurrency = systemCurrency3; // Default fallback
        }

        PaymentIntentCreateParams params =
                PaymentIntentCreateParams.builder()
                        .setDescription("Invoice#"+invoice.getId())
//                        .setCustomer(invoice.getClient().getName()+" "+invoice.getClient().getEmail())
                        .setAmount(
                                invoice.getSubTotalAmount()
                                        .multiply(BigDecimal.valueOf(100))
                                        .multiply(multiplicative)
                                        .longValue())
                        .setCurrency(agencyBaseCurrency.toLowerCase())
                        .putMetadata("invoice_id", String.valueOf(invoice.getId()))
                        // In the latest version of the API, specifying the `automatic_payment_methods` parameter is optional because Stripe enables its functionality by default.
                        .setAutomaticPaymentMethods(
                                PaymentIntentCreateParams.AutomaticPaymentMethods
                                        .builder()
                                        .setEnabled(true)
                                        .build()
                        )
                        .build();

        // Create a PaymentIntent with the order amount and currency
        PaymentIntent paymentIntent = null;
        try {
            paymentIntent = PaymentIntent.create(params);
        } catch (StripeException e) {
            throw new RuntimeException(e);
        }

        invoice.setClientSecret(paymentIntent.getClientSecret());
        return invoice;
    }


    @Override
    public VehicleBookingDto fulfillStripeCheckout(Charge sessionId) throws StripeException {

        Stripe.apiKey = STRIPE_PUBLIC_KEY;

        System.out.println("Fulfilling Checkout Session " + sessionId);

        // TODO: Make this function safe to run multiple times,
        // even concurrently, with the same session ID

        // TODO: Make sure fulfillment hasn't already been
        // peformed for this Checkout Session

        SessionRetrieveParams params =
                SessionRetrieveParams.builder()
                        .addExpand("line_items")
                        .build();

//        Session checkoutSession = Session.retrieve(sessionId, params, null);

        // Check the Checkout Session's payment_status property
        // to determine if fulfillment should be peformed
        if (Objects.equals(sessionId.getStatus(), "succeeded")) {
            Long invoiceId = Long.valueOf(sessionId.getMetadata().get("invoice_id"));
            Invoice quote = invoiceService.getOne(invoiceId);

            //            This is only the car rental component that is 4/7 of the payment

            BigDecimal total = BigDecimal.valueOf(sessionId.getAmount()).divide(BigDecimal.valueOf(100));


            Payment payment = new Payment(sessionId, total.multiply(BigDecimal.valueOf(0.5714285714285714)));
            quote.payInvoice(payment);
            quote.getVehicleBooking().setStatus(VehicleBookingStatus.BOOKED);
            VehicleBooking booking = vehicleBookingRepository.save(quote.getVehicleBooking());
            log.info("Payment was successful ref:" +payment.getRef()+ "Invoice id: "+ quote.getId());

            try{
                emailBookingPaid(quote);
                emailAgencyBookingReceived(quote.getVehicleBooking());
                return toVehicleBookingDto.apply(booking);
            }catch (Exception e){
                emailBookingPaid(quote);
                log.error("A minor error ocurred while creating booking but booking and payment were saved:" +e.toString()+"\n");
                log.error(Arrays.toString(e.getStackTrace()));
                return toVehicleBookingDto.apply(booking);
            }


            // TODO: Perform fulfillment of the line items

            // TODO: Record/save fulfillment status for this
        }

        log.info("Payment failed");
        return null;
    }


    /**
     * Helper method to generate promotion details text for emails
     */
    private String getPromotionDetailsText(Promotion promotion) {
        if (promotion == null) {
            return "";
        }

        StringBuilder promoDetails = new StringBuilder("\nPromotion Applied:\n");
        promoDetails.append("Promotion: ").append(promotion.getTitle()).append("\n");

        if (promotion.getCode() != null && !promotion.getCode().isEmpty()) {
            promoDetails.append("Promo Code: ").append(promotion.getCode()).append("\n");
        }

        if (promotion.getDescription() != null && !promotion.getDescription().isEmpty()) {
            promoDetails.append("Description: ").append(promotion.getDescription()).append("\n");
        }

        switch (promotion.getPromotionType()) {
            case DISCOUNT:
                promoDetails.append("Discount: ").append(promotion.getDiscount()).append("\n");
                break;
            case EXTRA_DAYS:
                promoDetails.append("Extra Days: ").append(promotion.getExtraDays()).append("\n");
                break;
            case EXTRA_MILEAGE:
                promoDetails.append("Extra Mileage: ").append(promotion.getExtraMileage()).append("\n");
                break;
            case OTHER_AWARD:
                if (promotion.getHtmlBody() != null && !promotion.getHtmlBody().isEmpty()) {
                    promoDetails.append("Special Offer: Please see details on our website\n");
                }
                break;
        }

        return promoDetails.toString();
    }

    private void emailBookingPaid(Invoice invoice) {
        Agency agency = invoice.getVehicleBooking().getVehicle().getAgency();

        // Get agency-specific branding
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agency.getId());
        String agencyName = config.getFromName();
        String agencyWebsite = config.getWebsiteUrl() != null ? config.getWebsiteUrl() : website;

        String title = "Car booking payment was successful";
        String buttonLink = agencyWebsite + "/bookings/" +invoice.getVehicleBooking().getId() + "?email="+invoice.getClient().getEmail();
        StringBuilder bodyBuilder = new StringBuilder("Dear "+invoice.getVehicleBooking().getFirstname()+",\n" +
                "Thank you for choosing "+agencyName+" for your booking! We are pleased to confirm your booking for your upcoming car rental. Below are the details of your reservation:\n" +
                "\n" +
                "Booking Details:\n" +
                "Booking Number: "+invoice.getVehicleBooking().getId()+"\n" +

                "Pick-up Date: "+dateTimeFormat.format(invoice.getVehicleBooking().getStartZoned())+"\n" +

                "Pick-up Location: "+(nonNull(invoice.getVehicleBooking().getLocation()) ?invoice.getVehicleBooking().getLocation().toString() : "")+"\n" +

                "Drop-off Date: "+dateTimeFormat.format(invoice.getVehicleBooking().getEndZoned())+"\n" +

                "Car Model: "+invoice.getVehicleBooking().getVehicle().getModel()+"\n");

        // Add promotion details if a promotion was applied
        if (nonNull(invoice.getVehicleBooking().getPromotion())) {
            bodyBuilder.append(getPromotionDetailsText(invoice.getVehicleBooking().getPromotion()));
        }

        bodyBuilder.append("\nAmount Paid: "+systemCurrency+" "+invoice.getTotalAmountPaid().multiply(BigDecimal.valueOf(1.75)).setScale(2, RoundingMode.HALF_UP)+"\n\n" +
                "Amount Due: "+systemCurrency+" "+invoice.getAmountDue().setScale(2, RoundingMode.HALF_UP)+"\n\n" +
                "<a href='"+buttonLink+"' style='text-decoration:none;'>"
                + "<button >View Booking & Invoice</button>"
                + "</a><br>"
                + "\n Or visit to view booking details and invoice: <a href='"+buttonLink+"'>"
                +buttonLink+"</a>\n" +
                "\n" +
                "Important Information:\n" +
                "Please bring a valid driver's license when you pick up the car.\n" +

                "If you need to make any changes to your booking, please contact the vehicle provider at "+invoice.getAgency().getEmail()+ (nonNull(invoice.getAgency().getTelephone())?" or "+invoice.getAgency().getTelephone():"")+".\n" +
                "\n" +
                "We look forward to serving you and hope you have a great experience with our service. If you have any questions or need further assistance, please do not hesitate to reach out to us.\n" +
                "Safe travels!\n");

//        CompletableFuture.runAsync(()->
                emailService.sendEmailAsUserReply(Collections.singletonList(invoice.getVehicleBooking().getClient().getEmail()), title,  bodyBuilder.toString(), agency.getBillingEmail(),agency.getName(), agency.getId())
        ;
    }
    private void emailBookingReserved(Invoice invoice) {
        Agency agency = invoice.getVehicleBooking().getVehicle().getAgency();

        // Get agency-specific branding
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agency.getId());
        String agencyName = config.getFromName();
        String agencyWebsite = config.getWebsiteUrl() != null ? config.getWebsiteUrl() : website;

        String title = "Car booking reservation was successful";
        String buttonLink = agencyWebsite + "/bookings/" +invoice.getVehicleBooking().getId() + "?email="+invoice.getClient().getEmail();
        StringBuilder bodyBuilder = new StringBuilder("Dear "+invoice.getVehicleBooking().getFirstname()+",\n" +
                "Thank you for choosing "+agencyName+" for your booking! We are pleased to confirm your booking for your upcoming car rental. Below are the details of your reservation:\n" +
                "\n" +
                "Booking Details:\n" +
                "Booking Number: "+invoice.getVehicleBooking().getId()+"\n" +
                "Pick-up Date: "+dateTimeFormat.format(invoice.getVehicleBooking().getStartZoned())+"\n" +

                "Pick-up Location: "+(nonNull(invoice.getVehicleBooking().getLocation()) ?invoice.getVehicleBooking().getLocation().toString() : "")+"\n" +

                "Drop-off Date: "+dateTimeFormat.format(invoice.getVehicleBooking().getEndZoned())+"\n" +

                "Car Model: "+invoice.getVehicleBooking().getVehicle().getModel()+"\n");

        // Add promotion details if a promotion was applied
        if (nonNull(invoice.getVehicleBooking().getPromotion())) {
            bodyBuilder.append(getPromotionDetailsText(invoice.getVehicleBooking().getPromotion()));
        }

        bodyBuilder.append("\nTotal Amount: "+systemCurrency+" "+invoice.getTotalAmount()+"\n\n" +
                "<a href='"+buttonLink+"' style='text-decoration:none;'>"
                + "<button >View Booking & Invoice</button>"
                + "</a><br>"
                + "\n Or visit to view booking details and invoice: <a href='"+buttonLink+"'>"
                +buttonLink+"</a>\n" +
                "\n" +
                "Important Information:\n" +
                "Please bring a valid driver's license when you pick up the car.\n" +

                "If you need to make any changes to your booking, please contact the vehicle provider at "+invoice.getAgency().getEmail()+ (nonNull(invoice.getAgency().getTelephone())?" or "+invoice.getAgency().getTelephone():"")+".\n" +
                "\n" +
                "We look forward to serving you and hope you have a great experience with our service. If you have any questions or need further assistance, please do not hesitate to reach out to us.\n" +
                "Safe travels!\n");

        emailService.sendEmailAsUserReply(Collections.singletonList(invoice.getVehicleBooking().getClient().getEmail()), title, bodyBuilder.toString(), agency.getEmail(), agency.getName(), agency.getId());
    }



    private void emailAgencyBookingReceived(VehicleBooking booking) {
        Agency agency = booking.getVehicle().getAgency();

        // Get agency-specific branding
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agency.getId());
        String agencyName = config.getFromName();

        String title = booking.getVehicle().getName()+ " "+booking.getVehicle().getModel()+" has been Rented through "+agencyName;
        StringBuilder bodyBuilder = new StringBuilder("Dear "+booking.getVehicle().getAgency().getName()+",\n" +
                "\n" +
                "We are pleased to inform you that one of your vehicles has been successfully rented through our platform, "+agencyName+".\n" +
                "\n" +
                "Rental Details:\n" +
                "- Vehicle: "+booking.getVehicle().getName()+" "+booking.getVehicle().getModel()+"\n" +
                "- Booking ID: "+ booking.getId()+"\n" +
                "- Rental Period: "+dateTimeFormat.format(booking.getStartZoned())+" to "+dateTimeFormat.format(booking.getEndZoned())+"\n" +
                "- Pickup Location: "+(nonNull(booking.getLocation())?booking.getLocation():"")+"\n" +
                "- Hirer Details: "+booking.getFirstname()+" "+booking.getSurname()+", "+booking.getEmail()+", "+booking.getPhone()+"\n");

        // Add promotion details if a promotion was applied
        if (nonNull(booking.getPromotion())) {
            bodyBuilder.append("\nPromotion Applied:\n");
            bodyBuilder.append("- Promotion: ").append(booking.getPromotion().getTitle()).append("\n");
            if (booking.getPromotion().getCode() != null && !booking.getPromotion().getCode().isEmpty()) {
                bodyBuilder.append("- Promo Code: ").append(booking.getPromotion().getCode()).append("\n");
            }

            switch (booking.getPromotion().getPromotionType()) {
                case DISCOUNT:
                    bodyBuilder.append("- Discount: ").append(booking.getPromotion().getDiscount()).append("%%\n");
                    break;
                case EXTRA_DAYS:
                    bodyBuilder.append("- Extra Days: ").append(booking.getPromotion().getExtraDays()).append("\n");
                    break;
                case EXTRA_MILEAGE:
                    bodyBuilder.append("- Extra Mileage: ").append(booking.getPromotion().getExtraMileage()).append("\n");
                    break;
                case OTHER_AWARD:
                    if (booking.getPromotion().getHtmlBody() != null && !booking.getPromotion().getHtmlBody().isEmpty()) {
                        bodyBuilder.append("- Special Offer: See details on our website\n");
                    }
                    break;
            }
        }

        bodyBuilder.append("\n" +
                "At "+companyName+", we strive to provide seamless rental experiences for rental companies, individuals and clients.\n" +
                "\n" +
                "Next Steps:\n" +
                "1. Please prepare the vehicle for the rental period.\n" +
                "2. Ensure that the vehicle is clean, fuelled, and in excellent condition.\n" +
                "3. Contact the hirer to confirm pickup details and answer any questions they might have.\n" +
                "4. On dispatching the vehicle ensure you verify the identity of the hirer.\n" +
                "5. Follow the <a href='https://mykarlink.com/community-guidelines'>community guidelines</a> and <a href='https://mykarlink.com/terms-and-conditions'>terms and conditions</a>.\n" +
                "\n" +
                "Thank you for partnering with "+agencyName+". We look forward to continuing our successful collaboration. Should you have any questions or need further assistance, please do not hesitate to contact us.\n" +
                "\n");

          emailService.sendEmailAsUserReply(Collections.singletonList(booking.getVehicle().getAgency().getEmail()), title, bodyBuilder.toString(), agency.getBillingEmail(), agency.getName(), agency.getId());
    }


    private void emailRatingRequest(VehicleBooking booking) {
        Agency agency = booking.getVehicle().getAgency();

        // Get agency-specific branding
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agency.getId());
        String agencyName = config.getFromName();
        String agencyWebsite = config.getWebsiteUrl() != null ? config.getWebsiteUrl() : "https://mykarlink.com";

        String title = "We hope you had an amazing trip - We Value Your Feedback!";
        String buttonLink = agencyWebsite + "/bookings/" + booking.getId() + "/rate";
        String body = String.format(
                "Dear "+booking.getFirstname()+",<br><br>" +
                        "We hope you had a pleasant trip with your rental car from "+agencyName+". Thank you for choosing us for your travel needs. We are delighted to have served you and hope that your experience was smooth and enjoyable.<br><br>" +
                        "Return Confirmation:<br><br>" +
                        "Booking Number: "+booking.getId()+"<br><br>" +
                        "Car Model: "+booking.getVehicle().getModel()+"<br><br>" +
                        "We would love to hear about your experience with our service. Your feedback is invaluable to us and helps us improve our services for future customers. Please take a moment to fill out our feedback form by clicking the button below:<br><br>" +
                        "<a href='"+buttonLink+"' style='text-decoration:none;'>" +
                        "<button>Rate Your Experience</button>" +
                        "</a><br><br>" +
                        "If the button above does not work, please use the following link: <a href='"+ buttonLink+"'>"+buttonLink+"</a><br><br>" +
                        "If you have any questions or need further assistance, please do not hesitate to reach out to us at "+ supportEmail+".<br><br>" +
                        "Thank you once again for choosing "+ companyName+". We look forward to serving you again in the future.<br><br>" +
                        ""
        );

        CompletableFuture.runAsync(() ->
                emailService.sendEmailAsUserReply(Collections.singletonList(booking.getEmail()), title, body, agency.getBillingEmail(), agency.getName(), agency.getId())
        );
    }


    private void emailBookingPaymentFailed(VehicleBooking booking) {
        Agency agency = booking.getVehicle().getAgency();

        // Get agency-specific branding
        EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agency.getId());
        String agencyName = config.getFromName();
        String agencyWebsite = config.getWebsiteUrl() != null ? config.getWebsiteUrl() : website;

        String title = "We Couldn't Confirm Your Car Booking";
        String body = "Dear "+booking.getFirstname()+",\n" +
                "\n" +
                "Thank you for choosing "+agencyName+" for your car rental needs.\n" +
                "\n" +
                "We regret to inform you that your recent attempt to reserve a vehicle was unsuccessful because the payment did not go through. As a result, your booking has been cancelled.\n" +
                "\n\n" +
                "- Vehicle: "+booking.getVehicle().getName()+" "+booking.getVehicle().getModel()+"\n" +
                "- Rental Period: "+dateTimeFormat.format(booking.getStartZoned())+" to "+dateTimeFormat.format(booking.getEndZoned())+"\n" +
                "- Pickup Location: "+(nonNull(booking.getLocation())?booking.getLocation():"")+"\n\n" +

                "We understand that this might be inconvenient, and we apologize for any inconvenience caused. Please feel free to continue your search for a vehicle on our website.\n" +
                "\n" +
                "If you encounter any issues or need further assistance, please do not hesitate to contact us.\n" +
                "\n" +
                "We look forward to assisting you with your car rental needs.\n" ;

        CompletableFuture.runAsync(()->
                emailService.sendEmailAsUserReply(Collections.singletonList(booking.getEmail()), title, body, agency.getBillingEmail(), agency.getName(), agency.getId())
        );
    }

    private void emailBookingCancelled(VehicleBooking booking) {
        String title = "Your Car Booking Has Been Cancelled";
        String body = "Dear "+booking.getFirstname()+",\n" +
                "\n" +
                "We are writing to confirm that your car booking with "+companyName+" has been cancelled.\n" +
                "\n" +
                "Booking Details:\n" +
                "- Booking Number: "+booking.getId()+"\n" +
                "- Vehicle: "+booking.getVehicle().getName()+" "+booking.getVehicle().getModel()+"\n" +
                "- Rental Period: "+dateTimeFormat.format(booking.getStartZoned())+" to "+dateTimeFormat.format(booking.getEndZoned())+"\n" +
                "- Pickup Location: "+(nonNull(booking.getLocation())?booking.getLocation():"")+"\n" +
                (nonNull(booking.getCancelReason()) ? "- Cancellation Reason: "+booking.getCancelReason()+"\n" : "") +
                "\n";

        // Add refund information if applicable
        if (booking.getByAgency()) {
            body += "As the cancellation was initiated by the vehicle provider, a refund will be processed according to our refund policy. Please allow 5-7 business days for the refund to appear in your account.\n\n";
        } else {
            body += "As the cancellation was initiated by you, please note that our cancellation policy applies. ";

            // Check if the booking was within 48 hours
            if (nonNull(booking.getCancelReason())) {
                body += "Since your cancellation was made close to the rental start date, our support team has been notified and will contact you if needed.\n\n";
            } else {
                body += "No refund will be processed for this cancellation.\n\n";
            }
        }

        body += "If you have any questions or need further assistance, please do not hesitate to contact our support team at "+supportEmail+".\n" +
                "\n" +
                "Thank you for your understanding.\n";

        // Send email to a client
        String finalBody = body;

        Agency agency = booking.getVehicle().getAgency();

        CompletableFuture.runAsync(()->
                emailService.sendEmailAsUserReply(Collections.singletonList(booking.getEmail()), title,  finalBody.toString(), agency.getBillingEmail(),agency.getName(), booking.byMyKarlink() ? null : agency.getId())
        );

        // Send email to agency
        String agencyTitle = "Vehicle Booking Cancellation Notification";
        String agencyBody = "Dear Vehicle Provider,\n" +
                "\n" +
                "This is to notify you that a booking for one of your vehicles has been cancelled.\n" +
                "\n" +
                "Booking Details:\n" +
                "- Booking Number: "+booking.getId()+"\n" +
                "- Vehicle: "+booking.getVehicle().getName()+" "+booking.getVehicle().getModel()+"\n" +
                "- Rental Period: "+dateTimeFormat.format(booking.getStartZoned())+" to "+dateTimeFormat.format(booking.getEndZoned())+"\n" +
                "- Client: "+booking.getFirstname()+" "+booking.getSurname()+"\n" +
                "- Client Email: "+booking.getEmail()+"\n" +
                (nonNull(booking.getCancelReason()) ? "- Cancellation Reason: "+booking.getCancelReason()+"\n" : "") +
                "\n" +
                "The booking has been marked as cancelled in our system.\n" +
                "\n" +
                "If you have any questions, please contact our support team at "+supportEmail+".\n";


        CompletableFuture.runAsync(()->
                emailService.sendEmailAsUserReply(Collections.singletonList(booking.getEmail()), title,  agencyBody.toString(), agency.getBillingEmail(),agency.getName(), booking.byMyKarlink() ? null : agency.getId())
        );

        // Send notification to support if cancellation is within 48 hours
        if (nonNull(booking.getCancelReason())) {
            String supportTitle = "Urgent: Booking Cancellation Within 48 Hours";
            String supportBody = "A booking has been cancelled within 48 hours of the rental start time.\n" +
                    "\n" +
                    "Booking Details:\n" +
                    "- Booking Number: "+booking.getId()+"\n" +
                    "- Vehicle: "+booking.getVehicle().getName()+" "+booking.getVehicle().getModel()+"\n" +
                    "- Rental Period: "+dateTimeFormat.format(booking.getStartZoned())+" to "+dateTimeFormat.format(booking.getEndZoned())+"\n" +
                    "- Client: "+booking.getFirstname()+" "+booking.getSurname()+"\n" +
                    "- Client Email: "+booking.getEmail()+"\n" +
                    "- Vehicle Provider: "+booking.getVehicle().getAgency().getName()+"\n" +
                    "- Vehicle Provider Email: "+booking.getVehicle().getAgency().getEmail()+"\n" +
                    "- Cancelled By: "+(booking.getByAgency() ? "Vehicle Provider" : "Client")+"\n" +
                    "- Cancellation Reason: "+booking.getCancelReason()+"\n" +
                    "\n" +
                    "Please review this cancellation and take appropriate action if needed.\n";

            CompletableFuture.runAsync(()->
                    emailService.sendEmailAsUserReply(Collections.singletonList(booking.getEmail()), supportTitle,  supportBody.toString(), agency.getBillingEmail(),agency.getName(), booking.byMyKarlink() ? null : agency.getId())
            );
        }
    }


    @Override
    public void checkForUnpaidBookings(){
        List<VehicleBooking> bookings = vehicleBookingRepository.findByStatusAndCreatedDateBefore(VehicleBookingStatus.RESERVED, Instant.now().minus(4, ChronoUnit.MINUTES));
        log.info("Found {} reserved bookings", bookings.size());
        bookings.forEach(b->b.getInvoices().forEach(c->{
            if(nonNull(c.getPollUrl())) {
                try {
                    log.info("Checking booking id: {} invoice: {}", c.getVehicleBooking().getId(), c.getId());
                    bookingPaidPaynow(c);
                } catch (Exception e) {
                    log.error(e.toString());
                }
            }else if (Duration.between(c.getCreatedDate().atZone(ZoneOffset.UTC).toLocalDateTime(), LocalDateTime.now()).toMinutes() > 15) {
                c.cancel();
                c.getVehicleBooking().setStatus(VehicleBookingStatus.FAILED);
                VehicleBooking booking = vehicleBookingRepository.save(c.getVehicleBooking());
                log.info("Failed to authenticate payment for booking quotation{}", c);

                try{
                    emailBookingPaymentFailed(c.getVehicleBooking());
                }catch (Exception ex){
                    log.error("Error on line 770 ",ex);
                }
            }else{
                log.info("Booking is still within 15 minutes of creation. Booking id: {} invoice: {}", c.getVehicleBooking().getId(), c.getId());
            }
        }));
    }



    @Override
    public VehicleBookingDto bookingPaidPaynow(Invoice quote) {
        StatusResponse status;

        if(List.of(InvoiceStatus.PAID, InvoiceStatus.UNPAID).contains(quote.getInvoiceStatus()) )
            return toVehicleBookingDto.apply(quote.getVehicleBooking());

        if(!nonNull(quote.getPollUrl()) || quote.getPollUrl().isBlank()) {
            if(nonNull(quote.getClientSecret())) return null;
            log.info("Quote does not have a poll url cancelling booking and returning");
            log.info("line 611 Payment for quotation id:"+quote.getId()+" failed.");
            quote.cancel();
            quote.getVehicleBooking().setStatus(VehicleBookingStatus.FAILED);
            VehicleBooking booking = vehicleBookingRepository.save(quote.getVehicleBooking());
            try{
                emailBookingPaymentFailed(quote.getVehicleBooking());
            }catch (Exception e){
                log.error("Error on line 618 {}",e);
            }

            return toVehicleBookingDto.apply(booking);
        }

        BigDecimal amount;

        try {
            status = paynowHelper.getPaynowInstance().pollTransaction(quote.getPollUrl());

            // This additional step is to get the raw total from paynow as their sdk getAmount method is not returning the correct amount
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<String>(headers);

            String response = restTemplate.postForObject(quote.getPollUrl(), null, String.class);

            Map<String, String> resultMap = Arrays.stream(response.split("&"))
                    .map(param -> param.split("="))
                    .collect(Collectors.toMap(entry -> entry[0], entry -> entry[1]));

            //            This is only the car rental component that is 4/7 of the payment

            amount = new BigDecimal(resultMap.get("amount")).multiply(BigDecimal.valueOf(0.5714285714285714));

        }catch (HashMismatchException e){
            log.info("Line 646 Payment for quotation id:"+quote.getId()+" failed.");
            quote.cancel();
            quote.getVehicleBooking().setStatus(VehicleBookingStatus.FAILED);
            VehicleBooking booking = vehicleBookingRepository.save(quote.getVehicleBooking());
            log.info("Failed to authenticate payment for quote"+quote);
            log.error(e.getMessage());
            log.error(Arrays.toString(e.getStackTrace()));
            try{
                emailBookingPaymentFailed(quote.getVehicleBooking());
            }catch (Exception ex){
                log.error("Error on line 651 {}",ex);
            }
            return toVehicleBookingDto.apply(booking);
        }

        if (status.paid()) {
            Payment payment = new Payment(status,amount);
            quote.payInvoice(payment);
            quote.getVehicleBooking().setStatus(VehicleBookingStatus.BOOKED);
            VehicleBooking booking = vehicleBookingRepository.save(quote.getVehicleBooking());
            log.info("Payment was successful ref:" +payment.getRef()+ "Invoice id: "+ quote.getId() + " amount: "+amount);

            try{
                emailBookingPaid(quote);
                emailAgencyBookingReceived(quote.getVehicleBooking());
                return toVehicleBookingDto.apply(booking);
            }catch (Exception e){
                emailBookingPaid(quote);
                log.error("A minor error ocurred while creating booking but booking and payment were saved:" +e.toString()+"\n");
                log.error(Arrays.toString(e.getStackTrace()));
                return toVehicleBookingDto.apply(booking);
            }

        } else if(!nonNull(quote.getCreatedDate()) ||
                (quote.getCreatedDate().isBefore(Instant.now().minus(30, ChronoUnit.MINUTES)) // THe booking was made more than 30 minutes ago
                        && quote.getCreatedDate().isAfter(Instant.now().minus(2, ChronoUnit.DAYS)))   // The booking was made less than 2 days ago
        ) {
            try {
                log.info("Line 683 Payment for quotation id:"+quote+" failed.");
                quote.cancel();
                log.info("Quote cancelled");

                quote.getVehicleBooking().setStatus(VehicleBookingStatus.FAILED);
                log.info("Booking cancelled");

                VehicleBooking booking = vehicleBookingRepository.save(quote.getVehicleBooking());
                log.info("Booking saved: {}", booking);

                log.info("Logging the booking after saving : {}",booking);
                log.info("Logging the booking after saving : {}",booking.getStatus());

                emailBookingPaymentFailed(quote.getVehicleBooking());
                return toVehicleBookingDto.apply(booking);

            }catch (Exception e){
                log.error("Error on line 686 {}",e);
                return toVehicleBookingDto.apply(quote.getVehicleBooking());

            }
        }

        return toVehicleBookingDto.apply(quote.getVehicleBooking());
    }

    public VehicleBookingDto bookingPaidPaynow(Long quoteId){
        Invoice quote = invoiceService.getOne(quoteId);
        return bookingPaidPaynow(quote);
    }

    @Override
    @Transactional
    public VehicleBookingDto handOverVehicle(VehicleBookingDto vehicleBookingDto) {
        VehicleBooking vehicleBooking = getOne(vehicleBookingDto.getId());
        if(!vehicleBooking.getStatus().equals(VehicleBookingStatus.BOOKED))
            throw new BusinessValidationException("This booking is no longer in the booked status.");

        vehicleBooking.setCheckoutItems(vehicleBookingDto.getCheckoutItems());
        vehicleBooking.setFuelOut(vehicleBookingDto.getFuelOut());
        vehicleBooking.setMileageOut(vehicleBookingDto.getMileageOut());
        vehicleBooking.setDamageImage(vehicleBookingDto.getDamageImage());
        vehicleBooking.setSignatureOutDate(ZonedDateTime.now(ZoneOffset.UTC));
        vehicleBooking.setStatus(VehicleBookingStatus.WAITINGAUTH);

        // Set legacy signature fields for backward compatibility
        // vehicleBooking.setSignatureOutName(vehicleBookingDto.getSignatureOutName());
        // vehicleBooking.setSignatureOut(vehicleBookingDto.getSignatureOut());

        // Set new signature fields for both hirer and car rental user
        vehicleBooking.setSignatureOutHirer(vehicleBookingDto.getSignatureOutHirer());
        vehicleBooking.setSignatureOutHirerName(vehicleBookingDto.getSignatureOutHirerName());
        vehicleBooking.setSignatureOutCarRental(vehicleBookingDto.getSignatureOutCarRental());
        vehicleBooking.setSignatureOutCarRentalName(vehicleBookingDto.getSignatureOutCarRentalName());

        if(nonNull(vehicleBookingDto.getDamageInfoOut())) vehicleBooking.setDamageInfoOut(vehicleBookingDto.getDamageInfoOut());

        vehicleBookingRepository.save(vehicleBooking);
        return toVehicleBookingDto.apply(vehicleBooking);
    }

    @Override
    @Transactional
    public VehicleBookingDto returnVehicle(VehicleBookingDto vehicleBookingDto) {
        VehicleBooking vehicleBooking = getOne(vehicleBookingDto.getId());
        vehicleBooking.setReturnItems(vehicleBookingDto.getReturnItems());
        vehicleBooking.setFuelIn(vehicleBookingDto.getFuelIn());
        vehicleBooking.setMileageIn(vehicleBookingDto.getMileageIn());
        vehicleBooking.setDamageImageIn(vehicleBookingDto.getDamageImageIn());
        vehicleBooking.setSignatureInDate(ZonedDateTime.now(ZoneOffset.UTC));
        vehicleBooking.setStatus(VehicleBookingStatus.COMPLETE);
        vehicleBooking.setDamagePhotos(vehicleBookingDto.getDamagePhotos());
        vehicleBooking.setExtraCharges(vehicleBookingDto.getExtraCharges());

        // Set legacy signature fields for backward compatibility
        // vehicleBooking.setSignatureIn(vehicleBookingDto.getSignatureIn());
        // vehicleBooking.setSignatureInName(vehicleBookingDto.getSignatureInName());

        // Set new signature fields for both hirer and car rental user
        vehicleBooking.setSignatureInHirer(vehicleBookingDto.getSignatureInHirer());
        vehicleBooking.setSignatureInHirerName(vehicleBookingDto.getSignatureInHirerName());
        vehicleBooking.setSignatureInCarRental(vehicleBookingDto.getSignatureInCarRental());
        vehicleBooking.setSignatureInCarRentalName(vehicleBookingDto.getSignatureInCarRentalName());

        if(nonNull(vehicleBookingDto.getDamageInfoIn()))vehicleBooking.setDamageInfoIn(vehicleBookingDto.getDamageInfoIn());

        emailRatingRequest(vehicleBooking);
        vehicleBookingRepository.save(vehicleBooking);
        return toVehicleBookingDto.apply(vehicleBooking);
    }







    @Override
    public Rating rateVehicleBooking(Rating rating, Long vehicleBookingId) {
        VehicleBooking transportBooking = getOne(vehicleBookingId);

        rating.setVehicleBooking(transportBooking);

        transportBooking.setRating(rating);

        vehicleBookingRepository.save(transportBooking);
        return rating;
    }





    @Override
    public VehicleBookingDto findById(Long id) {

        VehicleBooking booking = getOne(id);
        for(Invoice i: booking.getInvoices()){
            if(nonNull(i.getPollUrl()) && !List.of(InvoiceStatus.PAID, InvoiceStatus.UNPAID).contains(i.getInvoiceStatus()) )
                return this.bookingPaidPaynow(i.getId());
        };


        return toVehicleBookingDto.apply(booking);
    }

    @Override
    public VehicleBookingDto findByIdAndEmail(Long id, String email) {

        VehicleBooking booking = getOne(id);

        if(!booking.getClient().getEmail().equals(email))
            throw new BusinessValidationException("You are not authorised to view this booking.");

        booking.getInvoices().forEach(i->{
            if(nonNull(i.getPollUrl()) && !List.of(InvoiceStatus.PAID, InvoiceStatus.UNPAID).contains(i.getInvoiceStatus()) )
                this.bookingPaidPaynow(i.getId());
        });

        return toVehicleBookingDto.apply(booking);
    }

    @Override
    public Page<VehicleBookingDto> findAll(Long agencyId, List<VehicleBookingStatus> statuses, String searchCriteria, Long bookingId,LocalDateTime start, LocalDateTime end, PageRequest of) {


        return vehicleBookingRepository.findByAgencyId(agencyId, statuses, searchCriteria, bookingId,          start != null ? start.atZone(ZoneId.systemDefault()) : null,
                end != null ? end.atZone(ZoneId.systemDefault()) : null,
                of).map(toVehicleBookingDto);
    }



    @Override
    public Page<VehicleBookingDto> findByClient(Long clientId, List<VehicleBookingStatus> status, String s,Long agencyId, PageRequest of) {
        if(!nonNull(status) || status.isEmpty() )status =  List.of(VehicleBookingStatus.BOOKED, VehicleBookingStatus.COMPLETE, VehicleBookingStatus.CANCELLED);
        return vehicleBookingRepository.findByClientId(clientId,  status , agencyId, of).map(toVehicleBookingDto) ;
    }



    @Override
    public VehicleBookingDepositDto createDeposit(VehicleBookingDepositDto depositDto) {
        log.info("Creating deposit for booking ID: {}", depositDto.getBookingId());

        VehicleBooking booking = getOne(depositDto.getBookingId());

        VehicleBookingDeposit deposit = VehicleBookingDeposit.builder()
                .depositAmount(depositDto.getDepositAmount())
                .paymentMethod(depositDto.getPaymentMethod())
                .depositDate(ZonedDateTime.now())
                .vehicleBooking(booking)
                .build();

        deposit = vehicleBookingDepositRepository.save(deposit);
        log.info("Deposit created successfully with ID: {}", deposit.getId());

        return toVehicleBookingDepositDto.apply(deposit);
    }

    @Override
    public List<VehicleBookingDepositDto> getDepositsByBookingId(Long bookingId) {
        log.info("Getting deposits for booking ID: {}", bookingId);

        List<VehicleBookingDeposit> deposits = vehicleBookingDepositRepository.findByVehicleBookingId(bookingId);

        return deposits.stream()
                .map(toVehicleBookingDepositDto)
                .collect(Collectors.toList());
    }

    @Override
    public void sendRentalReminders() {
        log.info("Sending rental reminders for upcoming bookings");

        ZonedDateTime twoDaysToGo = ZonedDateTime.now().plusDays(2);
        List<VehicleBooking> upcomingBookings = vehicleBookingRepository.findByStartBeforeAndStartAfterAndReminderSentAndStatus(twoDaysToGo,ZonedDateTime.now(), false,  VehicleBookingStatus.BOOKED);

        log.info("Found {} bookings pending reminder sending", upcomingBookings.size());
        for (VehicleBooking booking : upcomingBookings) {
            sendProviderReminder(booking);
            sendHirerReminder(booking);
            booking.setReminderSent(true);
            log.info("Reminder sent for booking {}", booking.getId());
        }

        vehicleBookingRepository.saveAll(upcomingBookings);

    }


    private void sendHirerReminder(VehicleBooking booking) {
        String title = "Upcoming Rental Reminder";
        String invoiceLink = website + "/bookings/" + booking.getId() + "?email=" + booking.getClient().getEmail();
        String verificationLink = website + "/documents/upload?clientId=" + booking.getClient().getId();
        StringBuilder bodyBuilder = new StringBuilder("Dear " + booking.getFirstname() + ",\n\n" +
                "This is a reminder for your upcoming car rental:\n" +
                "- Booking Ref: " + booking.getId() + "\n" +
                "- Vehicle: " + booking.getVehicle().getName() + " " + booking.getVehicle().getModel() + "\n" +
                "- Rental Period: " + dateTimeFormat.format(booking.getStartZoned()) + " to " + dateTimeFormat.format(booking.getEndZoned()) + "\n" +
                "- Pickup Location: " + (nonNull(booking.getLocation()) ? booking.getLocation() : "") + "\n");

        // Add promotion details if a promotion was applied
        if (nonNull(booking.getPromotion())) {
            bodyBuilder.append("\nPromotion Applied:\n");
            bodyBuilder.append("- Promotion: ").append(booking.getPromotion().getTitle()).append("\n");
            if (booking.getPromotion().getCode() != null && !booking.getPromotion().getCode().isEmpty()) {
                bodyBuilder.append("- Promo Code: ").append(booking.getPromotion().getCode()).append("\n");
            }

            switch (booking.getPromotion().getPromotionType()) {
                case DISCOUNT:
                    bodyBuilder.append("- Discount: ").append(booking.getPromotion().getDiscount()).append("\n");
                    break;
                case EXTRA_DAYS:
                    bodyBuilder.append("- Extra Days: ").append(booking.getPromotion().getExtraDays()).append("\n");
                    break;
                case EXTRA_MILEAGE:
                    bodyBuilder.append("- Extra Mileage: ").append(booking.getPromotion().getExtraMileage()).append("\n");
                    break;
                case OTHER_AWARD:
                    if (booking.getPromotion().getHtmlBody() != null && !booking.getPromotion().getHtmlBody().isEmpty()) {
                        bodyBuilder.append("- Special Offer: See details on our website\n");
                    }
                    break;
            }
        }

        bodyBuilder.append("\nImportant Information:\n" +
                "Please bring a valid driver's license when you pick up the car.\n\n" +
                "For more information and assistance with your booking, please contact the vehicle provider at " + booking.getVehicle().getAgency().getEmail() + (nonNull(booking.getVehicle().getAgency().getTelephone()) ? " or " + booking.getVehicle().getAgency().getTelephone() : "") + ".\n" +
                "<a href='" + invoiceLink + "' style='text-decoration:none;'>"
                + "<button>View Booking & Invoice</button>"

                + (!booking.getClient().isVerified() ? ("</a><br><br>" +
                "Please upload your verification documents here: " +
                "<a href='" + verificationLink + "' style='text-decoration:none;'>"
                + "<button>Upload Verification Documents</button>") : "")


                + "</a><br><br>" +
                "We look forward to serving you and hope you have a great experience with our service. If you have any questions or need further assistance, please do not hesitate to reach out to us.\n" +
                "Safe travels!\n\n");
        Agency agency = booking.getVehicle().getAgency();

        emailService.sendEmailAsUserReply(Collections.singletonList(booking.getEmail()), title,  bodyBuilder.toString(), agency.getBillingEmail(),agency.getName(), booking.byMyKarlink() ? null : agency.getId());

    }

    private void sendProviderReminder(VehicleBooking booking) {
        String title = "Upcoming Rental Reminder";
        StringBuilder bodyBuilder = new StringBuilder("Dear " + booking.getVehicle().getAgency().getName() + ",\n\n" +
                "This is a reminder for the upcoming rental of your vehicle:\n" +
                "- Booking Ref: " + booking.getId() + "\n" +
                "- Vehicle: " + booking.getVehicle().getName() + " " + booking.getVehicle().getModel() + "\n" +
                "- Rental Period: " + dateTimeFormat.format(booking.getStartZoned()) + " to " + dateTimeFormat.format(booking.getEndZoned()) + "\n" +
                "- Pickup Location: "+(nonNull(booking.getLocation())?booking.getLocation():"")+"\n" +
                "- Hirer Details: "+booking.getFirstname()+", "+ booking.getSurname()+"\n" +
                "- Hirer Contact Details: "+booking.getEmail()+", "+ booking.getPhone()+"\n");

        // Add promotion details if a promotion was applied
        if (nonNull(booking.getPromotion())) {
            bodyBuilder.append("\nPromotion Applied:\n");
            bodyBuilder.append("- Promotion: ").append(booking.getPromotion().getTitle()).append("\n");
            if (booking.getPromotion().getCode() != null && !booking.getPromotion().getCode().isEmpty()) {
                bodyBuilder.append("- Promo Code: ").append(booking.getPromotion().getCode()).append("\n");
            }

            switch (booking.getPromotion().getPromotionType()) {
                case DISCOUNT:
                    bodyBuilder.append("- Discount: ").append(booking.getPromotion().getDiscount()).append("\n");
                    break;
                case EXTRA_DAYS:
                    bodyBuilder.append("- Extra Days: ").append(booking.getPromotion().getExtraDays()).append("\n");
                    break;
                case EXTRA_MILEAGE:
                    bodyBuilder.append("- Extra Mileage: ").append(booking.getPromotion().getExtraMileage()).append("\n");
                    break;
                case OTHER_AWARD:
                    if (booking.getPromotion().getHtmlBody() != null && !booking.getPromotion().getHtmlBody().isEmpty()) {
                        bodyBuilder.append("- Special Offer: See details on our website\n");
                    }
                    break;
            }
        }

        bodyBuilder.append("\nPlease ensure the vehicle is ready for the rental period.\n\n");
        Agency agency = booking.getVehicle().getAgency();

        emailService.sendEmailAsUserReply(Collections.singletonList(booking.getVehicle().getAgency().getEmail()), title,  bodyBuilder.toString(), agency.getBillingEmail(),agency.getName(), booking.byMyKarlink() ? null : agency.getId());
    }
//
//    private void sendHirerReminder(VehicleBooking booking) {
//        String title = "Upcoming Rental Reminder";
//        String invoiceLink = website + "/bookings/" + booking.getId() + "?email="+booking.getClient().getEmail();
//        String verificationLink = website + "/bookings/" + booking.getId() + "/verify";
//        String body = "Dear " + booking.getFirstname() + ",\n\n" +
//                "This is a reminder for your upcoming car rental:\n" +
//                "- Booking Ref: " + booking.getId() + "\n" +
//                "- Vehicle: " + booking.getVehicle().getName() + " " + booking.getVehicle().getModel() + "\n" +
//                "- Rental Period: " + dateTimeFormat.format(booking.getStartZoned()) + " to " + dateTimeFormat.format(booking.getEndZoned()) + "\n" +
//                "- Pickup Location: " + (nonNull(booking.getLocation()) ? booking.getLocation() : "") + "\n" +
//                "Please find your invoice here: " + invoiceLink + "\n\n" +
//                "Important Information:\n" +
//                "Please bring a valid driver's license when you pick up the car.\n\n" +
//                "For more information and assistance with your booking, please contact the vehicle provider at " + booking.getVehicle().getAgency().getEmail() + (nonNull(booking.getVehicle().getAgency().getTelephone()) ? " or " + booking.getVehicle().getAgency().getTelephone() : "") + ".\n" +
//                "<a href='" + invoiceLink + "' style='text-decoration:none;'>"
//                + "<button>View Booking & Invoice</button>"
//                + "</a><br><br>" +
//                "We look forward to serving you and hope you have a great experience with our service. If you have any questions or need further assistance, please do not hesitate to reach out to us.\n" +
//                "Safe travels!\n\n";
//
//        emailService.sendEmailAsUserReply(Collections.singletonList(booking.getEmail()), title, body);
//    }

    private VehicleBooking getOne(Long transportBookingId) {
        return  vehicleBookingRepository.findById(transportBookingId).orElseThrow( ()-> new BusinessValidationException(String.format("No vehicle booking with id: %s found", transportBookingId)));
    }

    @Override
    public VehicleBookingDto resendBookingEmail(Long bookingId, BookingEmailType bookingEmailType) {
        VehicleBooking booking = getOne(bookingId);
        Invoice invoice = booking.getInvoices().stream().findFirst().orElseThrow(() ->
                new BusinessValidationException("No invoice found for this booking"));

        try {
            switch (bookingEmailType) {
                case BOOKING_PAID:
                    emailBookingPaid(invoice);
                    break;
                case BOOKING_RESERVED:
                    emailBookingReserved(invoice);
                    break;
                case AGENCY_BOOKING_RECEIVED:
                    emailAgencyBookingReceived(booking);
                    break;
                case BOOKING_PAYMENT_FAILED:
                    emailBookingPaymentFailed(booking);
                    break;
                case BOOKING_CANCELLED:
                    emailBookingCancelled(booking);
                    break;
                case BOOKING_REMINDER:
                    sendProviderReminder(booking);
                    sendHirerReminder(booking);
                    break;
                case BOOKING_COMPLETED:
                    emailRatingRequest(booking);
                    break;
                default:
                    throw new BusinessValidationException("Unsupported email type: " + bookingEmailType);
            }

            log.info("Successfully resent {} email for booking ID: {}", bookingEmailType, bookingId);
            return toVehicleBookingDto.apply(booking);
        } catch (Exception e) {
            log.error("Error resending {} email for booking ID: {}: {}", bookingEmailType, bookingId, e.getMessage());
            log.error(Arrays.toString(e.getStackTrace()));
            throw new BusinessValidationException("Failed to resend email: " + e.getMessage());
        }
    }

    @Override
    public VehicleBookingDto cancelBooking(Long bookingId, String cancelReason, Boolean cancelledByAgency) {
        log.info("Request to cancel booking ID: {} by {}", bookingId, cancelledByAgency ? "agency" : "client");

        VehicleBooking booking = getOne(bookingId);

        // Check if booking is already cancelled
        if (booking.getStatus() == VehicleBookingStatus.CANCELLED) {
            throw new BusinessValidationException("This booking is already cancelled.");
        }

        // Check if booking is already completed
        if (booking.getStatus() == VehicleBookingStatus.COMPLETE) {
            throw new BusinessValidationException("Cannot cancel a completed booking.");
        }

        // Check if booking is within 48 hours of start time
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime bookingStart = booking.getStart();
        boolean isWithin48Hours = bookingStart.minusHours(48).isBefore(now);

        // If within 48 hours, require a cancellation reason
        if (isWithin48Hours && (cancelReason == null || cancelReason.trim().isEmpty())) {
            throw new BusinessValidationException("A cancellation reason is required when cancelling within 48 hours of the booking start time.");
        }

        // Set cancellation details
        booking.setByAgency(cancelledByAgency);
        booking.setCancelReason(cancelReason);
        booking.cancel(); // This sets the status to CANCELLED

        // Process refund if cancelled by agency
        if (cancelledByAgency) {
            // Get the invoice for the booking
            Invoice invoice = booking.getInvoices().stream().findFirst().orElseThrow(() ->
                    new BusinessValidationException("No invoice found for this booking"));

            // Mark invoice as cancelled
            invoice.cancel();

            // Note: Actual refund processing would typically involve integration with a payment gateway
            // This would be implemented based on the specific payment gateway being used
            log.info("Refund should be processed for booking ID: {} as it was cancelled by the agency", bookingId);
        }

        // Save the updated booking
        booking = vehicleBookingRepository.save(booking);

        // Send cancellation emails
        emailBookingCancelled(booking);

        log.info("Successfully cancelled booking ID: {}", bookingId);
        return toVehicleBookingDto.apply(booking);
    }

    /**
     * Apply tax calculations to all invoice items
     */
    private void applyTaxCalculationsToInvoice(Invoice invoice, VehicleBooking vehicleBooking) {
        try {
            // Get agency settings for tax configuration
            AgencySettings agencySettings = agencySettingsService.findAgencySettings(vehicleBooking.getVehicle().getAgency().getId());

            if (agencySettings == null) {
                log.warn("No agency settings found for agency ID: {}", vehicleBooking.getVehicle().getAgency().getId());
                return;
            }

            // Apply tax calculations to each invoice item
            for (InvoiceItem item : invoice.getInvoiceItems()) {
                // Determine if this is a vehicle item or addon item
                if (item.getDescription().contains("Car rental for")) {
                    // This is a vehicle rental item
                    taxCalculationService.applyTaxToInvoiceItem(item, vehicleBooking.getVehicle(), null, agencySettings);
                } else {
                    // This is likely an addon item - find the matching addon
                    VehicleInventory matchingAddon = vehicleBooking.getAddons().stream()
                            .filter(addon -> item.getDescription().trim().startsWith(addon.getName()))
                            .findFirst()
                            .orElse(null);

                    taxCalculationService.applyTaxToInvoiceItem(item, null, matchingAddon, agencySettings);
                }
            }

            log.info("Applied tax calculations to invoice ID: {} with {} items", invoice.getId(), invoice.getInvoiceItems().size());

        } catch (Exception e) {
            log.error("Error applying tax calculations to invoice ID: {}", invoice.getId(), e);
            // Don't throw exception to avoid breaking the booking process
        }
    }
}
