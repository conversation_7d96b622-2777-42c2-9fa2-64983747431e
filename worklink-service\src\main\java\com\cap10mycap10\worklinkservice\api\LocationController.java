package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationCreateDto;
import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationResultDto;
import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationUpdateDto;
import com.cap10mycap10.worklinkservice.service.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class LocationController {

    private final LocationService locationService;

    public LocationController(final LocationService locationService) {
        this.locationService = locationService;
    }

    /*@CreateShiftLocation*/
    @PostMapping(value = "location", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ShiftLocationResultDto> create(@RequestBody ShiftLocationCreateDto shiftLocationCreateDto) {
        log.info("Request to add location with : {}", shiftLocationCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(locationService.save(shiftLocationCreateDto));
    }

    /*@ViewShiftLocation*/
    @GetMapping(value = "location/{id}")
    public ResponseEntity<ShiftLocationResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request to view location with id : {}", id);
        return ResponseEntity.ok(locationService.findById(id));
    }

//    @GetMapping(value = "location")
//    public ResponseEntity<List<ShiftLocationResultDto>> findAll() {
//        log.info("Request to view all locations");
//        return ResponseEntity.ok(shiftLocationService.findAll());
//    }


    /*@ViewShiftLocation*/
    @GetMapping(value = "locations")
    public ResponseEntity<List<ShiftLocationResultDto>> findAllLocations(@RequestParam(value = "agencyId", required= false) Long agencyId) {
        log.info("Request to get all  locations ");
        return ResponseEntity.ok(locationService.findAll(agencyId));
    }


    @GetMapping(value = "locations/search/{query}")
    public ResponseEntity<List<ShiftLocationResultDto>> search(@PathVariable("query") String query, @RequestParam(value = "agencyId", required= false) Long agencyId) {
        log.info("Request to search locations with : {}", query);
        return ResponseEntity.ok(locationService.searchAllColumns(query, agencyId));
    }


    /*@ViewShiftLocation*/
    @GetMapping(value = "locations/{page}/{size}")
    public ResponseEntity<Page<ShiftLocationResultDto>> findById(@PathVariable("page") int page,
                                                                 @PathVariable("size") int size) {
        log.info("Request to get paged location with : {}, {}", page,size);
        return ResponseEntity.ok(locationService.findAllPaged(PageRequest.of(page, size)));
    }



    /*@UpdateShiftLocation*/
    @PutMapping(value = "location", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ShiftLocationResultDto> update(@RequestBody ShiftLocationUpdateDto shiftLocationUpdateDto) {
        log.info("Request to update location with : {}", shiftLocationUpdateDto);
        return ResponseEntity.ok(locationService.save(shiftLocationUpdateDto));
    }

   /*@DeleteShiftLocation*/
    @DeleteMapping(value = "location/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete location with id  : {}", id);
        locationService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
