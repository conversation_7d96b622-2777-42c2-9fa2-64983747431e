package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.reports.JasperReportDTO;
import com.cap10mycap10.worklinkservice.reports.JasperReportGenerator;
import com.cap10mycap10.worklinkservice.reports.ReportFormat;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import net.sf.jasperreports.engine.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class ReportService {

    private final DataSource dataSource;

    @Autowired
    ApplicationContext appContext;

    private final JasperReportGenerator jasperReportGenerator;

    public ReportService(@Qualifier("dataSource") DataSource dataSource, JasperReportGenerator jasperReportGenerator) {
        this.dataSource = dataSource;
        this.jasperReportGenerator = jasperReportGenerator;
    }

    private ResponseEntity<Resource> getResourceResponseEntity(HttpServletRequest servletRequest, JasperReportDTO jasperReportDTO) {
        val resource = jasperReportGenerator.generateReport(jasperReportDTO);
        String contentType = getContentType(servletRequest, resource);
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }


    private static String getContentType(HttpServletRequest httpServletRequest, Resource resource) {

        String contentType = null;
        try {
            contentType = httpServletRequest.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            ex.printStackTrace();
            log.error("### Could not determine file type. due to {}", ex.getMessage());
        }
        if (contentType == null) {
            contentType = "application/octet-stream";
        }
        return contentType;
    }

    private Connection getConnection() throws SQLException {
        DataSource dataSource = (DataSource) appContext.getBean("dataSource");
        return dataSource.getConnection();
    }


    public ResponseEntity<Resource> generateInvoice(HttpServletRequest servletRequest,
                                                    String reportFormat, Long invoiceId) throws SQLException, IOException, JRException {

        Connection connection = getConnection();

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("INVOICE_ID", invoiceId);


        val jasperReportDTO = JasperReportDTO.builder()
                .reportName("Worklink-Invoice")
                .reportFormat(ReportFormat.valueOf(reportFormat))
                .source(connection)
                .parameters(parameters)
                .build();

        return getResourceResponseEntity(servletRequest, jasperReportDTO);


    }

    public ResponseEntity<Resource> generatePayAdvice(HttpServletRequest servletRequest,
                                                    String reportFormat, Long invoiceId) throws SQLException, IOException, JRException {

        Connection connection = getConnection();

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("PayAdvice_ID", invoiceId);


        val jasperReportDTO = JasperReportDTO.builder()
                .reportName("Worklink-Invoice")
                .reportFormat(ReportFormat.valueOf(reportFormat))
                .source(connection)
                .parameters(parameters)
                .build();

        return getResourceResponseEntity(servletRequest, jasperReportDTO);


    }
}
