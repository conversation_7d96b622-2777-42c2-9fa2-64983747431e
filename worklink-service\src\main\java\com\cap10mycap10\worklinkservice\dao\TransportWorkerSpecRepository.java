package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.TransportWorkerSpec;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;

public interface TransportWorkerSpecRepository extends JpaRepository<TransportWorkerSpec, Long> {
    @Query(value =
            "SELECT * FROM transport_worker_spec AS spec " +
            "WHERE  " +
            "spec.transport_id IN (SELECT trans.id FROM transport AS trans WHERE  trans.transport_status = 'BOOKED' AND trans.agency_id IN  (SELECT agency_id FROM agency_worker WHERE worker_id = ?1) ) " +
            "AND " +
            "(spec.assignment_code_id IN (SELECT worker.assignment_code_id FROM worker AS worker WHERE worker.id = ?1 AND (worker.gender COLLATE utf8mb4_unicode_ci = spec.gender OR spec.gender = 'NO_PREFERENCE' ) ) ) " +
            "AND " +
            "( spec.number_of_staff > ( SELECT COUNT(*) from shift AS booking  WHERE booking.worker_spec_id = spec.id  AND booking.status not in ('APPLIED','CANCELLED')  AND booking.worker_id <>  ?1 ) ) " +
            "AND " +
            "(?1 NOT IN ( SELECT worker_id FROM shift WHERE worker_spec_id = spec.id AND worker_id = ?1));", nativeQuery = true)
    List<TransportWorkerSpec> findNewForWorker(Long workerId);
}
