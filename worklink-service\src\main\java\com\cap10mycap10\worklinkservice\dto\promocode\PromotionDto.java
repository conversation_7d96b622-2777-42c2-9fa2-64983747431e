package com.cap10mycap10.worklinkservice.dto.promocode;

import com.cap10mycap10.worklinkservice.dto.VehicleFilterDto;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.enums.PromotionType;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.VehicleFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.time.ZonedDateTime;

import static java.util.Objects.nonNull;

@Data
public class PromotionDto {
    private Long id;

    @Size(min = 4, max = 15, message = "The promo code should be between 4 and 15 characters")
    private String code;
    private Boolean adminDiscount = false;
    private String description;
    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")

    private ZonedDateTime expiryDate;
    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")

    private ZonedDateTime startDate;
    private Float discount;
    private Status status;
    @NotNull
    private Integer usageLimit;
    private ZonedDateTime cancelledDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant createdDate;
    private ZonedDateTime activatedDate;
    private AgencyResultDto agency;
    private Integer usageCount;

    private int extraMileage;


    private int extraDays;
//    private String otherAward;

    @Enumerated(EnumType.STRING)
    private PromotionType promotionType;


    private int daysHired;
//    private int extraMileage;
//    private int extraDays;
    private String title;
    private String htmlBody;
    @NotNull
    private VehicleFilterDto vehicleFilter;


    public String getCode() {
        return nonNull(code) ? code.toUpperCase() : null;
    }



    public void setExtraDays(int extraDays) {
        if (daysHired > 0 && extraDays >= daysHired) {
            throw new IllegalArgumentException("Extra days must be less than days hired.");
        }


        else this.extraDays = extraDays;
    }


    public void setDiscount(Float discount) {
        if (discount != null && discount < 0) {
            throw new IllegalArgumentException("Discount cannot be negative.");
        }
        if (discount != null && discount >= 75) {
            throw new IllegalArgumentException("Discount must be less than 75%.");
        }


        else this.discount = discount;
    }

    public void setForAdmin() {
        promotionType = PromotionType.OTHER_AWARD;
        adminDiscount = true;
        discount = null;
        extraDays = 0;
        extraMileage = 0;
    }
}
