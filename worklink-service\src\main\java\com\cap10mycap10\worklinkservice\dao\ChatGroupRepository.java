package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.ChatGroup;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ChatGroupRepository extends JpaRepository<ChatGroup, Long> {

    Optional<ChatGroup> findByGroupName(String name);

    ChatGroup findByShift_Id(Long shiftId);

    List<ChatGroup> findByChatGroupMembers_Id(Long workerId);
}
