package com.cap10mycap10.worklinkservice.dto.trainingsession;

import com.cap10mycap10.worklinkservice.enums.TrainingSessionStatus;
import com.cap10mycap10.worklinkservice.enums.TrainingStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.api.client.util.DateTime;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.Size;
import java.sql.Time;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Set;

@Data
public class TrainingSessionUpdateDto {
    private String name;

    private Long trainingId;
    private Long id;

    private TrainingSessionStatus trainingStatus;

    private Long shiftLocationId;

    private Long agencyId;

    private String postCode;

    private String address;

    @JsonSerialize(as = DateTime.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime startDateTime;

    @JsonSerialize(as = DateTime.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime endDateTime;

//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private Float breakTime;

    private Double trainingCost;

    private Integer vacancies;

    private String notes;

    private Boolean isAgencyPaying;
}
