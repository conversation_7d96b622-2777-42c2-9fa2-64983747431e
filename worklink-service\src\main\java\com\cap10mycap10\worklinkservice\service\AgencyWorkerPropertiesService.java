package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.InviteWorkerRequestDto;
import com.cap10mycap10.worklinkservice.dto.agencyworkerproperties.AgencyWorkerPropertiesCreateDto;
import com.cap10mycap10.worklinkservice.dto.agencyworkerproperties.IAgencyWorkerProperties;
import com.cap10mycap10.worklinkservice.model.AgencyWorkerProperties;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface AgencyWorkerPropertiesService {

    AgencyWorkerProperties addAgencyWorkerProperties (AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto);

    void deleteAgencyWorkerProperties(Long id);


    AgencyWorkerProperties findById(Long id);

    IAgencyWorkerProperties findProperties(Long workerId, Long agencyId);

    List<AgencyWorkerProperties> findAll();

    Page<AgencyWorkerProperties> findAllPaged(PageRequest of);

    AgencyWorkerProperties save(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto);

    AgencyWorkerProperties getOne(Long id);

    AgencyWorkerProperties convert(IAgencyWorkerProperties agencyWorkerProperties);

    AgencyWorkerProperties activateAgencyWorker(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto);
    AgencyWorkerProperties deactivateAgencyWorker(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto);

    AgencyWorkerProperties activateAgencyApplicant(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto);
    void deactivateAgencyApplicant(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto);
    void inviteWorker(InviteWorkerRequestDto inviteWorkerRequestDto);
}
