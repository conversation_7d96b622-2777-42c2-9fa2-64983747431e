package com.cap10mycap10.worklinkservice.model;

import lombok.*;

import jakarta.persistence.*;
import java.math.BigDecimal;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class AgencySettings {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private boolean chargeVat;

    @Column(nullable = false)
    private BigDecimal vatPercentage;

    @Column(nullable = false)
    private Long agencyId;

    // New tax-related fields
    @Column(nullable = false)
    private boolean defaultTaxInclusive = true; // Default pricing is tax-inclusive

    @Column(nullable = false)
    private boolean vehiclesTaxableByDefault = true; // Vehicles are taxable by default

    @Column(nullable = false)
    private boolean addonsTaxableByDefault = true; // Addons are taxable by default
}
