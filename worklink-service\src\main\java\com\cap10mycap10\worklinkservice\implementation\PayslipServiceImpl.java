package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.PayslipRepository;
import com.cap10mycap10.worklinkservice.dto.file.FileDto;
import com.cap10mycap10.worklinkservice.dto.payslip.PayslipResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.mapper.payslip.PayslipToPayslipResultDto;
import com.cap10mycap10.worklinkservice.model.Payslip;
import com.cap10mycap10.worklinkservice.model.WorkerTraining;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.PayslipService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PayslipServiceImpl implements PayslipService {

    @Value("${storage.volume.path}")
    private  String rootPath;
    private final WorkerService workerService;

    private final DataBucketUtil dataBucketUtil;

    private final PayslipToPayslipResultDto payslipToPayslipResultDto;
    private final AgencyService agencyService;
    private final PayslipRepository payslipRepository;


    public PayslipServiceImpl(WorkerService workerService,
                              DataBucketUtil dataBucketUtil, PayslipToPayslipResultDto payslipToPayslipResultDto,
                              AgencyService agencyService,
                              PayslipRepository payslipRepository) {
        this.workerService = workerService;
        this.dataBucketUtil = dataBucketUtil;
        this.payslipToPayslipResultDto = payslipToPayslipResultDto;
        this.agencyService = agencyService;
        this.payslipRepository = payslipRepository;
    }

    @Override
    @Transactional
    public Payslip addPayslip(Long agencyId, Long workerId, MultipartFile files) {
        log.info("Request to upload a payslip");
        Payslip payslip = new Payslip();
        List<String> types = new ArrayList<String>();

        types.add("application/pdf");

        if (!types.contains(files.getContentType())) {
            throw new BusinessValidationException("Uploaded file type is not supported. Please upload a pdf");
        }

        if (!files.isEmpty()) {


            log.info("Start file uploading service");
            List<WorkerTraining> inputFiles = new ArrayList<>();

            Arrays.asList(files).forEach(file -> {
                String originalFileName = file.getOriginalFilename();
                if (originalFileName == null) {
                    throw new BusinessValidationException("Original file city is null");
                }
                Path path = new File(originalFileName).toPath();

                String contentType = null;
                try {
                    contentType = Files.probeContentType(path);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                FileDto fileDto = dataBucketUtil.uploadFile(file, "w/t/" + workerId + "/" + originalFileName, contentType);

                if (fileDto != null) {


                    payslip.setPayslipPdf(fileDto.getFileUrl());
                    payslip.setWorker(workerService.getOne(workerId));

                    payslip.setAgency(agencyService.getOne(agencyId));

                    payslipRepository.save(payslip);
                    log.debug("File uploaded successfully, file city: {} and url: {}", fileDto.getFileName(), fileDto.getFileUrl());
                }
                log.debug("File details successfully saved in the database");

            });


        } else {
            log.info("You failed to upload");
            throw new BusinessValidationException("Uploaded file is not empty.");

        }

        return null;
    }

    @Override
    public Page<WorkerResultDto> findAllWorkersPaylipPaged(Long agencyId, PageRequest of) {
        Page<WorkerResultDto> page = agencyService.findAllWorkersPaged(agencyId, of);



        for(WorkerResultDto w: page){
            String count = payslipRepository.findWorkerTotalSlips(w.getId(), agencyId);
            String countAdvices = payslipRepository.findWorkerTotalAdvices(w.getId(), agencyId);
            String grossPay = payslipRepository.findWorkerGrossPay(w.getId(), agencyId);

            w.setTotalPayslips(count);
            w.setTotalPayAdvices(countAdvices);
            w.setGrossPay(grossPay);

        }

        return page;
    }
    @Override
    public Page<WorkerResultDto> searchAllWorkersPaylipPaged(Long agencyId, String query, PageRequest of) {
        Page<WorkerResultDto> workers = workerService.search(query,agencyId, of);


        for(WorkerResultDto w: workers){
            String count = payslipRepository.findWorkerTotalSlips(w.getId(), agencyId);
            String countAdvices = payslipRepository.findWorkerTotalAdvices(w.getId(), agencyId);
            String grossPay = payslipRepository.findWorkerGrossPay(w.getId(), agencyId);
            w.setTotalPayslips(count);
            w.setTotalPayAdvices(countAdvices);
            w.setGrossPay(grossPay);
        }

        return workers;
    }

    @Override
    public void deletePayslip(Long id) {

        Payslip payslip = payslipRepository.findById(id).orElseThrow(() -> new RecordNotFoundException("Payslip not found"));

//            payslipRepository.delete(payslip);
//            payslipRepository.flush();


    }

    @Override
    public Payslip findById(Long id) {
        return null;
    }

    @Override
    @Transactional
    public Page<PayslipResultDto> findWorkerPayslips(Long workerId, PageRequest of) {
        Page<Payslip> payslips = payslipRepository.findPagedWorkerPayslips(workerId, of);

        payslips.forEach(
                (p) -> {


                    Date d = Date.from(p.getCreatedDate());


                    LocalDate date = convertToLocalDateViaInstant(d);

                    DateTimeFormatter customFormat =
                            DateTimeFormatter.ofPattern("MM-yyyy-dd");





                    System.out.printf("Time: %s%n", date.format(customFormat));

                    p.setDate(DateTimeFormatter.ofLocalizedDate(FormatStyle.FULL).format(date));
                }
        );

        List<PayslipResultDto> payslipList = payslips.stream()
                .map(payslipToPayslipResultDto::convert)
                .collect(Collectors.toList());

        Pageable pageable = payslips.getPageable();
        final int start = (int)pageable.getOffset();
        final int end = Math.min((start + pageable.getPageSize()), payslipList.size());
        final Page<PayslipResultDto> result = new PageImpl<>(payslipList.subList(start, end), pageable, payslipList.size());


        return result;

    }

    @Override
    public Page<Payslip> findAllPaged(PageRequest of) {
        return null;
    }


    @Override
    public Payslip getOne(Long id) {
        return null;
    }

    public LocalDate convertToLocalDateViaInstant(Date dateToConvert) {
        return dateToConvert.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }



}
