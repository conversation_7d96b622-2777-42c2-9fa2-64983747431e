package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.agencyworkertraining.IAgencyWorkerTraining;
import com.cap10mycap10.worklinkservice.model.AgencyWorkerTraining;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AgencyWorkerTrainingRepository extends JpaRepository<AgencyWorkerTraining, Long> {

    List<AgencyWorkerTraining> findAllByWorkerIdAndAgencyIdAndTrainingId(Long workerId, Long agencyId, Long trainingId);

    @Query(value = "select *\n" +
            "from agency_worker_training\n" +
            "where worker_id = ?1\n" +
            "   and agency_id = ?2\n"+
            "   and training_id = ?3\n"
            , nativeQuery = true)
    IAgencyWorkerTraining findAgencyWorkerTraining(Long workerId, Long agencyId, Long trainingId);
}