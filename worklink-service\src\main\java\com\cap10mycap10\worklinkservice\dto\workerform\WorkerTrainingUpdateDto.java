package com.cap10mycap10.worklinkservice.dto.workerform;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class WorkerTrainingUpdateDto {

    private Long id;

    private String description;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate trainingDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate trainingExpiry;

    private Long agencyId;
    private Long workerId;

    private String comment;

    private String status;
}
