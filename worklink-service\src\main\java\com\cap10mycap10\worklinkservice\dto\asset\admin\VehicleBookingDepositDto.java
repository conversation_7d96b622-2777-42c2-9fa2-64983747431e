package com.cap10mycap10.worklinkservice.dto.asset.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
public class VehicleBookingDepositDto {
    private Long id;
    
    @NotNull
    private Long bookingId;
    
    @NotNull
    private BigDecimal depositAmount;
    
    @NotNull
    private String paymentMethod;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private ZonedDateTime depositDate;
}
