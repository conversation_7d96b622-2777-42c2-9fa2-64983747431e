package com.cap10mycap10.worklinkservice.mapper.shift;

import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
import com.cap10mycap10.worklinkservice.dto.shift.ShiftCreateDto;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.model.WorkerAppliedShift;
import com.cap10mycap10.worklinkservice.service.*;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

import static java.time.LocalDateTime.now;

@Component
public class ShiftDtoToShift implements Converter<ShiftCreateDto, Shift> {

    private final ClientService clientService;
    private final AgencyService agencyService;
    private final LocationService locationService;
    @Autowired
    private WorkerService workerService;
    private final ShiftTypeService shiftTypeService;
    private final AssignmentCodeService assignmentCodeService;
    private final AuthenticationFacade authenticationFacade;
    private final ShiftDirectorateService shiftDirectorateService;

    public ShiftDtoToShift(final ClientService clientService, AgencyService agencyService, final LocationService locationService, ShiftTypeService shiftTypeService, AssignmentCodeService assignmentCodeService, AuthenticationFacade authenticationFacade, ShiftDirectorateService shiftDirectorateService) {
        this.clientService = clientService;
        this.agencyService = agencyService;
        this.locationService = locationService;
        this.shiftTypeService = shiftTypeService;
        this.assignmentCodeService = assignmentCodeService;
        this.authenticationFacade = authenticationFacade;
        this.shiftDirectorateService = shiftDirectorateService;
    }

    @Override
    public Shift convert(ShiftCreateDto shiftCreateDto) {

        Shift shift = new Shift();
        shift.setClient(clientService.getOne(shiftCreateDto.getClientId()));
        shift.setAgency(agencyService.getOne(shiftCreateDto.getAgency()));
        shift.setGender(shiftCreateDto.getGender());
        shift.setHoursBeforeBroadcasting(shiftCreateDto.getHoursBeforeBroadcasting());
        shift.setNotes(shiftCreateDto.getNotes());

        shift.setStart(shiftCreateDto.getStart());

        shift.setEnd(shiftCreateDto.getEnd());
//        shift.setShiftStartTime(shiftCreateDto.getShiftStartTime());
        shift.setBreakTime(shiftCreateDto.getBreakTime());
//        shift.setShiftEndTime(shiftCreateDto.getShiftEndTime());
        shift.setRequireApplicationByWorkers(shiftCreateDto.getRequireApplicationByWorkers());
        shift.setPublishToAllWorkers(shiftCreateDto.getPublishToAllWorkers());
        shift.setStatus(shiftCreateDto.getShiftStatus());
        shift.setShiftType(shiftTypeService.getOne(shiftCreateDto.getShiftType()));
        shift.setShowNoteToAgency(shiftCreateDto.getShowNoteToAgency());
        shift.setShowNoteToFw(shiftCreateDto.getShowNoteToFw());
        shift.setDirectorate(shiftDirectorateService.getOne(shiftCreateDto.getShiftDirectorateId()));
        shift.setCreatedBy(authenticationFacade.getAuthentication().getName());
        shift.setAssignmentCode(assignmentCodeService.getOne(shiftCreateDto.getAssignmentCodeId()));
        Set<Agency> agencySet = new HashSet<>();
        for (Long agencyId : shiftCreateDto.getAgentIdList()) {
            Agency agency = agencyService.getOne(agencyId);
            agencySet.add(agency);
        }
        shift.addAllAgency(agencySet);


        return shift;

    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }


    public void bookWorkerDirectly(Shift shift, Long workerId) {

        Worker worker = workerService.getOne(workerId);

        if (shift.getRequireApplicationByWorkers())
            throw new BusinessValidationException("Shift requires that workers apply");
        if (shift.getStatus() != ShiftStatus.NEW)
            throw new BusinessValidationException("Shift already booked.");
        boolean isAvailable = workerService.getWorkerConflicts(workerId, shift.getStart(), shift.getEnd());
        if(!isAvailable)
            throw new BusinessValidationException("Worker cannot book this shift because of conflicting shifts");

        shift.setWorker(worker);
        shift.setBookedDate(now());
        shift.setStatus(ShiftStatus.BOOKED);

        WorkerAppliedShift workerAppliedShift = new WorkerAppliedShift();
        workerAppliedShift.setShift(shift);
        workerAppliedShift.setAgency(shift.getAgency());
        workerAppliedShift.setWorker(worker);
        workerAppliedShift.setAppliedDate(LocalDate.now());
        workerAppliedShift.setShiftStatus(ShiftStatus.BOOKED);

    }
}
