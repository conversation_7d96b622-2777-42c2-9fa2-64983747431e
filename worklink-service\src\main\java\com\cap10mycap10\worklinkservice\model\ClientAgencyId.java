package com.cap10mycap10.worklinkservice.model;


import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
@AllArgsConstructor
@NoArgsConstructor
public class ClientAgencyId implements Serializable {


    @Column(name = "client_id")
    private Long clientId;

    @Column(name = "agency_id")
    private Long agencyId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ClientAgencyId that = (ClientAgencyId) o;
        return Objects.equals(agencyId, that.agencyId) && Objects.equals(clientId, that.clientId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(agencyId, clientId);
    }
}
