package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateCreateDto;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateResultDto;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateUpdateDto;
import com.cap10mycap10.worklinkservice.model.ShiftDirectorate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface ShiftDirectorateService {

    ShiftDirectorateResultDto save(ShiftDirectorateCreateDto shiftDirectorateCreateDto);

    ShiftDirectorateResultDto findById(Long id);

    List<ShiftDirectorateResultDto> findAll();

    Page<ShiftDirectorateResultDto> findAllPaged(String searchCriteria, Long locationId,PageRequest of);

    void deleteById(Long id);

    ShiftDirectorateResultDto save(ShiftDirectorateUpdateDto shiftDirectorateUpdateDto);

    ShiftDirectorate getOne(Long shiftDirectorateId);

    List<ShiftDirectorateResultDto> findByLocationId(Long id);

    Page<ShiftDirectorateResultDto> findAllPagedByClient(Long clientId, String searchCriteria, PageRequest of);

    Page<ShiftDirectorateResultDto> findAllPagedByAgency(Long agencyId, String searchCriteria, PageRequest of);

    List<ShiftDirectorateResultDto> findAllWithFilter(String location);

    ShiftDirectorate getByDeputyId(Long id);
}
