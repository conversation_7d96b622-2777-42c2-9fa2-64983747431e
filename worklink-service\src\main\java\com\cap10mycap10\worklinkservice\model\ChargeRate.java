package com.cap10mycap10.worklinkservice.model;


import lombok.*;

import jakarta.persistence.*;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(
        uniqueConstraints = @UniqueConstraint(
                name = "uk_charge_rate_agency_admin",
                columnNames = {
                        "assignment_code_id",
                        "shift_type_id"
                }
        )
)
public class ChargeRate extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private BigDecimal chargeRate;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private ShiftType shiftType;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private AssignmentCode assignmentCode;



    public ShiftType getShiftType() {
        ShiftType a = new ShiftType();
        a.setId(shiftType.getId());
        a.setName(shiftType.getName());
        return a;
    }

    public AssignmentCode getAssignmentCode() {
        AssignmentCode a = new AssignmentCode();
        a.setId(assignmentCode.getId());
        a.setName(assignmentCode.getName());
        a.setCode(assignmentCode.getCode());
        return a;
    }
}
