package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.ServicesRepository;
import com.cap10mycap10.worklinkservice.dto.service.ServiceCreateDto;
import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.dto.service.ServiceUpdateDto;
import com.cap10mycap10.worklinkservice.mapper.services.ServicesDtoToServices;
import com.cap10mycap10.worklinkservice.mapper.services.ServicesToServiceResultDto;
import com.cap10mycap10.worklinkservice.model.Services;
import com.cap10mycap10.worklinkservice.service.ServicesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ServiceServiceImpl implements ServicesService {

    private final ServicesRepository servicesRepository;
    private final ServicesToServiceResultDto toServiceResultDto;
    private final ServicesDtoToServices toServices;

    public ServiceServiceImpl(final ServicesRepository servicesRepository,
                              final ServicesToServiceResultDto toServiceResultDto,
                              final ServicesDtoToServices toServices) {
        this.servicesRepository = servicesRepository;
        this.toServiceResultDto = toServiceResultDto;
        this.toServices = toServices;
    }

    @Override
    public ServiceResultDto save(ServiceCreateDto serviceCreateDto) {
        return toServiceResultDto.convert(servicesRepository.save(toServices.convert(serviceCreateDto)));
    }

    @Override
    public ServiceResultDto findById(Long id) {
        return toServiceResultDto.convert(servicesRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Service not found {}")));
    }

    @Override
    public List<ServiceResultDto> findAll() {
        return servicesRepository.findAll()
                .stream()
                .map(toServiceResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Page<ServiceResultDto> findAllPaged(PageRequest of) {
        return servicesRepository.findAll(of)
                .map(toServiceResultDto::convert);
    }

    @Override
    public void deleteById(Long id) {
        Services services = getOne(id);
        try {
            servicesRepository.deleteById(id);
            servicesRepository.flush();
        }catch (Exception ex){
            throw new BusinessValidationException("Service cannot be deleted");
        }
    }

    @Override
    public ServiceResultDto save(ServiceUpdateDto serviceUpdateDto) {
        Services services = getOne(serviceUpdateDto.getId());
        services.setName(serviceUpdateDto.getName());
        return toServiceResultDto.convert(servicesRepository.save(services));
    }

    @Override
    public Services getOne(Long id) {
        return servicesRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Service not found"));
    }
}
