package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.AgencyBill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface AgencyBillRepository extends JpaRepository<AgencyBill, Long> {

    @Query(value = "select * from agency_bill",nativeQuery = true)
    Page<AgencyBill> findAllPaged(PageRequest of);

    @Query(value = "select * from agency_bill where status like 'PENDING'",nativeQuery = true)
    Page<AgencyBill> findAllPendingPaged(PageRequest of);
}
