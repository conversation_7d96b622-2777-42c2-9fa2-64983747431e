package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import jakarta.persistence.*;
import java.time.*;


@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class WorkerTrainingSession extends AbstractAuditingEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Worker worker;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Training training;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinColumn(nullable = false)
    private Agency agency;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private TrainingSession trainingSession;


    @Column(nullable = false)
    private Boolean isAdminBilled = false;
    @Column(nullable = false)
    private Boolean feedbackSkipped = false;
    @Enumerated(EnumType.STRING)
    private WorkerTrainingSessionStatus trainingStatus;
    @Column(nullable = false)
    private Boolean skippedTraining = true;
    @Column(nullable = false)
    private Boolean isAgencyBilled = false;
    @Column(nullable = false)
    private Boolean showCertificate = false;
    private Integer trainingScore;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="dd-MM-yyyy", timezone="Z")
    private LocalDate trainingExpiryDate;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="dd-MM-yyyy", timezone="Z")
    private LocalDate dateUploaded;
    private Boolean passedTraining;

    @OneToOne(mappedBy = "workerTrainingSession")
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private WorkerTraining workerTraining;
}
