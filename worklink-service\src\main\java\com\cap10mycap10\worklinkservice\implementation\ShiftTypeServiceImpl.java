package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.dao.ShiftTypeRepository;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.ShiftType;
import com.cap10mycap10.worklinkservice.service.ShiftTypeService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ShiftTypeServiceImpl implements ShiftTypeService {

    @Autowired
    private ShiftTypeRepository shiftTypeRepository;
    @Override
    public ShiftType save(ShiftType shiftTypeCreateDto) {
        try{
            return shiftTypeRepository.save(shiftTypeCreateDto);
        }catch (DataIntegrityViolationException e){
            throw new BusinessValidationException("A rate with that city is already defined. Use a different city.");
        }

    }

    @Override
    public ShiftType findById(Long id) {
        return getOne(id);
    }

    @Override
    public List<ShiftType> findAll() {
        return shiftTypeRepository.findAll();
    }

    @Override
    public Page<ShiftType> findAllPaged(PageRequest of) {
        return shiftTypeRepository.findAll(of);
    }

    @Override
    public void deleteById(Long id) {
        ShiftType shiftType = getOne(id);
        try {
            shiftTypeRepository.deleteById(id);
            shiftTypeRepository.flush();
        } catch (Exception ex) {
            throw new BusinessValidationException("Shift type cannot be deleted");
        }
    }

    @Override
    public ShiftType update(ShiftType shiftTypeUpdateDto) {
        ShiftType shiftType = getOne(shiftTypeUpdateDto.getId());
        shiftType.setName(shiftTypeUpdateDto.getName());
        shiftType.setBookingType(shiftTypeUpdateDto.getBookingType());
        return shiftTypeRepository.save(shiftType);
    }

    @Override
    public ShiftType getOne(Long id) {
        return shiftTypeRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Shift type not found"));
    }

    @Override
    public void deactivate(Long id) {
        ShiftType shiftType = getOne(id);
        shiftType.setStatus(Status.INACTIVE);
        shiftTypeRepository.save(shiftType);
    }

    @Override
    public void activate(Long id) {
        ShiftType shiftType = getOne(id);
        shiftType.setStatus(Status.ACTIVE);
        shiftTypeRepository.save(shiftType);
    }
}
