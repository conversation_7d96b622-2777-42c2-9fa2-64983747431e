package com.cap10mycap10.worklinkservice.mapper.services;

import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.model.Services;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class ServicesToServiceResultDto implements Converter<Services, ServiceResultDto> {
    @Override
    public ServiceResultDto convert(Services services) {
        ServiceResultDto serviceResultDto = new ServiceResultDto();
        serviceResultDto.setId(services.getId());
        serviceResultDto.setName(services.getName());
        serviceResultDto.setCreatedBy(services.getCreatedBy());
        return serviceResultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
