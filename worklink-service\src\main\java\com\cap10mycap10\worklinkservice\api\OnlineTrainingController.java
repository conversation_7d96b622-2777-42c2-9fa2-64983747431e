package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.dao.TrainingRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerTrainingRepository;
import com.cap10mycap10.worklinkservice.dto.training.iHasco.CertificateDto;
import com.cap10mycap10.worklinkservice.dto.training.iHasco.CertificatesResponseDto;
import com.cap10mycap10.worklinkservice.dto.training.iHasco.CourseDto;
import com.cap10mycap10.worklinkservice.dto.training.iHasco.CoursesResponseDto;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.Training;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.model.WorkerTraining;
import com.cap10mycap10.worklinkservice.service.TrainingService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import com.cap10mycap10.worklinkservice.service.WorkerTrainingService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/online-trainings", produces = MediaType.APPLICATION_JSON_VALUE)
public class OnlineTrainingController {

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    WorkerService workerService;
    @Autowired
    WorkerRepository workerRepository;

    @Autowired
    WorkerTrainingService workerTrainingService;
    @Autowired
    TrainingService trainingService;
    @Autowired
    TrainingRepository trainingRepository;
    @Autowired
    WorkerTrainingRepository workerTrainingRepository;

    @PostMapping(value = "register-worker/{workerId}")
    public ResponseEntity<Object> registerToIHasco(@PathVariable("workerId") Long workerId) throws JsonProcessingException, JSONException {
        log.info("Request to register worker for online trainings: {}", workerId);

        Worker worker = workerService.getOne(workerId);

        String createPersonUrl = "https://api.ihasco.co.uk/users";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("30b9c433-4079-4351-b68a-895e52a4d4e5");


        JSONObject personJsonObject = new JSONObject();
        personJsonObject.put("first_name", worker.getFirstname());
        personJsonObject.put("last_name", worker.getLastname());
        personJsonObject.put("email", worker.getEmail());
        personJsonObject.put("password", "Myworklink1!");

        HttpEntity<String> request =
                new HttpEntity<String>(personJsonObject.toString(), headers);

//        try {
            String personResultAsJsonStr =
                    restTemplate.postForObject(createPersonUrl, request, String.class);
//        }catch (HttpClientErrorException e){
//
//        }
        JsonNode res = objectMapper.readTree(personResultAsJsonStr);

        if (res.isObject()) {
            ObjectNode obj = (ObjectNode) res;
            if (obj.get("data").get("id")!=null) {
                worker.setHascoId(obj.get("data").get("id").asLong());
                workerRepository.save(worker);
            }
        }


        return ResponseEntity.created(ServletUriComponentsBuilder.fromCurrentRequest().build().toUri())
                .build();
    }



    @GetMapping(value = "certificate/{enrolmentId}")
    public ResponseEntity<Object> getCertificate(@PathVariable("enrolmentId") String enrolmentId) throws JsonProcessingException, JSONException {
        log.info("Request to register worker for online trainings: {}", enrolmentId);


        String uri = "https://api.ihasco.co.uk/results/"+enrolmentId+"?fields=certificate_link";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("30b9c433-4079-4351-b68a-895e52a4d4e5");
        HttpEntity<String> entity = new HttpEntity<String>( headers);

        ResponseEntity<String> certificate = restTemplate.exchange(uri, org.springframework.http.HttpMethod.GET, entity, String.class, 100);
        JsonNode res = objectMapper.readTree(certificate.getBody());
        return ResponseEntity.ok(res);
    }


//    @GetMapping(value = "certificate-import")
//    public ResponseEntity<Object> importCertificate() throws JsonProcessingException, JSONException {
//        log.info("Getting certificate updates from iHasco");
//        syncCourses();
//        syncCetificates();
//        return ResponseEntity.ok("certificate");
//    }


    void  syncCourses() {
        String uri = "https://api.ihasco.co.uk/courses";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth("30b9c433-4079-4351-b68a-895e52a4d4e5");
        HttpEntity<String> entity = new HttpEntity<String>( headers);

        ResponseEntity<CoursesResponseDto> courses = restTemplate.exchange(uri, org.springframework.http.HttpMethod.GET, entity, CoursesResponseDto.class, 100);

        // Check if course exists and is a pass
        for(int i = 0; i< Objects.requireNonNull(courses.getBody()).getData().size(); i++){
            CourseDto course = courses.getBody().getData().get(i);

                Optional<Training> train = trainingService.findByIHascoId(course.getCourse_id());

                if(train.isEmpty()){
                    Training training = new Training();
                    training.setName(course.getTitle());
                    training.setHascoId(course.getCourse_id());
                    training.setDescription("iHasco Online Training");
//                    training.setCode();
//                    training.setServices(servicesService.getOne(trainingCreateDto.getServiceId()));
                    trainingRepository.save(training);
                }


        }
    }
//    void  syncCetificates() {
//        String uri = "https://api.ihasco.co.uk/results";
//        RestTemplate restTemplate = new RestTemplate();
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.setBearerAuth("30b9c433-4079-4351-b68a-895e52a4d4e5");
//        HttpEntity<String> entity = new HttpEntity<String>( headers);
//
//        ResponseEntity<CertificatesResponseDto> certificate = restTemplate.exchange(uri, org.springframework.http.HttpMethod.GET, entity, CertificatesResponseDto.class, 100);
//
//        // Check if certificate exists and is a pass
//        for(int i = 0; i< Objects.requireNonNull(certificate.getBody()).getData().size(); i++){
//            CertificateDto cert = certificate.getBody().getData().get(i);
//
//            if(cert.getCompleted_at()!=null && cert.getCompleted_at().isBefore(LocalDateTime.now().plusDays(1))){
//                WorkerTraining train = workerTrainingService.findByIHascoId(cert.getId());
//
//                if(train==null){
//                    WorkerTraining workerTraining = new WorkerTraining();
//                    Training training = trainingService.findByIHascoId(cert.getCourse_id()).orElseThrow(
//                            () -> new RecordNotFoundException("Training was not found")
//                    );
//
//                    workerTraining.setCode(training.getCode());
//                        workerTraining.setCity(training.getCity());
//
////                        workerTraining.setHascoId(cert.getId());
////                        workerTraining.setHascoCourseId(cert.getCourse_id());
//                        workerTraining.setTrainingDate(cert.getCompleted_at().toLocalDate());
//                        workerTraining.setTrainingExpiry(cert.getExpires_at().toLocalDate());
//
//                        workerTraining.setWorker(workerService.findByIHascoId(cert.getUser_id()));
//                        workerTraining.setTraining(training);
//
//                        workerTrainingRepository.saveAndFlush(workerTraining);
//
//                }
//
//            }
//
//
//        }
//    }



}
