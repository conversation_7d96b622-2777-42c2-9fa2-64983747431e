package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.chargerate.ChargeRateDto;
import com.cap10mycap10.worklinkservice.dto.chargerate.ChargeRateUpdateDto;
import com.cap10mycap10.worklinkservice.model.ChargeRate;
import com.cap10mycap10.worklinkservice.service.ChargeRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ChargeRateController {

    private final ChargeRateService chargeRateService;

    public ChargeRateController(ChargeRateService chargeRateService) {
        this.chargeRateService = chargeRateService;
    }

    /* @CreateShiftDirectorate*/
    @PostMapping(value = "charge-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity create(@RequestBody ChargeRateDto chargeRateDto) {
        log.info("Request to add charge rate with : {}", chargeRateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        chargeRateService.save(chargeRateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    /*@ViewShiftDirectorate*/
    @GetMapping(value = "charge-rate/{id}")
    public ResponseEntity<ChargeRate> findById(@PathVariable("id") Long id) {
        log.info("Request to get charge rate with id : {}", id);
        return ResponseEntity.ok(chargeRateService.findById(id));
    }

    /*@ViewShiftDirectorate*/
    @GetMapping(value = "charge-rate")
    public ResponseEntity<List<ChargeRate>> findAll() {
        log.info("Request to get all  charge rate");
        return ResponseEntity.ok(chargeRateService.findAll());
    }

    /* @ViewShiftDirectorate*/
    @GetMapping(value = "charge-rate/{page}/{size}")
    public ResponseEntity<Page<ChargeRate>> findById(@PathVariable("page") int page,
                                                     @PathVariable("size") int size) {
        log.info("Request to get paged charge rates with : {}, {}", page, size);
        return ResponseEntity.ok(chargeRateService.findAllPaged(PageRequest.of(page, size)));
    }

    /* @UpdateShiftDirectorate*/
    @PutMapping(value = "charge-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ChargeRate> update(@RequestBody ChargeRateUpdateDto chargeRateUpdateDto) {
        log.info("Request to update charge rate with : {}", chargeRateUpdateDto);
        return ResponseEntity.ok(chargeRateService.save(chargeRateUpdateDto));
    }

    /*@DeleteShiftDirectorate*/
    @DeleteMapping(value = "charge-rate/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to edit charge rate with id  : {}", id);
        chargeRateService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
