package com.cap10mycap10.worklinkservice.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.*;
import java.time.LocalDate;


@Entity
@Data
@EqualsAndHashCode(callSuper = true)
public class TrainingFeedback extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private int content;
    private int facility;
    private int trainer;
    private int method;
    private Boolean isRelevant;
    private Boolean skillsDev;
    private String comment;

    @OneToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinColumn(nullable = false, unique = true)
    private WorkerTrainingSession booking;
}
