package com.cap10mycap10.worklinkservice.dto.workertrainingsession;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter

public class WorkerTrainingSessionAuthorizeDto {
    private Long workerTrainingBookingId;

    private Boolean skippedTraining;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate trainingExpiryDate;

    private Integer score;

    private Boolean passedTraining;

    Boolean generateCertificate;

}
