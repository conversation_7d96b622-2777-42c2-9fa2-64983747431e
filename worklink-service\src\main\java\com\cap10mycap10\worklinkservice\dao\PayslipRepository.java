package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.payslip.PayslipResultDto;
import com.cap10mycap10.worklinkservice.model.Payslip;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface PayslipRepository extends JpaRepository<Payslip, Long> {

    @Query(value = "select id       ,\n" +
            "       payslip_pdf       as payslipPDF,\n" +
            "       agency_id        as agencyId,\n" +
            "       worker_id        as worker_id \n" +
            "from note\n" +
            "where worker_id = ?1" , nativeQuery = true)
    Page<PayslipResultDto> findAllWorkerNotes(Long workerId, PageRequest of);


    @Query(value = "select id       ,\n" +
            "       payslip_pdf       as payslipPdf,\n" +
            "       agency_id        as agencyId,\n" +
            "       worker_id        as workerId \n" +
            "from payslip\n" +
            "where payslip.worker_id =?1" , nativeQuery = true)
    Page<Payslip> getWorkerPayslips(Long workerId, PageRequest of);

    @Query(value = "select  id,  agency_id as agencyId, worker_id as workerId," +
            " created_date as createdDate, " +
            "created_by as createdBy, date as Date," +
            "version, " +
            " last_modified_by as lastModifiedBy," +
            "last_modified_date as lastModifiedDate," +
            " payslip_pdf as payslipPdf\n" +
            "from payslip where worker_id =?1", nativeQuery = true)
    Page<Payslip> findPagedWorkerPayslips(Long workerId, PageRequest of);

    @Query(value = "select  count(*) as count\n" +
            "from payslip\n" +
            "where worker_id =?1 and agency_id = ?2", nativeQuery = true)
    String findWorkerTotalSlips(Long workerId, Long agencyId);

    @Query(value = "select  count(*) as count\n" +
            "from pay_advice\n" +
            "where worker_id =?1 and agency_id =?2", nativeQuery = true)
    String findWorkerTotalAdvices(Long workerId, Long agencyId);

    @Query(value = "select  sum(total_amount) as count\n" +
            "from pay_advice\n" +
            "where worker_id =?1 and agency_id =?2", nativeQuery = true)
    String findWorkerGrossPay(Long workerId, Long agencyId);
}
