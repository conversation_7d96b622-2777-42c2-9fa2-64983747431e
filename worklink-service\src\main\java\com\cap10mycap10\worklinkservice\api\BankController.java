package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.bank.BankCreateDto;
import com.cap10mycap10.worklinkservice.dto.bank.BankResultDto;
import com.cap10mycap10.worklinkservice.model.Bank;
import com.cap10mycap10.worklinkservice.service.BankService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;


@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class BankController {

    private final BankService bankService;

    public BankController(final BankService bankService) {
        this.bankService = bankService;
    }

    /*@CreateBank*/
    @PostMapping(value = "bank", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Bank> create(@RequestBody BankCreateDto bankCreateDto) {
        log.info("Request to add bank with : {}", bankCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(bankService.save(bankCreateDto));
    }

   /* @ViewBank*/
    @GetMapping(value = "bank/{id}")
    public ResponseEntity<BankResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get bank with id : {}", id);
        return ResponseEntity.ok(bankService.findById(id));
    }

    /* @ViewWorkerBank*/
    @GetMapping(value = "bank/worker/{id}")
    public ResponseEntity<BankResultDto> findByWorkerId(@PathVariable("id") Long id) {
        log.info("Request to get bank with id : {}", id);
        return ResponseEntity.ok(bankService.findByWorkerId(id));
    }



//   /* @UpdateBank*/
//    @PutMapping(value = "bank", consumes = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<Bank> update(@RequestBody BankUpdateDto bankUpdateDto) {
//        log.info("Request to update bank with : {}", bankUpdateDto);
//        return ResponseEntity.ok(bankService.save(bankUpdateDto));
//    }

    /*@DeleteBank*/
    @DeleteMapping(value = "bank/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        bankService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping(value = "worker-banking/banking-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity uploadGeneralSignature(@RequestParam("file") MultipartFile file,
                                                 @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add worker profile image : {}");
        bankService.addGeneralSignature( workerId, file);
        return  ResponseEntity.noContent().build();
    }
}
