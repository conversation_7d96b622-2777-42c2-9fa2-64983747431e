package com.cap10mycap10.worklinkservice.mapper.vehiclefilter;

import com.cap10mycap10.worklinkservice.dao.LocationRepository;
import com.cap10mycap10.worklinkservice.dao.VehicleRepository;
import com.cap10mycap10.worklinkservice.dto.VehicleFilterDto;
import com.cap10mycap10.worklinkservice.model.Location;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.model.VehicleFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;

import java.util.HashSet;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Component
public class VehicleFilterToVehicleFilterDto implements Converter<VehicleFilter, VehicleFilterDto> {
    @Autowired
    private LocationRepository locationRepository;
    @Autowired
    private VehicleRepository vehicleRepository;

    @Override
    public VehicleFilterDto convert(VehicleFilter vehicleFilter) {
        VehicleFilterDto vehicleFilterDto = new VehicleFilterDto();
        vehicleFilterDto.setId(vehicleFilter.getId());
        vehicleFilterDto.setVehicleOperator(vehicleFilter.getVehicleOperator());
        vehicleFilterDto.setNames(vehicleFilter.getNames());
        vehicleFilterDto.setNameOperator(vehicleFilter.getNameOperator());
        vehicleFilterDto.setModels(vehicleFilter.getModels());
        vehicleFilterDto.setModelOperator(vehicleFilter.getModelOperator());
        vehicleFilterDto.setColors(vehicleFilter.getColors());
        vehicleFilterDto.setColorOperator(vehicleFilter.getColorOperator());
        vehicleFilterDto.setFuelTypes(vehicleFilter.getFuelTypes());
        vehicleFilterDto.setFuelTypeOperator(vehicleFilter.getFuelTypeOperator());
        vehicleFilterDto.setTypes(vehicleFilter.getTypes());
        vehicleFilterDto.setTypeOperator(vehicleFilter.getTypeOperator());
        vehicleFilterDto.setLocationIds(vehicleFilter.getLocations().stream()
                .map(Location::getId)
                .collect(Collectors.toSet()));
        vehicleFilterDto.setVehicleIds(vehicleFilter.getVehicles().stream()
                .map(Vehicle::getId)
                .collect(Collectors.toSet()));
        vehicleFilterDto.setLocationOperator(vehicleFilter.getLocationOperator());
        vehicleFilterDto.setAgencyId(vehicleFilter.getAgencyId());
        return vehicleFilterDto;
    }

    public VehicleFilter convertToEntity(VehicleFilterDto vehicleFilterDto) {
        VehicleFilter vehicleFilter = new VehicleFilter();
        vehicleFilter.setId(vehicleFilterDto.getId());
        vehicleFilter.setVehicleOperator(vehicleFilterDto.getVehicleOperator());
        vehicleFilter.setNames(vehicleFilterDto.getNames());
        vehicleFilter.setNameOperator(vehicleFilterDto.getNameOperator());
        vehicleFilter.setModels(vehicleFilterDto.getModels());
        vehicleFilter.setModelOperator(vehicleFilterDto.getModelOperator());
        vehicleFilter.setColors(vehicleFilterDto.getColors());
        vehicleFilter.setColorOperator(vehicleFilterDto.getColorOperator());
        if(nonNull(vehicleFilterDto.getFuelTypes())) vehicleFilter.setFuelTypes(vehicleFilterDto.getFuelTypes().stream()
                .map(Enum::name)
                .collect(Collectors.toList()));
        vehicleFilter.setFuelTypeOperator(vehicleFilterDto.getFuelTypeOperator());
        if(nonNull(vehicleFilterDto.getTypes()))  vehicleFilter.setTypes(vehicleFilterDto.getTypes().stream()
                .map(Enum::name)
                .collect(Collectors.toList()));
        vehicleFilter.setTypeOperator(vehicleFilterDto.getTypeOperator());
        if(nonNull(vehicleFilterDto.getLocationIds())) vehicleFilter.setLocations(new HashSet<>(locationRepository.findAllById(vehicleFilterDto.getLocationIds())));
        if(nonNull(vehicleFilterDto.getVehicleIds())) vehicleFilter.setVehicles(new HashSet<>(vehicleRepository.findAllById(vehicleFilterDto.getVehicleIds())));
        vehicleFilter.setLocationOperator(vehicleFilterDto.getLocationOperator());
        vehicleFilter.setAgencyId(vehicleFilterDto.getAgencyId());
        return vehicleFilter;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return typeFactory.constructType(VehicleFilter.class);
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return typeFactory.constructType(VehicleFilterDto.class);
    }
}