package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.dto.transport.TransportWorkerTimesDto;
import com.cap10mycap10.worklinkservice.dto.transport.WorkerTimesDto;
import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.search.mapper.pojo.mapping.definition.annotation.Indexed;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

import static java.util.Objects.nonNull;


@Entity
@Data
@Indexed
@Slf4j
public class Shift extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private ShiftDirectorate directorate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime start;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime end;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime lastAuthorisationReminder;

    private String breakTime;

    @Enumerated(EnumType.STRING)
    private Gender gender;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private ShiftType shiftType;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private AssignmentCode assignmentCode;

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "shift", fetch = FetchType.EAGER)
    Set<ShiftExpenseClaim> shiftExpenseClaims;

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "shift", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    Set<WorkerAppliedShift> applications;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonBackReference
    private TransportWorkerSpec workerSpec;

    private String cancelReason;
    private Boolean workerNotified;

    private String queryReason;

    private String queryResponse;

    private String notes;

    private Number applicantCount;

    private Boolean showNoteToFw;

    private Boolean released;

    private Boolean showNoteToAgency;

    private Boolean publishToAllWorkers;

    private Boolean requireApplicationByWorkers;

    private Boolean directlyBookByWorker;

    private int hoursBeforeBroadcasting;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    @Column(nullable = false)
    private LocalDateTime dateTimeBooked =  LocalDateTime.now();


    @Enumerated(EnumType.STRING)
    private ShiftStatus status;

    @Column(nullable = false)
    private Boolean isAdminBilled = false;

    @Column(nullable = false)
    private Boolean isAgencyBilled = false;

    @Enumerated(EnumType.STRING)
    private ShiftStatus shiftWorkerStatus;


    @Enumerated(EnumType.STRING)
    private ShiftStatus appliedStatus;

    @JsonIgnore
    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Agency agency;

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @ManyToMany(fetch = FetchType.LAZY, cascade = {
            CascadeType.ALL,
    })
    @JoinTable(name = "shift_agency",
            joinColumns = @JoinColumn(name = "shift_id"),
            inverseJoinColumns = @JoinColumn(name = "agency_id")
    )
    private Set<Agency> agencies = new HashSet<>();

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Worker worker;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Client client;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime appliedDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime bookedDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime authorizedDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime releaseDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime queriedDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime cancelledDate;

    private Boolean carPooling;

    @ElementCollection(fetch = FetchType.EAGER)
    private Set<Long> carPoolingShiftSet = new HashSet<>();

    @ManyToOne(fetch = FetchType.EAGER)
    private ChatGroup shiftChatGroup;


    public void addAgency(Agency agency) {
        agencies.add(agency);
        agency.getShifts().add(this);
    }

    public void addAllAgency(Set<Agency> agency) {
        agencies.addAll(agency);
        for (Agency agency1 : agency
        ) {
            agency1.getShifts().add(this);
        }

    }

    public void removeAgency(Agency agency) {
        agencies.remove(agency);
        agency.getShifts().remove(this);
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Shift)) return false;
        return id != null && id.equals(((Shift) o).getId());
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }



    public boolean approveApplicant() throws BusinessValidationException {
        if(!nonNull(this.workerSpec))
            throw new BusinessValidationException("Transport job not found. Save the applicant first before approving");

        if(!this.workerSpec.isFullyBooked()){
            if(!this.workerSpec.workerIsValid(worker))
                throw new BusinessValidationException("Worker is no longer qualified to book job.");
            this.status = ShiftStatus.BOOKED;
            return true;
        }else
            throw new BusinessValidationException("Job is already fully booked");


    }

    public void setEnd(LocalDateTime end) {
        this.end = end;
    }

    public void setStart(LocalDateTime start) {
        this.start = start;
    }

    public LocalDateTime getStart() {
        return (!nonNull(start)&&nonNull(workerSpec)) ? workerSpec.getTransport().getDateTimeRequired() :  start;
    }

    public AssignmentCode getAssignmentCode() {
        return (!nonNull(assignmentCode)&&nonNull(workerSpec)) ? workerSpec.getAssignmentCode() :  assignmentCode;
    }

    public LocalDateTime getEnd() {
        return nonNull(end) ? end :  getStart().plusMinutes(10);
    }

    public void authorize(LocalDateTime start, LocalDateTime end) {
        this.start = start;
        this.end = end;
        this.status = ShiftStatus.AUTHORIZED;
    }

    void startJob() {
        if(this.workerSpec.getTransport().getDateTimeRequired().isBefore(LocalDateTime.now())){
            this.status = ShiftStatus.AWAITING_AUTHORIZATION;
        }else
            log.error("Attempt to begin job before required start time. Job Details: {}", this);
    }

    public void expire() {
        this.status = ShiftStatus.EXPIRED;
    }

    public boolean isBooked() {
        return this.status==ShiftStatus.BOOKED || this.status==ShiftStatus.AWAITING_AUTHORIZATION || this.status==ShiftStatus.AUTHORIZED;
    }

    protected void cancelByJob(){
        this.cancelReason = "The transport job was cancelled";
        this.status = ShiftStatus.CANCELLED;
    }

    public void cancel() {
        Transport transport = this.workerSpec.getTransport();

        if( nonNull(transport.getTeamLeader()) && transport.getTeamLeader().getId().equals(this.worker.getId()))
            transport.setTeamLeader(null);
        transport.refreshState();
    }

    public Boolean getWorkerNotified() {
        return nonNull(workerNotified)?workerNotified:false;
    }

    public void updateBookingTimes(TransportWorkerTimesDto vehicleLogDto) {
        Optional<WorkerTimesDto> times = vehicleLogDto.getTimes().stream().filter(t -> Objects.equals(t.getBookingId(), this.getId())).findFirst();
        if(times.isPresent()) {
            start = times.get().getStart();
            end = times.get().getEnd();
        }
    }

    public List<InvoiceItem> billClient() {
        if(!nonNull(start) || !nonNull(end) )
            throw new BusinessValidationException("Please set individual start and end times for this job. This is typically done by the team leader.");
        return worker.getInvoiceItems(start, end, 0, "umbrella", this);
    }
}
