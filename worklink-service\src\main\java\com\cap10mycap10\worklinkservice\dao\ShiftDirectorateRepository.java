package com.cap10mycap10.worklinkservice.dao;


import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.model.ShiftDirectorate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ShiftDirectorateRepository extends JpaRepository<ShiftDirectorate, Long>, JpaSpecificationExecutor<ShiftDirectorate> {

    List<ShiftDirectorate> findAllByLocation_Id(Long id);

//    @Query(value = "select * from shift_directorate where location_id in (\n" +
//            "    select shift_location.id from shift_location\n" +
////            "    inner join client c on shift_location.client_id = c.id\n" +
//            "    where client_id =?1 order by shift_directorate.city asc\n" +
//            "    )", nativeQuery = true)
    List<ShiftDirectorate> findAllByClientOrderByName(Client client);


    @Query(value = "select *\n" +
            "from shift_directorate\n" +
            "where location_id in (\n" +
            "    select location.id\n" +
            "    from location\n" +
//            "             inner join client c on shift_location.client_id = c.id\n" +
            "    where shift_directorate.client_id in (select client_id from agency_client where agency_id = ?1)\n" +
            ") ", nativeQuery = true)
    List<ShiftDirectorate> findAllByAgentId(Long id);

    ShiftDirectorate findByDeputyId(Long id);
}
