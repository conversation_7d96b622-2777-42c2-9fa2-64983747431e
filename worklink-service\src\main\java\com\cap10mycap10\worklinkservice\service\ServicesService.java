package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.service.ServiceCreateDto;
import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.dto.service.ServiceUpdateDto;
import com.cap10mycap10.worklinkservice.model.Services;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface ServicesService {
    ServiceResultDto save(ServiceCreateDto serviceCreateDto);

    ServiceResultDto findById(Long id);

    List<ServiceResultDto> findAll();

    Page<ServiceResultDto> findAllPaged(PageRequest of);

    void deleteById(Long id);

    ServiceResultDto save(ServiceUpdateDto serviceUpdateDto);

    Services getOne(Long serviceId);
}
