package com.cap10mycap10.worklinkservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing exchange rates between currencies
 * Exchange rates are fetched from Stripe and cached for performance
 */
@Entity
@Table(name = "exchange_rate", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"from_currency", "to_currency", "rate_date"}))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeRate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "from_currency", nullable = false, length = 3)
    private String fromCurrency; // Source currency code

    @Column(name = "to_currency", nullable = false, length = 3)
    private String toCurrency; // Target currency code

    @Column(nullable = false, precision = 19, scale = 6)
    private BigDecimal rate; // Exchange rate from source to target

    @Column(name = "rate_date", nullable = false)
    private LocalDateTime rateDate; // When this rate was fetched

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "source", length = 50)
    private String source = "STRIPE"; // Source of the exchange rate (STRIPE, MANUAL, etc.)

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    public ExchangeRate(String fromCurrency, String toCurrency, BigDecimal rate, LocalDateTime rateDate) {
        this.fromCurrency = fromCurrency;
        this.toCurrency = toCurrency;
        this.rate = rate;
        this.rateDate = rateDate;
        this.createdAt = LocalDateTime.now();
        this.source = "STRIPE";
        this.isActive = true;
    }

    /**
     * Convert an amount from the source currency to the target currency
     */
    public BigDecimal convert(BigDecimal amount) {
        return amount.multiply(rate);
    }
}
