package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.AgencyExpenseRate;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;


public interface AgencyExpenseRateRepository extends JpaRepository<AgencyExpenseRate, Long> {
    List<AgencyExpenseRate> findAllByAgency(Agency agency);
}
