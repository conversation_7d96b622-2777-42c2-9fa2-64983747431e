package com.cap10mycap10.worklinkservice.mapper.transportbooking;

import com.cap10mycap10.worklinkservice.dto.transportbooking.TransportApplicantsResultDto;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.service.AssignmentCodeService;
import org.springframework.stereotype.Service;

import java.util.function.Function;

@Service
public class TransportBookingToApplicantsResultDtoMapper implements Function<Shift, TransportApplicantsResultDto> {
    private final AssignmentCodeService assignmentCodeService;

    public TransportBookingToApplicantsResultDtoMapper(AssignmentCodeService assignmentCodeService) {
        this.assignmentCodeService = assignmentCodeService;
    }

    @Override
    public TransportApplicantsResultDto apply(Shift transportBooking) {
        TransportApplicantsResultDto transportApplicantsResultDto = new TransportApplicantsResultDto();
        transportApplicantsResultDto.setTransportId(transportBooking.getWorkerSpec().getTransport().getId());
        transportApplicantsResultDto.setApplicationStatus(transportBooking.getStatus().toString());
        transportApplicantsResultDto.setFirstName(transportBooking.getWorker().getFirstname());
        transportApplicantsResultDto.setLastName(transportBooking.getWorker().getLastname());
        transportApplicantsResultDto.setTransportBooking(transportBooking.getId());
        AssignmentCode assignmentCode = transportBooking.getWorker().getAssignmentCode();
        transportApplicantsResultDto.setAssignmentCode(assignmentCode.getCode());
        if(transportBooking.getCancelReason() != null){
            transportApplicantsResultDto.setCancelReason(transportBooking.getCancelReason());
        }

        return transportApplicantsResultDto;
    }
}
