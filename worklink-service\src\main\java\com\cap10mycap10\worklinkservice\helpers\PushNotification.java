package com.cap10mycap10.worklinkservice.helpers;

import com.cap10mycap10.worklinkservice.service.FirebaseMessagingService;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PushNotification {

    @Autowired
    private FirebaseMessagingService firebaseService;


    public void sendPushMessage(String title, String body, String fcmToken ) throws FirebaseMessagingException {
        firebaseService.sendNotification(title, body, fcmToken);
    }
}
