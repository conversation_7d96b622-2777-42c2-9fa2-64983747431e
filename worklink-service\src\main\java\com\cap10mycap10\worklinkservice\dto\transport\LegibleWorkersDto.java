package com.cap10mycap10.worklinkservice.dto.transport;


import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.model.Worker;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode
public class LegibleWorkersDto {
    private Long workerSpecId;
    private List<LegibleWorkerDto> workers = new ArrayList<>();

    public LegibleWorkersDto(Long specId){
        this.workerSpecId = specId;
    }

    @Getter
    @Setter
    @EqualsAndHashCode
    public static class LegibleWorkerDto {
        private Long workerId;
        private String workerName;
        private Boolean available;
        private Gender gender;

        public LegibleWorkerDto(Worker worker){
            this.workerId = worker.getId();
            this.workerName = worker.getFirstname()+" "+worker.getLastname();
            this.gender = worker.getGender();

        }
    }
}


