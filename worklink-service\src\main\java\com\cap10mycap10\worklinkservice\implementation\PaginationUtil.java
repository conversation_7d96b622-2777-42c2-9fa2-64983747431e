package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dto.invoice.InvoiceResult;
import com.cap10mycap10.worklinkservice.dto.payadvice.PayAdviceResult;
import com.cap10mycap10.worklinkservice.dto.trainingsession.TrainingSessionResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;
@Slf4j
public final class PaginationUtil {
    private PaginationUtil() {
    }

    public static Page<ShiftDirectorate> paginateList(final Pageable pageable, List<ShiftDirectorate> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<Location> paginateLocation(final Pageable pageable, List<Location> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<Worker> paginateWorker(final Pageable pageable, List<Worker> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<WorkerResultDto> paginateIWorker(final Pageable pageable, List<WorkerResultDto> list) {
        log.info("Request to view workers result: size = {}, Count = {}", list.size(), list.stream().count());

        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<Shift> paginateShift(final Pageable pageable, List<Shift> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<InvoiceResult> paginateInvoice(final Pageable pageable, List<InvoiceResult> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }
    public static Page<PayAdviceResult> paginatePayAdvice(final Pageable pageable, List<PayAdviceResult> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<TrainingSession> paginateTrainingSession(final Pageable pageable, List<TrainingSession> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<TrainingSessionResultDto> paginateTrainingSessionDto(final Pageable pageable, List<TrainingSessionResultDto> list) {
        int first = Math.min(new Long(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }


    public static Page<WorkerTrainingSession> paginateWorkerTrainingSession(final Pageable pageable, List<WorkerTrainingSession> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<Transport> paginateTransport(final Pageable pageable, List<Transport> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }

    public static Page<Shift> paginateTransportBooking(final Pageable pageable, List<Shift> list) {
        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
        int last = Math.min(first + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(first, last), pageable, list.size());
    }
//    public static Page<WorkerTransportAssignment> paginateWorkerTransportAssignment(final Pageable pageable, List<WorkerTransportAssignment> list) {
//        int first = Math.min(Long.valueOf(pageable.getOffset()).intValue(), list.size());
//        int last = Math.min(first + pageable.getPageSize(), list.size());
//        return new PageImpl<>(list.subList(first, last), pageable, list.size());
//    }
}
