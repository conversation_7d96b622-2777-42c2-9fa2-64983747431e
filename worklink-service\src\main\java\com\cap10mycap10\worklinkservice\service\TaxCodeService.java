package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.taxCode.TaxCodeDto;
import com.cap10mycap10.worklinkservice.dto.taxCode.TaxCodeUpdateDto;
import com.cap10mycap10.worklinkservice.model.TaxCode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface TaxCodeService {

    void addTaxCode(TaxCodeDto taxCodeDto);

    void deleteTaxCode(Long id);

    TaxCode findById(Long id);

    List<TaxCode> findAll();

    Page<TaxCode> findAllPaged(PageRequest of);


    void save(TaxCodeUpdateDto taxCodeUpdateDto);

    TaxCode getOne(Long id);


}
