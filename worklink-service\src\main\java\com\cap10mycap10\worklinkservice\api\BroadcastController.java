package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import com.cap10mycap10.worklinkservice.model.Notification;
import com.cap10mycap10.worklinkservice.service.BroadcastService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;


@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class BroadcastController {

    private final BroadcastService broadcastService;

    public BroadcastController(final BroadcastService broadcastService) {
        this.broadcastService = broadcastService;
    }

    /*@CreateNotification*/
    @PostMapping(value = "broadcast", consumes = {  MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<Notification> create(
            @RequestParam("title")  String title,
            @RequestParam("body")  String body,
            @RequestParam("sendToAll")  Boolean sendToAll,
            @RequestParam("sendToAssCodes") List<Long> sendToAssCodes,
            @RequestParam("sendToIds")  List<Long> sendToIds,
            @RequestParam("sendToType") WorklinkUserType sendToType,
            @RequestParam("senderType")  WorklinkUserType senderType,
            @RequestParam("sendingAgencyId")  Long sendingAgencyId,
            @RequestParam("sendingClientId")  Long sendingClientId

            ) {

        NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

        notificationCreateDto.setTitle(title);
        notificationCreateDto.setBody(body);
        notificationCreateDto.setSendToAll(sendToAll);
        notificationCreateDto.setSendToAssCodes(sendToAssCodes);
        notificationCreateDto.setSendToIds(sendToIds);
        notificationCreateDto.setSendToType(sendToType);
        notificationCreateDto.setSenderType(senderType);
        notificationCreateDto.setSendingAgencyId(sendingAgencyId);
        notificationCreateDto.setSendingClientId(sendingClientId);

        log.info("Request to broadcast message to : {}", notificationCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(broadcastService.broadcast(notificationCreateDto));
    }

    /*@CreateNotification*/
    @PostMapping(value = "broadcast-attachment", consumes = {  MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<Notification> createWithAttachemnt(
            @RequestParam("file") MultipartFile file,
            @RequestParam("title")  String title,
            @RequestParam("body")  String body,
            @RequestParam("sendToAll")  Boolean sendToAll,
            @RequestParam("sendToAssCodes") List<Long> sendToAssCodes,
            @RequestParam("sendToIds")  List<Long> sendToIds,
            @RequestParam("sendToType") WorklinkUserType sendToType,
            @RequestParam("senderType")  WorklinkUserType senderType,
            @RequestParam("sendingAgencyId")  Long sendingAgencyId,
            @RequestParam("sendingClientId")  Long sendingClientId

            ) {

        NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

        notificationCreateDto.setTitle(title);
        notificationCreateDto.setBody(body);
        notificationCreateDto.setSendToAll(sendToAll);
        notificationCreateDto.setSendToAssCodes(sendToAssCodes);
        notificationCreateDto.setSendToIds(sendToIds);
        notificationCreateDto.setSendToType(sendToType);
        if(file!=null){
            notificationCreateDto.setFile(file);
        }
        notificationCreateDto.setSenderType(senderType);
        notificationCreateDto.setSendingAgencyId(sendingAgencyId);
        notificationCreateDto.setSendingClientId(sendingClientId);

        log.info("Request to broadcast message to : {}", notificationCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(broadcastService.broadcast(notificationCreateDto));
    }


    @GetMapping(value = "sent-notifications/agency/{id}")
    public ResponseEntity<List<Notification>> findAgencySent(@PathVariable("id") Long id) {
        log.info("Request to get sent notification with id : {}", id);
        return ResponseEntity.ok(broadcastService.findAgencySent(id));
    }

 
    @GetMapping(value = "sent-notifications/client/{id}")
    public ResponseEntity<List<Notification>> findClientSent(@PathVariable("id") Long id) {
        log.info("Request to get sent notification with id : {}", id);
        return ResponseEntity.ok(broadcastService.findClientSent(id));
    }

    @DeleteMapping(value = "notification/{id}")
    public ResponseEntity<Object> delete(@PathVariable("id") Long id) {
        log.info("Request to delete sent notification with id : {}", id);
        broadcastService.delete(id);
        return ResponseEntity.ok("Deleted");
    }

 
    @GetMapping(value = "sent-notifications/admin")
    public ResponseEntity<List<Notification>> findAdminSent( ) {
        log.info("Request to get sent notification with id");
        return ResponseEntity.ok(broadcastService.findAdminSent());
    }


}
