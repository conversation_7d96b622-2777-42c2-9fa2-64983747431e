package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.note.NoteResultDto;
import com.cap10mycap10.worklinkservice.model.Note;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface NoteRepository extends JpaRepository<Note, Long> {

    @Query(value = "select id       ,\n" +
            "       note       ,\n" +
            "       agency_id        as agencyId,\n" +
            "       worker_id        as worker_id \n" +
            "from note\n" +
            "where worker_id = ?1" , nativeQuery = true)
    Page<NoteResultDto> findAllWorkerNotes(Long workerId, PageRequest of);
}
