package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.bank.BankCreateDto;
import com.cap10mycap10.worklinkservice.model.Bank;
import com.cap10mycap10.worklinkservice.dto.bank.BankResultDto;
import com.cap10mycap10.worklinkservice.dto.bank.BankUpdateDto;
import org.springframework.web.multipart.MultipartFile;

public interface BankService {
    Bank save(BankCreateDto bankCreateDto);

    BankResultDto findById(Long id);

    BankResultDto findByWorkerId(Long id);

    Bank saveUpdate(BankUpdateDto bankCreateDto);

    void deleteById(Long id);

    Bank getOne(Long bankId);

    void addGeneralSignature(Long workerId, MultipartFile file);
}
