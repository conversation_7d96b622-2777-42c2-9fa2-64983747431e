package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.agencyworkercompliance.IAgencyWorkerCompliance;
import com.cap10mycap10.worklinkservice.model.AgencyWorkerCompliance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AgencyWorkerComplianceRepository extends JpaRepository<AgencyWorkerCompliance, Long> {

    List<AgencyWorkerCompliance> findAllByWorkerIdAndAgencyIdAndComplianceId(Long workerId, Long agencyId, Long complianceId);

    @Query(value = "select *\n" +
            "from agency_worker_compliance\n" +
            "where worker_id = ?1\n" +
            "   and agency_id = ?2\n"+
            "   and compliance_id = ?3\n"
            , nativeQuery = true)
    IAgencyWorkerCompliance findAgencyWorkerCompliance(Long workerId, Long agencyId, Long complianceId);
}