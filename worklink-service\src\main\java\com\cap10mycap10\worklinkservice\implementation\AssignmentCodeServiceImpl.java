package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.dao.AssignmentCodeRepository;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeCreateDto;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeResultDto;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeUpdateDto;
import com.cap10mycap10.worklinkservice.mapper.assignment.AssignmentCodeDtoToAssignmentCode;
import com.cap10mycap10.worklinkservice.mapper.assignment.AssignmentCodeToAssignmentCodeResultDto;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.AssignmentCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.ConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j

public class AssignmentCodeServiceImpl implements AssignmentCodeService {
    @Autowired
    private AgencyService agencyService;

    private final AssignmentCodeRepository assignmentCodeRepository;
    private final AssignmentCodeToAssignmentCodeResultDto toAssignmentCodeResultDto;
    private final AssignmentCodeDtoToAssignmentCode toAssignmentCode;

    public AssignmentCodeServiceImpl(final AssignmentCodeRepository assignmentCodeRepository,
                                     final AssignmentCodeToAssignmentCodeResultDto toAssignmentCodeResultDto,
                                     final AssignmentCodeDtoToAssignmentCode toAssignmentCode) {
        this.assignmentCodeRepository = assignmentCodeRepository;
        this.toAssignmentCodeResultDto = toAssignmentCodeResultDto;
        this.toAssignmentCode = toAssignmentCode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssignmentCodeResultDto save(AssignmentCodeCreateDto assignmentCodeCreateDto) {
        List<AssignmentCode> exists = assignmentCodeRepository.findAllByCode(assignmentCodeCreateDto.getCode());
        if(exists.size()!=0){
            throw new BusinessValidationException("Assignment code already exists. Try a different code.");
        }
        try{
            return toAssignmentCodeResultDto.convert(assignmentCodeRepository.save(toAssignmentCode.convert(assignmentCodeCreateDto)));
        }catch(ConstraintViolationException e){
            log.error(e.toString());
            throw new BusinessValidationException("An assignment code with that code or city already exists.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssignmentCodeResultDto save(AssignmentCodeUpdateDto assignmentCodeUpdateDto) {
        AssignmentCode assignmentCode = getOne(assignmentCodeUpdateDto.getId());
        assignmentCode.setName(assignmentCodeUpdateDto.getName());
        assignmentCode.setCode(assignmentCodeUpdateDto.getCode());

        try{
           return toAssignmentCodeResultDto.convert(assignmentCodeRepository.save(assignmentCode));
        }catch(ConstraintViolationException e){
            log.error(e.toString());
            throw new BusinessValidationException("An assignment code with that code or city already exists.");
        }
    }

    @Override
    public AssignmentCodeResultDto findById(Long id) {

        return toAssignmentCodeResultDto.convert(assignmentCodeRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Assignment code not found")));
    }

    @Override
    public List<AssignmentCodeResultDto> findAll() {
        return assignmentCodeRepository.findAll()
                .stream()
                .map(toAssignmentCodeResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Page<AssignmentCodeResultDto> findAllPaged(PageRequest of) {
        return assignmentCodeRepository.findAll(of)
                .map(toAssignmentCodeResultDto::convert);
    }

    @Override
    public void deleteById(Long id) {
        AssignmentCode assignmentCode = assignmentCodeRepository.findById(id).get();
        try {
            assignmentCodeRepository.deleteById(id);
            assignmentCodeRepository.flush();
        }catch (Exception ex){
            throw new BusinessValidationException("Assignment Code cannot be deleted because it references an existing record");
        }
    }

    @Override
    public AssignmentCode getOne(Long id) {
            return assignmentCodeRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Assignment code does not exist"));
    }

    @Override
    @Transactional
    public List<AssignmentCodeResultDto> findByAgencyId(Long id) {

        Agency agency = agencyService.getOne(id);

        return assignmentCodeRepository.findAllByAgencyServiceId(agency.getService().getId())
                .stream()
                .map(toAssignmentCodeResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<AssignmentCodeResultDto> findByClientId(Long id) {
        return assignmentCodeRepository.findAllByClientServiceId(id)
                .stream()
                .map(toAssignmentCodeResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public AssignmentCodeResultDto findByCode(String code) {
        AssignmentCode assignmentCode = assignmentCodeRepository.findByCode(code);
        return toAssignmentCodeResultDto.convert(assignmentCode);
    }
}
