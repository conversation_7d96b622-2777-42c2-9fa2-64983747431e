-- Migration script to remove the old location_id column from vehicle table
-- This should be run AFTER confirming the new vehicle_location junction table works correctly

-- Step 1: Remove the foreign key constraint first
ALTER TABLE vehicle DROP FOREIGN KEY IF EXISTS fk_vehicle_location;

-- Step 2: Remove the location_id column from vehicle table
ALTER TABLE vehicle DROP COLUMN IF EXISTS location_id;

-- Step 3: Add comment to document the change
ALTER TABLE vehicle COMMENT = 'Vehicle table updated to use many-to-many location relationship via vehicle_location junction table';
