package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.dto.shift.ShiftExpenseClaimDto;
import com.cap10mycap10.worklinkservice.model.ShiftExpenseClaim;
import com.cap10mycap10.worklinkservice.model.ExpenseRate;
import com.cap10mycap10.worklinkservice.service.ShiftExpenseClaimService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ShiftExpenseClaimController {

    @Autowired
    private   ShiftExpenseClaimService agencyExpenseRateService;
 
    @PostMapping(value = "shift/expense-claim", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ExpenseRate> create(@RequestBody ShiftExpenseClaimDto expenseRateDto) {
        log.info("Request to add agency rate with : {}", expenseRateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyExpenseRateService.addShiftExpenseClaim(expenseRateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    @GetMapping(value = "shift/expense-claim/{shiftIds}")
    public ResponseEntity<List<ShiftExpenseClaim>> findShiftExpenseClaims(
                                                                          @PathVariable("shiftIds") List<Long> shiftIds
    ) {
        log.info("Request to get agency expense rates : {} ", shiftIds);
        return ResponseEntity.ok(agencyExpenseRateService.findShiftsClaims(shiftIds));
    }

    /* @UpdateServices*/
    @PutMapping(value = "shift/expense-claim", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> update(@RequestBody ShiftExpenseClaimDto expenseRateUpdateDto) {
        log.info("Request to update service with : {}", expenseRateUpdateDto);
        agencyExpenseRateService.save(expenseRateUpdateDto);
        return ResponseEntity.ok().build();
    }

}
