package com.cap10mycap10.worklinkservice.mapper.assignmentcoderate;

import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateCreateDto;
import com.cap10mycap10.worklinkservice.model.AssignmentRate;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class AssignmentCodeRateDtoToAssignmentCodeRate implements Converter<AssignmentCodeRateCreateDto, AssignmentRate> {
    @Override
    public AssignmentRate convert(AssignmentCodeRateCreateDto assignmentCodeRateCreateDto) {
        return null;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
