package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.ComplianceStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.*;
import java.time.LocalDate;


@Entity
@AllArgsConstructor
@Data
public class WorkerCompliance {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String description;
    private String document;

    private String name;

    private Boolean uploaded;

    private String code;
    private String comment;
    @Enumerated(EnumType.STRING)
    private ComplianceStatus status;

    private LocalDate complianceDate;

    private LocalDate complianceExpiry;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Worker worker;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Agency agency;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Compliance compliance;

    public WorkerCompliance() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public ComplianceStatus getStatus() {
        return status;
    }

    public void setStatus(ComplianceStatus status) {
        this.status = status;
    }

    public LocalDate getComplianceDate() {
        return complianceDate;
    }

    public void setComplianceDate(LocalDate complianceDate) {
        this.complianceDate = complianceDate;
    }

    public LocalDate getComplianceExpiry() {
        return complianceExpiry;
    }

    public void setComplianceExpiry(LocalDate complianceExpiry) {
        this.complianceExpiry = complianceExpiry;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Worker getWorker() {
        return worker;
    }

    public void setWorker(Worker worker) {
        this.worker = worker;
    }

    public Agency getAgency() {
        return agency;
    }

    public void setAgency(Agency agency) {
        this.agency = agency;
    }

    public Compliance getCompliance() {
        return compliance;
    }

    public void setCompliance(Compliance compliance) {
        this.compliance = compliance;
    }
}
