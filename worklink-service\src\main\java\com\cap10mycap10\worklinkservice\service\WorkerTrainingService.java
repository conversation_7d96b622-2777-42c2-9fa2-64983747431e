package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.workertraining.IWorkerTrainingResultDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingCreateDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingResultDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingUpdateDto;
import com.cap10mycap10.worklinkservice.model.WorkerTraining;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface WorkerTrainingService {
    WorkerTrainingResultDto addWorkerTraining(WorkerTrainingCreateDto workerTrainingCreateDto);

    void updateWorkerTraining(WorkerTrainingUpdateDto workerTrainingUpdateDto);

    void deleteWorkerTraining(Long id);

    WorkerTrainingResultDto findById(Long id);

    Page<WorkerTrainingResultDto> findWorkerTrainings(Long workerId, PageRequest of);

    List<WorkerTrainingResultDto> findAgencyWorkerTrainings(Long workerId,Long agencyId, PageRequest of);

    Page<WorkerTraining> findAllPaged(PageRequest of);

    WorkerTraining save(WorkerTrainingUpdateDto workerTrainingUpdateDto);

    WorkerTraining getOne(Long id);

//    WorkerTraining findByIHascoId(Long id);

    void addTrainingDoc(Long workerId, Long compliance, MultipartFile file);
}
