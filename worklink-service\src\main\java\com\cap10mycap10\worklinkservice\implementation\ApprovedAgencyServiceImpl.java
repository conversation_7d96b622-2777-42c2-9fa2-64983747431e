package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.service.ApprovedAgencyService;
import lombok.extern.slf4j.Slf4j;


/*@Service*/
@Slf4j
/*@Transactional(rollbackFor = Exception.class)*/
public class ApprovedAgencyServiceImpl implements ApprovedAgencyService {

/*    private final ApprovedAgencyRepository approvedAgencyRepository;

    public ApprovedAgencyServiceImpl(ApprovedAgencyRepository approvedAgencyRepository) {
        this.approvedAgencyRepository = approvedAgencyRepository;
    }

    @Override
    public ApprovedAgency save(AgencyCreateDto agencyCreateDto) {
        return null;
    }

    @Override
    public ApprovedAgency save(AgencyUpdateDto agencyUpdateDto) {
        return null;
    }

    @Override
    public ApprovedAgency findById(Long payerId, Long agencyId) {
        return null;
    }

    @Override
    public List<ApprovedAgency> findAll() {
        return null;
    }

    @Override
    public Page<ApprovedAgency> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public void deleteById(Long payerId, Long agencyId) {

    }

    @Override
    public ApprovedAgency getOne(Long payerId, Long agencyId) {
        return null;
    }

    @Override
    public Page<Agency> findAllWorkersPaged(Long payerId, PageRequest of) {
        return null;
    }

    @Override
    public List<ApprovedAgency> findAgencyByClient(Long payerId) {

       *//*return approvedAgencyRepository.findApprovedAgenciesByClient_Id(payerId);
*//*
        return null;

    }

    @Override
    public List<String> findEmailsByClient(Long payerId) {
        //return approvedAgencyRepository.findEmailsByClient_Id(payerId);
        return null;
    }*/
}
