package com.cap10mycap10.worklinkservice.dto.trainingsession;

import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionResultsDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TrainingSessionResultDto {

    private Long id;

    private String name;

    private Long trainingId;

    private String trainingName;

    private Long shiftLocationId;

    private String shiftLocationName;

    private String trainerName;
    private Long trainerId;

    private String trainingStatus;

    private String postCode;
    private long applicantCount;

    private String address;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm")
    private LocalDateTime startDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm")
    private LocalDateTime endDateTime;

//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private double breakTime;

    private List<Long> agencyIds;

    private Double trainingCost;

    private Integer vacancies;

    private String notes;

    private Boolean isAgencyPaying;
    private Boolean publishToAllWorkers;
    private Boolean publishToAllAgencies;
    private List<WorkerTrainingSessionResultsDto> bookings;

}
