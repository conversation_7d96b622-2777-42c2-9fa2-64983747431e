package com.cap10mycap10.worklinkservice.mapper.shiftlocation;


import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationCreateDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Location;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ClientService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ShiftLocationDtoToShiftLocation implements Converter<ShiftLocationCreateDto, Location> {

    @Autowired
    private   AuthenticationFacade authenticationFacade;
    @Autowired
    private   ClientService clientService;
    @Autowired
    private  AgencyService agencyService;



    @Override
    public Location convert(ShiftLocationCreateDto dto) {
        Location location = new Location();
        location.setCity(dto.getCity());
        location.setCountry(dto.getCountry());
        location.setIso2(dto.getIso2());
        location.setIso3(dto.getIso3());
        location.setCapital(dto.getCapital());
        location.setName(dto.getAdminName());
        location.setTimeZone(dto.getTimeZone());
        location.setAddress(dto.getAddress());
        location.setPostalCode(dto.getPostalCode());
        location.setState(dto.getState());
        location.setDescription(dto.getDescription());
        // Set locationType if provided and valid
        if (dto.getLocationType() != null) {
            try {
                location.setLocationType(com.cap10mycap10.worklinkservice.model.LocationType.valueOf(dto.getLocationType().toUpperCase()));
            } catch (IllegalArgumentException e) {
                throw new BusinessValidationException("Invalid location type: " + dto.getLocationType());
            }
        } else {
            location.setLocationType(com.cap10mycap10.worklinkservice.model.LocationType.OTHER);
        }
        // Set agency if provided
        if (dto.getAgencyId() != null) {
            // Use AgencyRepository to fetch the agency by id
            Agency agency = agencyService.getOne(dto.getAgencyId());
            if (agency == null) {
                throw new BusinessValidationException("Agency not found for id: " + dto.getAgencyId());
            }
            location.setAgency(agency);
        }
        // Optionally set other fields if needed
        return location;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
