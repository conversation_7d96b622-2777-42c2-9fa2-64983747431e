package com.cap10mycap10.worklinkservice.mapper.assignmentcoderate;

import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateResultDto;
import com.cap10mycap10.worklinkservice.model.AssignmentRate;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class AssignmentCodeRateToAssignmentCodeRateResultDto implements Converter<AssignmentRate, AssignmentCodeRateResultDto> {

    @Override
    public AssignmentCodeRateResultDto convert(AssignmentRate assignmentRate) {

        AssignmentCodeRateResultDto resultDto = new AssignmentCodeRateResultDto();
        resultDto.setId(assignmentRate.getId());

        if (assignmentRate.getAssignmentCode() != null) {
            resultDto.setAssignmentCodeId(assignmentRate.getAssignmentCode().getId());
            resultDto.setAssignmentCode(assignmentRate.getAssignmentCode().getCode());
        }
        if (assignmentRate.getAgent() != null) {
            resultDto.setAgencyId(assignmentRate.getAgent().getId());
            resultDto.setAgencyName(assignmentRate.getAgent().getName());
        }

        if (assignmentRate.getShiftType() != null) {
            resultDto.setShiftType(assignmentRate.getShiftType().getName());
        }
        if (assignmentRate.getClient() != null) {
            resultDto.setClientId(assignmentRate.getClient().getId());
            resultDto.setClientName(assignmentRate.getClient().getName());
        }

        if (assignmentRate.getShiftDirectorate()!=null){
            resultDto.setDirectorate(assignmentRate.getShiftDirectorate().getName());
        }

        if (assignmentRate.getLocation()!=null){
            resultDto.setLocation(assignmentRate.getLocation().getCity());
        }

        resultDto.setDayOfWeek(assignmentRate.getDayOfTheWeek());
        resultDto.setClientRate(assignmentRate.getClientRate());
        resultDto.setEndTime(assignmentRate.getEndTime());
        resultDto.setStartTime(assignmentRate.getStartTime());
        resultDto.setUmbrellaRate(assignmentRate.getUmbrellaRate());
        resultDto.setPayeRate(assignmentRate.getPayeRate());
        resultDto.setPrivateRate(assignmentRate.getPrivateRate());
        resultDto.setCreatedBy(assignmentRate.getCreatedBy());
        return resultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
