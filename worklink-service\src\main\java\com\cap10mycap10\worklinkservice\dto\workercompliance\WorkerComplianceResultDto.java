package com.cap10mycap10.worklinkservice.dto.workercompliance;

import lombok.Data;

@Data
public class WorkerComplianceResultDto implements IWorkerComplianceResultDto {
    private Long id;

    private String code;

    private String name;
    private  Boolean uploaded;
    private  String document;
    private String description;

    private String serviceId;

    private String complianceDate;

    private String complianceExpiry;

    private String workerId;

    private String agencyId;
    private String status;
    private String comment;


    private String getComplianceId;


    public void setComplianceDate(String complianceDate) {
        this.complianceDate = complianceDate;
    }


    @Override
    public String getComplianceId() {
        return null;
    }
}
