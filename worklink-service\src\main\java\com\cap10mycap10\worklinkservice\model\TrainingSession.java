package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.TrainingSessionStatus;
import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import jakarta.persistence.*;
import java.time.*;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;


@Entity
@Data
public class TrainingSession {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinColumn(nullable = false)
    private Agency trainer;


    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    private Boolean getFeedback = true;


    @Column(nullable = false)
    private String supervisor;

    @Column(nullable = false)
    private Boolean publishToAllWorkers = false;

    @Column(nullable = false)
    private Boolean publishToAllAgencies = false;


    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany(mappedBy = "trainingSession", fetch =  FetchType.EAGER)
    private Set<WorkerTrainingSession> workerTrainingSessions = new HashSet<>();


    @ManyToMany()
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinTable(name = "training_session_agency",
            joinColumns = {@JoinColumn(name = "training_session_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "agency_id", referencedColumnName = "id")}
    )
    @JsonIgnore
    private Set<Agency> agencies = new HashSet<>();


    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TrainingSessionStatus trainingStatus = TrainingSessionStatus.NEW;

    @ManyToMany(cascade = { CascadeType.PERSIST,  CascadeType.MERGE  })
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinTable(name = "training_session_worker",
            joinColumns = @JoinColumn(name = "training_session_id"),
            inverseJoinColumns = @JoinColumn(name = "worker_id")
    )
    private Set<Worker> workers = new HashSet<>();

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
        private Training training;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Location location;

    private String postCode;

    @Column(nullable = false)
    private String address;

//    @JsonSerialize(as = DateTime.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    @Column(nullable = false)
    private LocalDateTime startDateTime;

//    @JsonSerialize(as = DateTime.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")

    @Column(nullable = false)
    private LocalDateTime endDateTime;

    private Float breakTimeMins;

    @Column(nullable = false)
    private Double trainingCost;

    @Column(nullable = false)
    private Integer vacancies;

    private String notes;

    @Column(nullable = false)
    private Boolean isAgencyPaying = false;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TrainingSession that = (TrainingSession) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    public long getApplicantCount(){
       return workerTrainingSessions.stream().filter(e->e.getTrainingStatus()== WorkerTrainingSessionStatus.NEW).count();
    }

}
