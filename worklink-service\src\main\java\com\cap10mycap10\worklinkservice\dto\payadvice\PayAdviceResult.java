package com.cap10mycap10.worklinkservice.dto.payadvice;


import com.cap10mycap10.worklinkservice.dto.shiftexpenseclaim.ShiftExpenseClaimResultDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PayAdviceResult {

    private Long id;

    private Long agentId;

    private Long workerId;
    private String workerName;

    private BigDecimal totalAmount;

    private String paymentRef;

    private String payAdviceDate;
    private String workerGross;
    private String payAdviceStatus;

    private List<PayAdviceItemResult> payAdviceItemResult;
    private List<ShiftExpenseClaimResultDto> shiftExpenseClaims;
    private List<IPayAdvice> ipayAdviceItemResult;
}
