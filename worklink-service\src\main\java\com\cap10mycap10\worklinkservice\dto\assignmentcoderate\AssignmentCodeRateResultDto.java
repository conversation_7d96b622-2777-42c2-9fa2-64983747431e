package com.cap10mycap10.worklinkservice.dto.assignmentcoderate;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalTime;

@Data
public class AssignmentCodeRateResultDto {

    private Long id;

    private Long assignmentCodeId;

    private String  assignmentCode;

    private Long agencyId;

    private String agencyName;

    private Long clientId;

    private String clientName;

    private String shiftType;

    private DayOfWeek dayOfWeek;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private LocalTime startTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
    private LocalTime endTime;

    private BigDecimal clientRate;

    private BigDecimal privateRate;

    private BigDecimal umbrellaRate;

    private BigDecimal payeRate;

    private String createdBy;

    private  String directorate;

    private String location;
}
