package com.cap10mycap10.worklinkservice.mapper.vehiclebooking;

import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDepositDto;
import com.cap10mycap10.worklinkservice.model.VehicleBookingDeposit;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Function;

@Service
@Transactional
public class VehicleBookingDepositToVehicleBookingDepositDto implements Function<VehicleBookingDeposit, VehicleBookingDepositDto> {

    @Override
    @Transactional
    public VehicleBookingDepositDto apply(VehicleBookingDeposit deposit) {
        VehicleBookingDepositDto depositDto = new VehicleBookingDepositDto();
        
        depositDto.setId(deposit.getId());
        depositDto.setBookingId(deposit.getVehicleBooking().getId());
        depositDto.setDepositAmount(deposit.getDepositAmount());
        depositDto.setPaymentMethod(deposit.getPaymentMethod());
        depositDto.setDepositDate(deposit.getDepositDate());
        
        return depositDto;
    }
}
