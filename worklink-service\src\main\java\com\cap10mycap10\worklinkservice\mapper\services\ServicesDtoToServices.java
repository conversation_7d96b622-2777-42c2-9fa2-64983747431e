package com.cap10mycap10.worklinkservice.mapper.services;

import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
import com.cap10mycap10.worklinkservice.dto.service.ServiceCreateDto;
import com.cap10mycap10.worklinkservice.model.Services;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class ServicesDtoToServices implements Converter<ServiceCreateDto, Services> {

    private final AuthenticationFacade authenticationFacade;


    public ServicesDtoToServices(AuthenticationFacade authenticationFacade) {
        this.authenticationFacade = authenticationFacade;
    }

    @Override
    public Services convert(ServiceCreateDto serviceCreateDto) {
        Services services = new Services();
        services.setName(serviceCreateDto.getName());
        services.setCreatedBy(authenticationFacade.getAuthentication().getName());
        return services;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
