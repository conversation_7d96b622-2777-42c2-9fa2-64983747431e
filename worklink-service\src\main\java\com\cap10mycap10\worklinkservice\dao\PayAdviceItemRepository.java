package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.payadvice.IPayAdvice;
import com.cap10mycap10.worklinkservice.model.PayAdviceItem;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface PayAdviceItemRepository extends JpaRepository<PayAdviceItem, Long> {

    List<IPayAdvice> findAllByPayAdvice_Id(Long payAdviceId);
}
