package com.cap10mycap10.worklinkservice.dto.transport;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class TransportTeamLeaderUpdateDto {
    private Long Id;
    private List<String> propertyList;
    private List<String> medicationList;
    private Float cashHandover;
    private Boolean pDroppedOff;
    private String pComment;
    private String signature;
    private char pfCleanliness;
    private char pfCourtesy;
    private char pfKnowledge;
    private char pfTreatment;
    private char pfAdvice;
    private char pfComfort;
    private char pfExperience;
    private LocalDateTime dropTime;
    private String patientRecipient;
    private String recipientContact;
    private String recipientRole;
    private String recipientSignature;
    private String newAddress;
    private String newPostCode;
    private String newPhone;
    private String newEmail;
}
