package com.cap10mycap10.worklinkservice.controller;

import com.cap10mycap10.worklinkservice.dto.email.AgencyEmailConfigurationDto;
import com.cap10mycap10.worklinkservice.dto.email.AgencyEmailConfigurationPublicDto;
import com.cap10mycap10.worklinkservice.dto.email.AgencyEmailConfigurationResponseDto;
import com.cap10mycap10.worklinkservice.dto.email.TestEmailDto;
import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import com.cap10mycap10.worklinkservice.service.AgencyEmailConfigurationService;
import com.cap10mycap10.worklinkservice.service.EmailSenderFactory;
import com.cap10mycap10.worklinkservice.service.EncryptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * REST Controller for managing agency email configurations
 */
@RestController
@RequestMapping("/api/v1/agency-email-config")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class AgencyEmailConfigurationController {

    private final AgencyEmailConfigurationService emailConfigService;
    private final EmailSenderFactory emailSenderFactory;
    private final EncryptionService encryptionService;

    /**
     * Get email configuration for an agency
     */
    @GetMapping("/agency/{agencyId}")
//    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<AgencyEmailConfigurationDto> getConfigurationByAgencyId(@PathVariable Long agencyId) {
        log.info("Getting email configuration for agency ID: {}", agencyId);
        
        Optional<AgencyEmailConfiguration> config = emailConfigService.findByAgencyId(agencyId);
        return config.map(agencyEmailConfiguration -> ResponseEntity.ok(AgencyEmailConfigurationDto.fromEntity(agencyEmailConfiguration))).orElseGet(() -> ResponseEntity.ok(null));
    }

    /**
     * Create or update email configuration for an agency
     */
    @PostMapping("/agency/{agencyId}")
//    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<AgencyEmailConfigurationPublicDto> createOrUpdateConfiguration(
            @PathVariable Long agencyId,
            @Valid @RequestBody AgencyEmailConfigurationDto configDto) {

        log.info("Creating/updating email configuration for agency ID: {}", agencyId);

        try {
            // Ensure the agency ID matches
            configDto.setAgencyId(agencyId);

            // Check if configuration already exists
            Optional<AgencyEmailConfiguration> existingConfig = emailConfigService.findByAgencyId(agencyId);
            if (existingConfig.isPresent()) {
                // Update existing configuration
                AgencyEmailConfiguration existing = existingConfig.get();
                configDto.setId(existing.getId());
                configDto.setCreatedBy(existing.getCreatedBy());
            }

            // Convert DTO to entity and save
            AgencyEmailConfiguration entity = configDto.toEntity();
            AgencyEmailConfiguration savedConfig = emailConfigService.save(entity);

            // Encrypt the decrypted password for external API response
            String encryptedPassword = encryptionService.encrypt(savedConfig.getDecryptedSmtpPassword());
            return ResponseEntity.ok(AgencyEmailConfigurationPublicDto.fromEntity(savedConfig, encryptedPassword));

        } catch (Exception e) {
            log.error("Error creating/updating email configuration for agency ID: {}", agencyId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Test email configuration
     */
    @PostMapping("/test")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<Map<String, Object>> testConfiguration(@Valid @RequestBody TestEmailDto testDto) {
        log.info("Testing email configuration for agency ID: {}", testDto.getAgencyId());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean testResult = emailConfigService.testConfiguration(
                testDto.getAgencyId(), 
                testDto.getTestEmailAddress()
            );
            
            response.put("success", testResult);
            response.put("message", testResult ? 
                "Test email sent successfully" : 
                "Test email failed - please check your configuration");
            
            // Test result logged above
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error testing email configuration for agency ID: {}", testDto.getAgencyId(), e);
            response.put("success", false);
            response.put("message", "Test failed: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * Activate email configuration
     */
    @PutMapping("/agency/{agencyId}/activate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<AgencyEmailConfigurationDto> activateConfiguration(@PathVariable Long agencyId) {
        log.info("Activating email configuration for agency ID: {}", agencyId);
        
        try {
            AgencyEmailConfiguration config = emailConfigService.activateConfiguration(agencyId);
            return ResponseEntity.ok(AgencyEmailConfigurationDto.fromEntity(config));
        } catch (Exception e) {
            log.error("Error activating email configuration for agency ID: {}", agencyId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Deactivate email configuration
     */
    @PutMapping("/agency/{agencyId}/deactivate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<AgencyEmailConfigurationDto> deactivateConfiguration(@PathVariable Long agencyId) {
        log.info("Deactivating email configuration for agency ID: {}", agencyId);
        
        try {
            AgencyEmailConfiguration config = emailConfigService.deactivateConfiguration(agencyId);
            return ResponseEntity.ok(AgencyEmailConfigurationDto.fromEntity(config));
        } catch (Exception e) {
            log.error("Error deactivating email configuration for agency ID: {}", agencyId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Delete email configuration
     */
    @DeleteMapping("/agency/{agencyId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, String>> deleteConfiguration(@PathVariable Long agencyId) {
        log.info("Deleting email configuration for agency ID: {}", agencyId);
        
        try {
            emailConfigService.deleteByAgencyId(agencyId);

            Map<String, String> response = new HashMap<>();
            response.put("message", "Email configuration deleted successfully");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error deleting email configuration for agency ID: {}", agencyId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get all email configurations (Admin only)
     */
    @GetMapping("/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<AgencyEmailConfigurationDto>> getAllConfigurations() {
        log.info("Getting all email configurations");
        
        try {
            List<AgencyEmailConfiguration> configs = emailConfigService.findAll();
            List<AgencyEmailConfigurationDto> dtos = configs.stream()
                .map(AgencyEmailConfigurationDto::fromEntity)
                .collect(Collectors.toList());
            
            return ResponseEntity.ok(dtos);
            
        } catch (Exception e) {
            log.error("Error getting all email configurations", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get the active email configuration for an agency.
     * This endpoint returns the configuration with decrypted password for use by other services.
     *
     * @param agencyId the agency ID
     * @return the active email configuration with decrypted password
     */
    @GetMapping("/agency/{agencyId}/active")
    public ResponseEntity<AgencyEmailConfigurationResponseDto> getActiveConfiguration(@PathVariable Long agencyId) {
        log.info("Getting active email configuration for agency: {}", agencyId);
        Optional<AgencyEmailConfiguration> config = emailConfigService.findActiveByAgencyId(agencyId);

        if (config.isPresent()) {
            // Convert to response DTO with decrypted password
            AgencyEmailConfigurationResponseDto responseDto = AgencyEmailConfigurationResponseDto.fromEntity(config.get());
            return ResponseEntity.ok(responseDto);
        } else {
            log.warn("No active email configuration found for agency: {}", agencyId);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Check if agency has custom email configuration
     */
    @GetMapping("/agency/{agencyId}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('AGENCY_ADMIN')")
    public ResponseEntity<Map<String, Object>> getConfigurationStatus(@PathVariable Long agencyId) {
        log.info("Getting email configuration status for agency ID: {}", agencyId);
        
        Map<String, Object> status = new HashMap<>();
        status.put("agencyId", agencyId);
        status.put("hasConfiguration", emailConfigService.existsByAgencyId(agencyId));
        status.put("hasActiveConfiguration", emailConfigService.hasActiveConfiguration(agencyId));
        status.put("hasVerifiedConfiguration", emailConfigService.hasVerifiedActiveConfiguration(agencyId));
        status.put("isUsingCustomEmail", emailSenderFactory.hasCustomEmailConfiguration(agencyId));
        
        return ResponseEntity.ok(status);
    }


}
