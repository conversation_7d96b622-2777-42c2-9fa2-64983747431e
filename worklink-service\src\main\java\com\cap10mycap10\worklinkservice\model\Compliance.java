package com.cap10mycap10.worklinkservice.model;

import lombok.*;

import jakarta.persistence.*;



@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Compliance {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String code;

    private String name;

    private String description;

    @ManyToOne(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Services services;


}
