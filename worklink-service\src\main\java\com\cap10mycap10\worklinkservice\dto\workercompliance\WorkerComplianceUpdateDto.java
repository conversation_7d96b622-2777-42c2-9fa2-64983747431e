package com.cap10mycap10.worklinkservice.dto.workercompliance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class WorkerComplianceUpdateDto {

    private Long id;

    private String description;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate complianceDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate complianceExpiry;

    private Long agencyId;
    private Long workerId;

    private String comment;

    private String status;
}
