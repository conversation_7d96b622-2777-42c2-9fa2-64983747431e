package com.cap10mycap10.worklinkservice.mapper.client;

import com.cap10mycap10.worklinkservice.dao.VehicleBookingRepository;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;

import static java.util.Objects.nonNull;

@Component
public class ClientToClientDto implements Converter<Client, ClientDto> {

    @Autowired
    private VehicleBookingRepository bookingRepository;

    @Override
    public ClientDto convert(Client client) {

        ClientDto clientDto = new ClientDto();
        clientDto.setName(client.getName());
        clientDto.setId(client.getId());
        clientDto.setSbsCode(client.getSbsCode());
        clientDto.setPurchaseOrder(client.getPurchaseOrder());
        clientDto.setTelephone(client.getTelephone());
        clientDto.setAddress(client.getAddress());
        clientDto.setEmail(client.getEmail());
        clientDto.setBillingEmail(client.getBillingEmail());
        clientDto.setLogo(client.getLogo());
        clientDto.setRating(getRating(client));
        clientDto.setTotalBooking(client.getValidVehicleBookings().size());
        clientDto.setVerified(client.isVerified());
        clientDto.setClientDocs(client.getClientDocs());
        if(nonNull(client.getService()))clientDto.setServiceId(client.getService().getId());
        return clientDto;

    }

    private Float getRating(Client client) {
        return bookingRepository.findAverageRatingByClientId(client.getId());
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
