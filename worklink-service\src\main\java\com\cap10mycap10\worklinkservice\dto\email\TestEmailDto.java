package com.cap10mycap10.worklinkservice.dto.email;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * DTO for testing email configuration
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestEmailDto {

    @NotNull(message = "Agency ID is required")
    private Long agencyId;

    @Email(message = "Test email address must be valid")
    @NotBlank(message = "Test email address is required")
    private String testEmailAddress;

    private String customMessage;
}
