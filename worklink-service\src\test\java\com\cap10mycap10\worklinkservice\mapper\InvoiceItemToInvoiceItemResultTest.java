package com.cap10mycap10.worklinkservice.mapper;

import com.cap10mycap10.worklinkservice.dto.invoice.InvoiceItemResult;
import com.cap10mycap10.worklinkservice.mapper.invoice.InvoiceItemToInvoiceItemResult;
import com.cap10mycap10.worklinkservice.model.InvoiceItem;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class InvoiceItemToInvoiceItemResultTest {

    @InjectMocks
    private InvoiceItemToInvoiceItemResult mapper;

    private InvoiceItem invoiceItem;

    @BeforeEach
    void setUp() {
        invoiceItem = new InvoiceItem();
        invoiceItem.setId(1L);
        invoiceItem.setDescription("Car rental for MONDAY Toyota Camry");
        invoiceItem.setTotal(new BigDecimal("115.00"));
        invoiceItem.setRate(new BigDecimal("100.00"));
        invoiceItem.setNumberOfHoursWorked(8.0);
        
        // Set tax-related fields
        invoiceItem.setTaxExempt(false);
        invoiceItem.setTaxRate(new BigDecimal("15.00"));
        invoiceItem.setTaxAmount(new BigDecimal("15.00"));
        invoiceItem.setNetAmount(new BigDecimal("100.00"));
        invoiceItem.setTaxInclusive(true);
    }

    @Test
    void testConvert_ShouldIncludeTaxFields() {
        // Given
        List<InvoiceItem> invoiceItems = Arrays.asList(invoiceItem);

        // When
        List<InvoiceItemResult> results = mapper.convert(invoiceItems);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());
        
        InvoiceItemResult result = results.get(0);
        
        // Verify basic fields
        assertEquals(invoiceItem.getId(), result.getId());
        assertEquals(invoiceItem.getDescription(), result.getDescription());
        assertEquals(invoiceItem.getTotal(), result.getTotal());
        assertEquals(invoiceItem.getRate(), result.getRate());
        assertEquals(invoiceItem.getNumberOfHoursWorked(), result.getNumberOfHoursWorked());
        
        // Verify tax fields are properly mapped
        assertEquals(invoiceItem.getTaxExempt(), result.getTaxExempt());
        assertEquals(invoiceItem.getTaxRate(), result.getTaxRate());
        assertEquals(invoiceItem.getTaxAmount(), result.getTaxAmount());
        assertEquals(invoiceItem.getNetAmount(), result.getNetAmount());
        assertEquals(invoiceItem.getTaxInclusive(), result.getTaxInclusive());
    }

    @Test
    void testConvert_WithTaxExemptItem() {
        // Given
        invoiceItem.setTaxExempt(true);
        invoiceItem.setTaxRate(BigDecimal.ZERO);
        invoiceItem.setTaxAmount(BigDecimal.ZERO);
        invoiceItem.setNetAmount(new BigDecimal("100.00"));
        invoiceItem.setTaxInclusive(false);

        List<InvoiceItem> invoiceItems = Arrays.asList(invoiceItem);

        // When
        List<InvoiceItemResult> results = mapper.convert(invoiceItems);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());

        InvoiceItemResult result = results.get(0);

        // Verify tax exempt item fields
        assertTrue(result.getTaxExempt());
        assertEquals(0, result.getTaxRate().compareTo(BigDecimal.ZERO));
        assertEquals(0, result.getTaxAmount().compareTo(BigDecimal.ZERO));
        assertEquals(0, result.getNetAmount().compareTo(new BigDecimal("115.00"))); // Calculated as total - taxAmount = 115.00 - 0.00
        assertFalse(result.getTaxInclusive());
    }

    @Test
    void testConvert_WithDefaultTaxFields() {
        // Given - Create a new invoice item with default values
        InvoiceItem defaultItem = new InvoiceItem();
        defaultItem.setId(2L);
        defaultItem.setDescription("Default item");
        defaultItem.setTotal(new BigDecimal("50.00"));
        defaultItem.setRate(new BigDecimal("50.00"));
        defaultItem.setNumberOfHoursWorked(4.0);

        List<InvoiceItem> invoiceItems = Arrays.asList(defaultItem);

        // When
        List<InvoiceItemResult> results = mapper.convert(invoiceItems);

        // Then
        assertNotNull(results);
        assertEquals(1, results.size());

        InvoiceItemResult result = results.get(0);

        // Verify default tax fields are handled properly
        assertFalse(result.getTaxExempt()); // Default is false (from entity)
        assertEquals(0, result.getTaxRate().compareTo(BigDecimal.ZERO)); // Default is ZERO (from entity)
        assertEquals(0, result.getTaxAmount().compareTo(BigDecimal.ZERO)); // Default is ZERO (from entity getter)
        assertEquals(0, result.getNetAmount().compareTo(new BigDecimal("50.00"))); // Calculated as total - taxAmount = 50.00 - 0.00
        assertFalse(result.getTaxInclusive()); // Default is false (from entity)
    }
}
