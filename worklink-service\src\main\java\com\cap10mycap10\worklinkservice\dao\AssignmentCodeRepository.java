package com.cap10mycap10.worklinkservice.dao;


import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AssignmentCodeRepository extends JpaRepository<AssignmentCode, Long> {

    @Query(value = "select * from assignment_code where agency_id =?1", nativeQuery = true)
    List<AssignmentCode> findAllByAgencyId(Long id);
    List<AssignmentCode> findAllByCode(String code);


    @Query(value = "select * from assignment_code where agency_id =?1", nativeQuery = true)
    List<AssignmentCode> findAllByClientId(Long id);


    @Query(value = "select * from assignment_code where services_id in (\n" +
            "    select service_id from client where client.id =?1\n" +
            "    )", nativeQuery = true)
    List<AssignmentCode> findAllByClientServiceId(Long clientId);


    @Query(value = "select * from assignment_code where  services_id = ?1", nativeQuery = true)
    List<AssignmentCode> findAllByAgencyServiceId(Long serviceId);

    AssignmentCode findByCode(String code);


}
