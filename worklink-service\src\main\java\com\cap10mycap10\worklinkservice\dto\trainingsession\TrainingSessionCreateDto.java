package com.cap10mycap10.worklinkservice.dto.trainingsession;

import com.cap10mycap10.worklinkservice.enums.TrainingStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.Size;

import java.time.*;
import java.util.Set;

@Data
public class TrainingSessionCreateDto {
    private String name;
    private String supervisor;

    private Long trainingId;
    private Boolean getFeedback;

    private Boolean publishToAllWorkers;
    private Boolean publishToAllAgencies;

    private Long trainerId;

    @Enumerated(EnumType.STRING)
    private TrainingStatus trainingStatus;

    private Long shiftLocationId;

    private Set<Long> agencyIds;

    @Size(max = 16)
    private String postCode;

    private String address;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime startDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime endDateTime;

    private Float breakTime;


    private Double trainingCost;

    private Integer vacancies;

    private String notes;

    private Boolean isAgencyPaying;
}
