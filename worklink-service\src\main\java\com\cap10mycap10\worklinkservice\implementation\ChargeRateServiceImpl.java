package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.ChargeRateRepository;
import com.cap10mycap10.worklinkservice.dto.chargerate.ChargeRateDto;
import com.cap10mycap10.worklinkservice.dto.chargerate.ChargeRateUpdateDto;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.cap10mycap10.worklinkservice.model.AssignmentRate;
import com.cap10mycap10.worklinkservice.model.ChargeRate;
import com.cap10mycap10.worklinkservice.model.ShiftType;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.AssignmentCodeService;
import com.cap10mycap10.worklinkservice.service.ChargeRateService;
import com.cap10mycap10.worklinkservice.service.ShiftTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ChargeRateServiceImpl implements ChargeRateService {

    private final ChargeRateRepository chargeRateRepository;
    private final AgencyService agencyService;

    private final ShiftTypeService shiftTypeService;
    private final AssignmentCodeService assignmentCodeService;

    public ChargeRateServiceImpl(ChargeRateRepository chargeRateRepository, AgencyService agencyService, ShiftTypeService shiftTypeService, AssignmentCodeService assignmentCodeService) {
        this.chargeRateRepository = chargeRateRepository;
        this.agencyService = agencyService;
        this.shiftTypeService = shiftTypeService;
        this.assignmentCodeService = assignmentCodeService;
    }

    @Override
    public void save(ChargeRateDto chargeRateDto) {
        ChargeRate chargeRate = new ChargeRate();
        chargeRate.setChargeRate(chargeRateDto.getChargeRate());
        chargeRate.setShiftType(shiftTypeService.getOne(chargeRateDto.getShiftTypeId()));
        chargeRate.setAssignmentCode(assignmentCodeService.getOne(chargeRateDto.getAssignmentCodeId()));

        chargeRateRepository.save(chargeRate);
    }

    @Override
    public ChargeRate findById(Long id) {
        return getOne(id);
    }

    @Override
    public List<AssignmentRate> findForAgencyBilling(Long agencyId, ShiftType shiftType, AssignmentCode assignmentCode) {
        List<ChargeRate> rate = new ArrayList<>();
        List<ChargeRate> foundRates = chargeRateRepository.findByShiftTypeAndAssignmentCode( shiftType, assignmentCode);
        if(foundRates.isEmpty()){
            throw  new BusinessValidationException("Rates not set.");
        }

        rate.add(foundRates.get(0));
        List<AssignmentRate> assignmentRates =  new ArrayList<>();
        for (ChargeRate chargeRate : rate) {
            AssignmentRate assignmentRate = new AssignmentRate();
            assignmentRate.setClientRate(chargeRate.getChargeRate());
            assignmentRate.setPayeRate(chargeRate.getChargeRate());
            assignmentRate.setPrivateRate(chargeRate.getChargeRate());
            assignmentRate.setUmbrellaRate(chargeRate.getChargeRate());

            assignmentRate.setStartTime(LocalTime.MIDNIGHT);
            assignmentRate.setEndTime(LocalTime.MIDNIGHT);

            assignmentRate.setShiftType(chargeRate.getShiftType());
            assignmentRate.setAssignmentCode(chargeRate.getAssignmentCode());

            assignmentRates.add(assignmentRate);

        }

        return assignmentRates;

    }

    @Override
    public List<ChargeRate> findAll() {
        return chargeRateRepository.findAll();
    }

    @Override
    public Page<ChargeRate> findAllPaged(PageRequest of) {
        return chargeRateRepository.findAll(of);
    }

    @Override
    public void deleteById(Long id) {
        ChargeRate chargeRate = getOne(id);
        chargeRateRepository.delete(chargeRate);
    }

    @Override
    public ChargeRate save(ChargeRateUpdateDto chargeRateUpdateDto) {
        ChargeRate chargeRate = getOne(chargeRateUpdateDto.getId());
        chargeRate.setChargeRate(chargeRateUpdateDto.getChargeRate());
        return chargeRateRepository.save(chargeRate);
    }

    @Override
    public ChargeRate getOne(Long id) {
        return chargeRateRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Charge rate does not exist"));
    }
}
