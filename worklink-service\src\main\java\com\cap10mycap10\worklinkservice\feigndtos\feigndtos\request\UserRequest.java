package com.cap10mycap10.worklinkservice.feigndtos.feigndtos.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserRequest {

    private String firstName;

    private String lastName;

    private String username;

    private String email;

    private Long agentId;

    private Long clientId;

    private Long roleId;

   /* public static UserRequest buildRequest(Agent agency, AdministratorCreateDto request) {
        return UserRequest.builder()
                .email(request.getEmail())
                .firstName(request.getFirstname())
                .lastName(request.getLastname())
                .username(request.getFirstname().concat(request.getLastname()))
                .roleId(3L)
                .payeeId(agency.getId())
                .build();
    }*/


}