package com.cap10mycap10.worklinkservice.dto.asset.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleAvailabilityDto {
    private Long vehicleId;
    private List<LocalDate> unavailableDates;
    private List<LocalDate> bookedDates;
    private String message;
}
