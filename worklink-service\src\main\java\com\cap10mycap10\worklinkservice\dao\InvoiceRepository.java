package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.enums.InvoiceStatus;
import com.cap10mycap10.worklinkservice.enums.InvoiceType;
import com.cap10mycap10.worklinkservice.model.Invoice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.ZonedDateTime;
import java.util.List;

public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    Page<Invoice> findAllByClientIdAndInvoiceTypeAndPublishedAndInvoiceStatusNot(Long clientId,InvoiceType invoiceType, Boolean published, InvoiceStatus status, Pageable page);
    Page<Invoice> findAllByAgencyIdAndPublishedAndInvoiceTypeAndInvoiceStatusNot(Long agencyId, <PERSON><PERSON><PERSON> published, InvoiceType invoiceType, InvoiceStatus status, Pageable page);
    List<Invoice> findAllByAgencyIdAndInvoiceTypeAndInvoiceStatusNot(Long agencyId, InvoiceType invoiceType, InvoiceStatus status);
    List<Invoice> findAllByAgencyIdAndInvoiceTypeAndInvoiceStatus(Long agencyId, InvoiceType invoiceType, InvoiceStatus status);
    List<Invoice> findAllByPayeeIdAndInvoiceTypeAndInvoiceStatusNot(Long agencyId, InvoiceType invoiceType, InvoiceStatus status);
    List<Invoice> findAllByPayeeIdAndInvoiceTypeAndInvoiceStatus(Long agencyId, InvoiceType invoiceType, InvoiceStatus status);
    Page<Invoice> findAllByWorkerIdAndInvoiceTypeOrderByInvoiceStatusDesc(Long workerId, InvoiceType invoiceType, Pageable page);
    Page<Invoice> findAllByAgencyIdAndInvoiceTypeAndInvoiceStatus(Long agencyId, InvoiceType invoiceType, InvoiceStatus status, Pageable page);




    @Query(value = "select *      \n" +
            "from invoice\n" +
            "where agency_id = ?1 and invoice_status in ('PAID') and invoice_type = 'CLIENT'" , nativeQuery = true)
    Page<Invoice> findAllPaidByAgencyId(Long agencyId, PageRequest of);

    @Query(value = "select *      \n" +
            "from invoice\n" +
            "where id in ( select invoice_id from invoice_item \n" +
            " where training_id = ?1) and invoice_type = 'WORKERTRAINING' and worker_id= ?2 " , nativeQuery = true)
    List<Invoice> findIndividualTrainingInvoice(Long trainingId, Long workerId);

    @Query(value = "select *      \n" +
            "from invoice\n" +
            "where agency_id = ?1 and invoice_status in ('UNPAID', 'PARTLY') and invoice_type = 'CLIENT'" , nativeQuery = true)
    Page<Invoice> findAllUnpaidByAgencyId(Long agencyId, PageRequest of);

    @Query(value = "select *      \n" +
            "from invoice\n" +
            "where invoice_status in ('UNPAID', 'PARTLY') and invoice_type = 'AGENCY'" , nativeQuery = true)
    Page<Invoice> findAllUnpaid( PageRequest of);




    @Query("SELECT i FROM Invoice i " +
            "WHERE (:rentalId IS NULL OR i.agency.id = :rentalId) " +
            "AND (:status IS NULL OR i.settlementStatus = :status) " +
            "AND (:startDate IS NULL OR i.vehicleBooking.start >= :startDate) " +
            "AND (:endDate IS NULL OR i.vehicleBooking.end <= :endDate) " +
            "AND (:bookingId IS NULL OR i.vehicleBooking.id = :bookingId) " +
            "AND i.invoiceStatus = 'PAID' " )
    Page<Invoice> findPaidInvoicesDueForSettlement(@Param("rentalId") Long rentalId,
                                                   @Param("status") String status,
                                                   @Param("startDate") ZonedDateTime start,
                                                   @Param("endDate") ZonedDateTime end,
                                                   @Param("bookingId") Long bookingId,
                                                   Pageable pageable);



}
