package com.cap10mycap10.worklinkservice.service;

public interface ApprovedAgencyService {

 /*   ApprovedAgency save(AgencyCreateDto agencyCreateDto);

    ApprovedAgency save(AgencyUpdateDto agencyUpdateDto);

    ApprovedAgency findById(Long payerId, Long agencyId);

    List<ApprovedAgency> findAll();

    Page<ApprovedAgency> findAllPaged(PageRequest of);

    void deleteById(Long payerId, Long agencyId);

    ApprovedAgency getOne(Long payerId, Long agencyId);

    Page<Agency> findAllWorkersPaged(Long payerId, PageRequest of);

    List<ApprovedAgency> findAgencyByClient(Long payerId);

    List<String> findEmailsByClient(Long payerId);*/

}
