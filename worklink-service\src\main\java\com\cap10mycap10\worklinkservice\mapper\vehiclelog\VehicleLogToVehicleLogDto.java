package com.cap10mycap10.worklinkservice.mapper.vehiclelog;
 
import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.mapper.transport.TransportToTransportDto;
import com.cap10mycap10.worklinkservice.model.VehicleLog;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Function;

import static java.util.Objects.nonNull;

@Slf4j
@Service
@EqualsAndHashCode
public class VehicleLogToVehicleLogDto implements Function<VehicleLog, VehicleLogDto> {

    @Autowired
    private TransportToTransportDto toTransportDto;

    @Override
    @Transactional
    public VehicleLogDto apply(VehicleLog vehicleLog) {

        VehicleLogDto vlog = new VehicleLogDto();

        var vehicle = vehicleLog.getVehicle();
        var transport = vehicleLog.getTransport();
        if(nonNull(transport)){
            vlog.transport = toTransportDto.apply(transport);
        }

        vlog.id = vehicleLog.getId();
        vlog.startMileage = vehicleLog.getStartMileage();

        vlog.endMileage = vehicleLog.getEndMileage();
        vlog.vehicle = (nonNull(vehicle.getName())?vehicle.getName():"")+" "
                +(nonNull(vehicle.getModel())?vehicle.getModel():"")+" "
                +(nonNull(vehicle.getRegno())?vehicle.getRegno():"");
        vlog.totalMileage = vehicleLog.getTotalMileage();
        vlog.notes = vehicleLog.getNotes();
        vlog.feedback = vehicleLog.getFeedback();
        vlog.damageReport = vehicleLog.getDamageReport();
        vlog.damageDescriptions = vehicleLog.getDamageDescriptions();
        vlog.damageDoc = vehicleLog.getDamageDoc();
        vlog.approvedByName = vehicleLog.getApprovedByName();
        vlog.status = vehicleLog.getStatus();



        vlog.indicators = vehicleLog.getIndicators();
        vlog.comment = vehicleLog.getComment();
        vlog.drivingControls = vehicleLog.getDrivingControls();
        vlog.wheelCondition = vehicleLog.getWheelCondition();
        vlog.tyreInflation = vehicleLog.getTyreInflation();
        vlog.brakes = vehicleLog.getBrakes();
        vlog.windscreen = vehicleLog.getWindscreen();
        vlog.mirrors = vehicleLog.getMirrors();
        vlog.speedometer = vehicleLog.getSpeedometer();
        vlog.battery = vehicleLog.getBattery();
        vlog.fuel = vehicleLog.getFuel();
        vlog.seatbelt = vehicleLog.getSeatbelt();
        vlog.doors = vehicleLog.getDoors();
        vlog.oil = vehicleLog.getOil();
        vlog.engineCheckLight = vehicleLog.getEngineCheckLight();
        vlog.warningLight = vehicleLog.getWarningLight();
        vlog.litter = vehicleLog.getLitter();
        vlog.hardSurface = vehicleLog.getHardSurface();
        vlog.seats = vehicleLog.getSeats();
        vlog.equipment = vehicleLog.getEquipment();
        vlog.sanitizer = vehicleLog.getSanitizer();
        vlog.cellArea = vehicleLog.getCellArea();
        vlog.lamp = vehicleLog.getLamp();
        vlog.sideReapter = vehicleLog.getSideReapter();
        vlog.stoplamp = vehicleLog.getStoplamp();
        vlog.reflectors = vehicleLog.getReflectors();
        vlog.markers = vehicleLog.getMarkers();
        vlog.warningdevices = vehicleLog.getWarningdevices();
        vlog.mirror = vehicleLog.getMirror();
        vlog.drivingcontrol = vehicleLog.getDrivingcontrol();
        vlog.body = vehicleLog.getBody();
        vlog.horn = vehicleLog.getHorn();
        vlog.wipers = vehicleLog.getWipers();
        vlog.washers = vehicleLog.getWashers();
        vlog.fluidleaks = vehicleLog.getFluidleaks();
        vlog.exhaust = vehicleLog.getExhaust();
        vlog.coolant = vehicleLog.getCoolant();
        vlog.instrumentalPanel = vehicleLog.getInstrumentalPanel();
        vlog.adblue = vehicleLog.getAdblue();
        vlog.trailercoupling = vehicleLog.getTrailercoupling();
        vlog.createdDate = vehicleLog.getCreatedDate();
        vlog.approvedBy = vehicleLog.getApprovedBy();
        vlog.type = vehicleLog.getType();
        vlog.worker = vehicleLog.getWorker().getFirstname()+" "+vehicleLog.getWorker().getLastname();
        vlog.trailercoupling = vehicleLog.getTrailercoupling();



        vlog.vehicleId = vehicleLog.getVehicle().getId();
        if(nonNull(vehicleLog.getTransport()))
            vlog.transportId = vehicleLog.getTransport().getId();

        return vlog;
    }
}
