package com.cap10mycap10.worklinkservice.dto.payadvice;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DailyShiftRate {

    private long minutesWorked;
    private String dayOfWeek;
    private BigDecimal rate;

    private LocalDateTime billStartDateTime;

    private LocalDateTime billEndDateTime;

    public DailyShiftRate() {
    }

    public DailyShiftRate(long minutesWorked, String dayOfWeek, BigDecimal rate,
                          LocalDateTime billStartDateTime, LocalDateTime billEndDateTime) {
        this.minutesWorked = minutesWorked;
        this.dayOfWeek = dayOfWeek;
        this.rate = rate;
        this.billEndDateTime = billEndDateTime;
        this.billStartDateTime = billStartDateTime;
    }
}
