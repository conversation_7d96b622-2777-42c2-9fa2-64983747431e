package com.cap10mycap10.worklinkservice.mapper.workertrainingsession;

import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionResultsDto;
import com.cap10mycap10.worklinkservice.model.WorkerTrainingSession;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import org.springframework.stereotype.Service;

import java.util.function.Function;

@Service
public class WorkerTrainingSessionToWorkerTrainingSessionResultsDto implements Function<WorkerTrainingSession, WorkerTrainingSessionResultsDto> {
    private final AgencyService agencyService;

    public WorkerTrainingSessionToWorkerTrainingSessionResultsDto(AgencyService agencyService) {
        this.agencyService = agencyService;
    }

    @Override
    public WorkerTrainingSessionResultsDto apply(WorkerTrainingSession workerTrainingSession) {
        WorkerTrainingSessionResultsDto workerTrainingSessionResultsDto = new WorkerTrainingSessionResultsDto();

        workerTrainingSessionResultsDto.setId(workerTrainingSession.getId());
//        if(workerTrainingSession.getTraining() != null) {
//            workerTrainingSessionResultsDto.setTrainingId(workerTrainingSession.getTraining().getId());
//        }
        workerTrainingSessionResultsDto.setTrainingId(workerTrainingSession.getTrainingSession().getTraining().getId());
        workerTrainingSessionResultsDto.setWorkerName(workerTrainingSession.getWorker().getFirstname() + " " + workerTrainingSession.getWorker().getLastname());
        workerTrainingSessionResultsDto.setTrainerName(workerTrainingSession.getTrainingSession().getTrainer().getName());
        workerTrainingSessionResultsDto.setAgencyName(workerTrainingSession.getAgency().getName());
        workerTrainingSessionResultsDto.setShiftLocationName(workerTrainingSession.getTrainingSession().getLocation().getCity());
        workerTrainingSessionResultsDto.setTrainingName(workerTrainingSession.getTrainingSession().getTraining().getName());
        workerTrainingSessionResultsDto.setTrainingSessionName(workerTrainingSession.getTrainingSession().getName());
        workerTrainingSessionResultsDto.setTrainingStatus(workerTrainingSession.getTrainingStatus());
        workerTrainingSessionResultsDto.setEndDateTime(workerTrainingSession.getTrainingSession().getEndDateTime());
        workerTrainingSessionResultsDto.setSkippedTraining(workerTrainingSession.getSkippedTraining());
        workerTrainingSessionResultsDto.setPassedTraining(workerTrainingSession.getPassedTraining());
        workerTrainingSessionResultsDto.setTrainingExpiryDate(workerTrainingSession.getTrainingExpiryDate());
        workerTrainingSessionResultsDto.setTrainingScore(workerTrainingSession.getTrainingScore());
        workerTrainingSessionResultsDto.setStartDateTime(workerTrainingSession.getTrainingSession().getStartDateTime());
        workerTrainingSessionResultsDto.setBreakTime(workerTrainingSession.getTrainingSession().getBreakTimeMins());
        workerTrainingSessionResultsDto.setNotes(workerTrainingSession.getTrainingSession().getNotes());
        workerTrainingSessionResultsDto.setCostPerTrainee(workerTrainingSession.getTrainingSession().getTrainingCost());
        workerTrainingSessionResultsDto.setShowCertificate(workerTrainingSession.getShowCertificate());
        workerTrainingSessionResultsDto.setPostCode(workerTrainingSession.getTrainingSession().getPostCode());
        workerTrainingSessionResultsDto.setAddress(workerTrainingSession.getTrainingSession().getAddress());
        workerTrainingSessionResultsDto.setIsAgencyPaying(workerTrainingSession.getTrainingSession().getIsAgencyPaying());
        workerTrainingSessionResultsDto.setTrainerId(workerTrainingSession.getTrainingSession().getTrainer().getId());

        return workerTrainingSessionResultsDto;
    }
}
