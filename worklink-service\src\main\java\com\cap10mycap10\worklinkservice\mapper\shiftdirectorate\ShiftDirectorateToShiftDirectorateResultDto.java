package com.cap10mycap10.worklinkservice.mapper.shiftdirectorate;


import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateResultDto;
import com.cap10mycap10.worklinkservice.model.ShiftDirectorate;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ShiftDirectorateToShiftDirectorateResultDto implements Converter<ShiftDirectorate, ShiftDirectorateResultDto> {
    @Override
    public ShiftDirectorateResultDto convert(ShiftDirectorate shiftDirectorate) {
        ShiftDirectorateResultDto resultDto = new ShiftDirectorateResultDto();
        resultDto.setId(shiftDirectorate.getId());
        resultDto.setName(shiftDirectorate.getName());
        resultDto.setPhoneNumber(shiftDirectorate.getPhoneNumber());
        resultDto.setCreatedBy(shiftDirectorate.getCreatedBy());
        resultDto.setPostCode(shiftDirectorate.getPostCode());
        resultDto.setLocation(shiftDirectorate.getLocation().getCity());
        resultDto.setClient(shiftDirectorate.getClient().getName());

        return resultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
