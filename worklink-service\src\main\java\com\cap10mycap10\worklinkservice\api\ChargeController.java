package com.cap10mycap10.worklinkservice.api;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.PaymentIntent;
import com.stripe.param.PaymentIntentCreateParams;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;


@Controller
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)

public class ChargeController {

    private static Gson gson = new Gson();

    static class CreatePaymentItem {
        @SerializedName("id")
        String id;

        public String getId() {
            return id;
        }
        @SerializedName("amount")
        Long amount;

        public Long getAmount() {
            return amount;
        }
    }

    static class CreatePayment {
        @SerializedName("items")
        CreatePaymentItem[] items;

        public CreatePaymentItem[] getItems() {
            return items;
        }
    }

    @Data
    @NoArgsConstructor
    static class CreatePaymentResponse {
        private String clientSecret;
        public CreatePaymentResponse(String clientSecret) {
            this.clientSecret = clientSecret;
        }
    }

    static int calculateOrderAmount(CreatePaymentItem[] items) {
        // Calculate the order total on the server to prevent
//        // people from directly manipulating the amount on the client
//        int total = 0;
//        for (CreatePaymentItem item : items) {
//            total += item.getAmount();
//        }
        return 1000;
    }

//    @PostMapping(value="/create-payment-intent")
//    public ResponseEntity<CreatePaymentResponse> charge(@RequestBody String request) {
//        // This is your test secret API key.
//        Stripe.apiKey = "sk_test_51Qjd3G2LpuY6cK8k5PcwTf6gXK8ftBjww6WufIwWaG13xaqw3DaQa4K5tslmLzYu4VU1E27abn3Uw2CcgWvqbvH400jlFEunZq";
//
//
//        CreatePayment postBody = gson.fromJson(request, CreatePayment.class);
//
//        PaymentIntentCreateParams params =
//                PaymentIntentCreateParams.builder()
//                        .setAmount(new Long(calculateOrderAmount(postBody.getItems())))
//                        .setCurrency("usd")
//                        // In the latest version of the API, specifying the `automatic_payment_methods` parameter is optional because Stripe enables its functionality by default.
//                        .setAutomaticPaymentMethods(
//                                PaymentIntentCreateParams.AutomaticPaymentMethods
//                                        .builder()
//                                        .setEnabled(true)
//                                        .build()
//                        )
//                        .build();
//
//        // Create a PaymentIntent with the order amount and currency
//        PaymentIntent paymentIntent = null;
//        try {
//            paymentIntent = PaymentIntent.create(params);
//        } catch (StripeException e) {
//            throw new RuntimeException(e);
//        }
//
////        CreatePaymentResponse paymentResponse = new CreatePaymentResponse(paymentIntent.getClientSecret(), paymentIntent.getId());
//        CreatePaymentResponse paymentResponse = new CreatePaymentResponse(paymentIntent.getClientSecret());
//
//
//
//        return ResponseEntity.ok(paymentResponse);
//    }

}