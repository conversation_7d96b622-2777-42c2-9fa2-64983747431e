package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.chargerate.ChargeRateDto;
import com.cap10mycap10.worklinkservice.dto.chargerate.ChargeRateUpdateDto;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.cap10mycap10.worklinkservice.model.AssignmentRate;
import com.cap10mycap10.worklinkservice.model.ChargeRate;
import com.cap10mycap10.worklinkservice.model.ShiftType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface ChargeRateService {

    void save(ChargeRateDto chargeRateDto);

    ChargeRate findById(Long id);

    List<AssignmentRate> findForAgencyBilling(Long agencyId, ShiftType shiftType, AssignmentCode assignmentCode);

    List<ChargeRate> findAll();

    Page<ChargeRate> findAllPaged(PageRequest of);

    void deleteById(Long id);

    ChargeRate save(ChargeRateUpdateDto chargeRateUpdateDto);

    ChargeRate getOne(Long id);


}
