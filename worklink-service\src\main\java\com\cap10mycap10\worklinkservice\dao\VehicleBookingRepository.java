package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.enums.RatingType;
import com.cap10mycap10.worklinkservice.enums.VehicleBookingStatus;
import com.cap10mycap10.worklinkservice.model.Rating;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.model.VehicleBooking;
import org.apache.commons.lang3.Streams;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

public interface VehicleBookingRepository extends JpaRepository<VehicleBooking, Long> {

//  @Query("SELECT vb FROM VehicleBooking vb WHERE vb.client.id = :clientId AND (:status IS NULL OR vb.status IN :status)")
//  Page<VehicleBooking> findByClientId(@Param("clientId") Long clientId, @Param("status") List<VehicleBookingStatus> status, Pageable pageable);

//    @Query("SELECT vb FROM VehicleBooking vb WHERE vb.client.id = :clientId AND (vb.status IN (:status))")
//    Page<VehicleBooking> findByClientId(@Param("clientId") Long clientId, @Param("status") List<VehicleBookingStatus> status, Pageable pageable);

    @Query("SELECT vb FROM VehicleBooking vb WHERE vb.client.id = :clientId AND (vb.status IN (:status)) AND (:agencyId IS NULL OR vb.vehicle.agency.id = :agencyId)")
    Page<VehicleBooking> findByClientId(@Param("clientId") Long clientId, @Param("status") List<VehicleBookingStatus> status, @Param("agencyId") Long agencyId, Pageable pageable);

//    @Query("SELECT vb FROM VehicleBooking vb WHERE " +
//            "(:carRentalId IS NULL OR vb.vehicle.agency.id = :carRentalId) AND " +
//            "(:bookingStatuses IS NULL OR vb.status IN :bookingStatuses) AND " +
//            "(:vehicleRegNumber IS NULL OR vb.vehicle.regno = :vehicleRegNumber) AND " +
//            "(:bookingId IS NULL OR vb.id = :bookingId) AND " +
//            "(:startDate IS NULL OR vb.start >= :startDate) AND " +
//            "(:endDate IS NULL OR vb.end <= :endDate)")
//    Page<VehicleBooking> findByAgencyId(@Param("carRentalId") Long carRentalId,
//                                             @Param("bookingStatuses") List<VehicleBookingStatus> bookingStatuses,
//                                             @Param("vehicleRegNumber") String vehicleRegNumber,
//                                             @Param("bookingId") Long bookingId,
//                                             @Param("startDate") LocalDateTime startDate,
//                                             @Param("endDate") LocalDateTime endDate, Pageable pageable);


    @Query("SELECT vb FROM VehicleBooking vb WHERE " +
            "(:carRentalId IS NULL OR vb.vehicle.agency.id = :carRentalId) AND " +
            "(:bookingStatuses IS NULL OR vb.status IN :bookingStatuses) AND " +
            "(:bookingId IS NULL OR vb.id = :bookingId) AND " +
            "(:startDate IS NULL OR vb.start >= :startDate) AND " +
            "(:endDate IS NULL OR vb.end <= :endDate) AND " +
            "(:searchQuery IS NULL OR " +
                "LOWER(vb.vehicle.name) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
                "LOWER(vb.vehicle.model) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
                "LOWER(vb.firstname) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
                "LOWER(vb.id) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
                "LOWER(vb.surname) LIKE LOWER(CONCAT('%', :searchQuery, '%')))")
    Page<VehicleBooking> findByAgencyId(@Param("carRentalId") Long carRentalId,
                                        @Param("bookingStatuses") List<VehicleBookingStatus> bookingStatuses,
                                        @Param("searchQuery") String searchQuery,
                                        @Param("bookingId") Long bookingId,
                                        @Param("startDate") ZonedDateTime startDate,
                                        @Param("endDate") ZonedDateTime endDate,
                                        Pageable pageable);







    @Query("SELECT r FROM Rating r WHERE r.vehicleBooking.vehicle.id = :vehicleId and r.type = :type")
    Page<Rating> findRatingsByVehicleId(@Param("vehicleId") Long vehicleId, @Param("type") RatingType type, Pageable pageable);




    @Query("SELECT AVG(ri.rate) FROM Rating r JOIN r.ratingItems ri WHERE r.vehicleBooking.vehicle.id = :vehicleId and r.type = :type")
    Float findAverageRatingByVehicleId(@Param("vehicleId") Long vehicleId,  @Param("type") RatingType type);

    @Query("SELECT AVG(ri.rate) FROM Rating r JOIN r.ratingItems ri WHERE r.vehicleBooking.client.id = :clientId")
    Float findAverageRatingByClientId(@Param("clientId") Long clientId);

//    @Query("SELECT vb FROM VehicleBooking vb WHERE " +
//            "(:carRentalId IS NULL OR vb.vehicle.agency.id = :carRentalId) AND " +
//            "(:bookingStatuses IS NULL OR vb.status IN :bookingStatuses) AND " +
//            "(:vehicleRegNumber IS NULL OR vb.vehicle.regno = :vehicleRegNumber) AND " +
//            "(:bookingId IS NULL OR vb.id = :bookingId) AND " +
//            "(:startDate IS NULL OR vb.start >= :startDate) AND " +
//            "(:endDate IS NULL OR vb.end <= :endDate)")
//    Page<VehicleBooking> findAll(@Param("carRentalId") Long carRentalId,
//                                             @Param("bookingStatuses") List<VehicleBookingStatus> bookingStatuses,
//                                             @Param("vehicleRegNumber") String vehicleRegNumber,
//                                             @Param("bookingId") Long bookingId,
//                                             @Param("startDate") LocalDateTime startDate,
//                                             @Param("endDate") LocalDateTime endDate, Pageable pageable);


    List<VehicleBooking> findByStatusAndCreatedDateBefore(VehicleBookingStatus vehicleBookingStatus, Instant minus);

    long countByVehicleAndStatus(Vehicle vehicle, VehicleBookingStatus status);

    List<VehicleBooking> findByVehicleAndStatusIn(Vehicle vehicle, List<VehicleBookingStatus> statuses);




    List<VehicleBooking> findByStartBeforeAndStartAfterAndReminderSentAndStatus(ZonedDateTime twoDaysToGo, ZonedDateTime now, boolean b, VehicleBookingStatus vehicleBookingStatus);
}
