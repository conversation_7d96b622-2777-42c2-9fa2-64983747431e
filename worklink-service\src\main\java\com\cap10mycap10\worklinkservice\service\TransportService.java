package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.shift.ShiftReportStatus;
import com.cap10mycap10.worklinkservice.dto.transport.*;
import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import com.cap10mycap10.worklinkservice.model.Transport;
import org.codehaus.jettison.json.JSONException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface TransportService {

    TransportDto create(TransportDto transportDto) throws JSONException;

    Page<TransportDto> findPaged(PageRequest of, TransportStatus status);

    TransportDto findById(Long id);

    void authorize(AuthorizeTransportDto authorizeTransportDto);
    VehicleLogDto authorizeVehicleLog(Long logId, String authorizer);

    VehicleLogDto commentVehicleLog(Long logId, String comment);

    void beginPendingJobs();

    TransportDto update(TransportDto transportUpdateDto) throws JSONException;

    ShiftReportStatus findAllAgencyShiftsByStatus(Long agencyId);

    @Transactional
    ShiftReportStatus findAllAgencyLogsShiftsByStatus(Long agencyId);

    @Transactional
    ShiftReportStatus findAllClientShiftsByStatus(Long agencyId);

    Page<TransportDto> findAgencyJobs(Long agencyId, PageRequest of, TransportStatus status, boolean isFullyBooked, String searchCriteria);

    Page<TransportDto> findByClientId(Long clientId, PageRequest of, TransportStatus status);

    List<LegibleWorkersDto> getEligibleWorkers(Long transportId);

    TransportDto commitJob(Long transportId);

    Page<TransportDto> findByLegibleForAgency(Long agencyId, PageRequest of, TransportStatus status);

    TransportDto acceptJob(Long transportId, Long agencyId);

    List<BookingResultDto> workerEligibleTransport(Long workerId, TransportStatus transportStatus);

    List<BookingResultDto> workerEligibleTransport(Long workerId);

    TransportDto transportRating(Long transportId, Byte rating);

    Transport getOne(Long transportId);

    Transport save(Transport transport);

    void uploadRiskDocument(Long transportId, MultipartFile file);

    void expireUnbookedJobs();

    void uploadDocument(MultipartFile file, Transport transport, String documentType);
    void uploadDamageReport(MultipartFile file, Long logId);


    TransportDto pickVehicle(Long id, Long transportId);

    TransportDto pickTeamLeader(Long id, Long transportId);
    TransportDto pickDriver(Long id, Long transportId);

    Page<TransportDto> findByDriver(Long driverId, TransportStatus status, PageRequest of);

    Page<VehicleLogDto> findByWorkerAndStatus(Long agencyId, LogStatus status, PageRequest of);

    Page<VehicleLogDto> findLogsByAgencyAndStatus(Long agencyId, LogStatus status, PageRequest of);
    Page<TransportDto> findByTeamLeader(Long leaderId, TransportStatus status, PageRequest of);

    VehicleLogDto updateMileage(VehicleLogDto vehicleLogDto);

    VehicleLogDto updateVehicleCheck(VehicleLogDto vehicleLogDto);

    TransportDto teamLeaderUpdate(TransportTeamLeaderUpdateDto transport);
    TransportDto teamLeaderWorkerTimesUpdate(TransportWorkerTimesDto transport);

    VehicleLogDto updateCleaningCheck(VehicleLogDto vehicleLogDto);

    void cancelJob(Long id);
}
