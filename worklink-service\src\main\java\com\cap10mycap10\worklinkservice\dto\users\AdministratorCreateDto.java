package com.cap10mycap10.worklinkservice.dto.users;

import com.cap10mycap10.worklinkservice.model.Agency;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdministratorCreateDto {

    private String firstname;

    private String lastname;

    private String email;

    private String mobile;

    private String landline;

    private Long agentId;

    public static AdministratorCreateDto buildRequest(Agency agent, AdministratorCreateDto request) {
        return AdministratorCreateDto.builder()
                .email(request.getEmail())
                .firstname(request.getFirstname())
                .lastname(request.getLastname())
                .mobile(request.getMobile())
                .landline(request.getLandline())
                .agentId(request.getAgentId())
                .build();
    }
}
