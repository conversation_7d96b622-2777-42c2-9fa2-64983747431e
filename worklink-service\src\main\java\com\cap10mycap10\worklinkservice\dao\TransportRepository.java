package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.model.Transport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;

public interface TransportRepository extends JpaRepository<Transport, Long> {
    @Query(value = "SELECT * FROM transport \n" +
            "WHERE agency_id = ?1 and transport_status = ?2 and id like %?3%", nativeQuery = true)
    Page<Transport> findByAgencyAndStatusAndSearch(Long agencyId,String status, String searchCriteria, Pageable of);
    List<Transport> findByAgencyId(Long agencyId);

    @Query(value = "SELECT * FROM transport JOIN\n" +
            "transport_agency WHERE transport_agency.agency_id = ?1", nativeQuery = true)
    List<Transport> findByAgenciesContain(Long agencyId);

    List<Transport> findByClientId(Long clientId);

    void deleteById(Long id);

    void delete(Transport transport);

    List<Transport> findByTransportStatusNot(TransportStatus closed);

    List<Transport> findAllByTransportStatusAndDateTimeRequiredLessThan(TransportStatus transportStatus, LocalDateTime now);

    @Query(value = "SELECT * FROM transport WHERE id in ( SELECT transport_id from transport_agency WHERE agency_id = ?1 ) \n" +
            "and \n" +
            " transport_status = ?2 and transport.id like %?3%", nativeQuery = true)
    Page<Transport> findAllByAgenciesAndTransportStatusAndSearch(Long agencyId, String transportStatus, String searchCriteria, Pageable of);

    List<Transport> findAllByAgenciesAndTransportStatus(Agency agency, TransportStatus transportStatus);
    @Query(value = "SELECT * FROM transport WHERE transport_status not in ('CLOSED', 'CANCELLED') AND \n" +
            "id IN (SELECT wsp.transport_id from transport_worker_spec as wsp WHERE wsp.assignment_code_id = 37 \n" +
            "AND wsp.id in (SELECT worker_spec_id FROM shift WHERE worker_id = ?1 )\n" +
            " ) AND id not in (SELECT transport_id FROM vehicle_log where transport_id = transport.id and vehicle_log.status in ('APPROVED', 'WAITING_APPROVAL'))", nativeQuery = true)
    Page<Transport> findDriverNew(Long driverId, Pageable of);

    @Query(value = "SELECT * FROM transport WHERE transport_status not in ('CLOSED', 'CANCELLED') AND \n" +
            "id IN (SELECT wsp.transport_id from transport_worker_spec as wsp WHERE wsp.assignment_code_id = 37 \n" +
            "AND wsp.id in (SELECT worker_spec_id FROM shift WHERE worker_id = ?1 )\n" +
            " )", nativeQuery = true)
    Page<Transport> findDriverAwaiting(Long driverId, Pageable of);

    @Query(value = "SELECT * FROM transport WHERE transport_status = 'CLOSED' AND \n" +
            "id IN (SELECT wsp.transport_id from transport_worker_spec as wsp WHERE wsp.assignment_code_id = 37 \n" +
            "AND wsp.id in (SELECT worker_spec_id FROM shift WHERE worker_id = ?1 )\n" +
            " )", nativeQuery = true)
    Page<Transport> findDriverClosed(Long driverId, Pageable of);


    Page<Transport> findByTeamLeaderIdAndTransportStatus(Long leaderId, TransportStatus status, Pageable of);

    long countByVehicle(Vehicle vehicle);


}
