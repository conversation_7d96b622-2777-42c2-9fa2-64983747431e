package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.chatgroup.CreateGroupRequest;
import com.cap10mycap10.worklinkservice.dto.chatgroupmessage.ChatGroupMessageResponseDto;
import com.cap10mycap10.worklinkservice.model.ChatGroup;
import com.cap10mycap10.worklinkservice.model.ChatGroupMessage;
import com.cap10mycap10.worklinkservice.model.Shift;

import java.util.List;

public interface ChatGroupService {

    ChatGroup createChatGroup(CreateGroupRequest request);

    ChatGroup getChatGroupById(Long chatGroupId);

//    ChatGroup createChatGroup(String groupName);

    ChatGroup createChatGroup(String groupName, List<Shift> shift);

    ChatGroup joinChatGroup(Long chatGroupId, Long workerId);

    ChatGroup findChatGroupByShiftID(Long shiftId);

    ChatGroup findChatGroupByShiftIdAndWorkerId(Long shiftId, Long workerId);

    List<ChatGroupMessageResponseDto> findChatGroupMessagesByShiftIdAndWorkerId(Long shiftId, Long workerId);

    List<ChatGroupMessageResponseDto> findChatGroupMessagesByChatGroupId(Long groupId);
}



