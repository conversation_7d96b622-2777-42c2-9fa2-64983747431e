package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.workertraining.IWorkerTrainingResultDto;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.model.WorkerTraining;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

public interface WorkerTrainingRepository extends JpaRepository<WorkerTraining, Long> {


    Page<WorkerTraining> findAllByWorkerAndDeletedAndTrainingExpiryGreaterThan(Worker workerId, boolean deleted, LocalDate date, Pageable of);

}
