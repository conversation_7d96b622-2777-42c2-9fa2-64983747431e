package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.PayAdviceItemRepository;
import com.cap10mycap10.worklinkservice.dao.PayAdviceRepository;
import com.cap10mycap10.worklinkservice.dao.ShiftExpenseClaimRepository;
import com.cap10mycap10.worklinkservice.dto.payadvice.DailyShiftRate;
import com.cap10mycap10.worklinkservice.dto.ShiftRateItem;
import com.cap10mycap10.worklinkservice.dto.payadvice.PayAdviceResult;
import com.cap10mycap10.worklinkservice.dto.payadvice.ShiftDayTime;
import com.cap10mycap10.worklinkservice.enums.PayAdviceStatus;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.InvalidRequestException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.mapper.payadvice.PayAdviceToPayAdviceResult;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.reports.ReportFormat;
import com.cap10mycap10.worklinkservice.service.*;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.nonNull;


@Slf4j
@Service
public class PayAdviceServiceImpl implements PayAdviceService {
    @Autowired
    private ShiftExpenseClaimRepository shiftExpenseRateRepository;
    private final ShiftService shiftService;
    private final PayAdviceRepository payAdviceRepository;
    private final PayAdviceItemRepository payAdviceItemRepository;
    private final AssignmentCodeRateService assignmentCodeRateService;
    private final EmailService emailService;
    private final WorkerService workerService;
    private final AgencyService agencyService;
    private final ReportService reportService;

    private final AgencyWorkerPropertiesService agencyWorkerPropertiesService;
    private final PayAdviceToPayAdviceResult toPayAdviceResult;
    private final AgencySettingsService agencySettingsService;

    public PayAdviceServiceImpl(ShiftService shiftService, PayAdviceRepository payAdviceRepository,
                                PayAdviceItemRepository payAdviceItemRepository,
                                AssignmentCodeRateService assignmentCodeRateService,
                                EmailService emailService, WorkerService workerService,
                                AgencyService agencyService, ReportService reportService,
                                AgencyWorkerPropertiesService agencyWorkerPropertiesService, PayAdviceToPayAdviceResult toPayAdviceResult,
                                AgencySettingsService agencySettingsService) {
        this.shiftService = shiftService;
        this.payAdviceRepository = payAdviceRepository;
        this.payAdviceItemRepository = payAdviceItemRepository;
        this.assignmentCodeRateService = assignmentCodeRateService;
        this.emailService = emailService;
        this.workerService = workerService;
        this.agencyService = agencyService;
        this.reportService = reportService;
        this.agencyWorkerPropertiesService = agencyWorkerPropertiesService;
        this.toPayAdviceResult = toPayAdviceResult;
        this.agencySettingsService = agencySettingsService;
    }

    @Override
    public void createPayAdvice(HttpServletRequest servletRequest, Long id, Long agentId,String payDate, List<Long> shiftId) throws Exception {

        Agency agency = agencyService.getOne(agentId);

        String paymentMethod = agencyWorkerPropertiesService.findProperties(id, agentId).getPaymentMethod();
        log.info("Payment method {}", paymentMethod);

        var agencySettings = agencySettingsService.findAgencySettings(agentId);

        PayAdvice payAdvice = new PayAdvice();
        payAdvice.setWorkerId(id);
        payAdvice.setAgentId(agentId);
        payAdvice.setPayAdviceStatus(PayAdviceStatus.UNPAID);

        var payAdviceItems = createPayAdviceItem(shiftId, paymentMethod);

        if (payAdviceItems.isEmpty()) {
            throw new InvalidRequestException("All selected shifts have already been billed.");
        }

        payAdvice.addPayAdviceItems(payAdviceItems);



        payAdvice.setPayAdviceDate(LocalDate.parse(payDate));

        BigDecimal subtotalAmount = payAdvice.getPayAdviceItems().stream()
                .map(PayAdviceItem::getTotal)
                .reduce(BigDecimal::add).get();

        payAdvice.setSubTotalAmount(payAdvice.getPayAdviceItems().stream()
                .map(PayAdviceItem::getTotal)
                .reduce(BigDecimal::add).get());

//        if (nonNull(agencySettings) && agencySettings.isChargeVat()) {
//            BigDecimal vatPercent = agencySettings.getVatPercentage();
//            log.info("vat percentage applied {}", subtotalAmount);
//            val vatAmount = subtotalAmount
//                    .multiply( vatPercent
//                    )
//                    .divide(BigDecimal.valueOf(100L));
//
//            payAdvice.setTotalAmount(subtotalAmount.add(vatAmount));
//            payAdvice.setVatPercentage(agencySettings.getVatPercentage());
//            payAdvice.setVatAmount(vatAmount);
//
//        } else {
            payAdvice.setTotalAmount(subtotalAmount);
            payAdvice.setVatAmount(BigDecimal.ZERO);
            payAdvice.setVatPercentage(BigDecimal.ZERO);
//        }

        PayAdvice savedPayAdvice = payAdviceRepository.saveAndFlush(payAdvice);


        List<ShiftExpenseClaim> claims = new ArrayList<>();
        List<Shift> shifts = shiftService.findAllByIds(shiftId);
        shifts.forEach(s->{
            Set<ShiftExpenseClaim> shiftClaims = s.getShiftExpenseClaims();
            shiftClaims.forEach(c->{
                if(c.getStatus()== Status.APPROVED)c.setPayAdvice(savedPayAdvice);
            });

            claims.addAll(shiftClaims);
        });

        shiftExpenseRateRepository.saveAll(claims);


        CompletableFuture.runAsync(() -> {
            for (Long idShift : shiftId
            ) {
                Shift shift = shiftService.getOne(idShift);
//                if (shift.getStatus().city().equalsIgnoreCase("AUTHORIZED")) {
                    shift.setShiftWorkerStatus(ShiftStatus.PAID);
                    shiftService.save(shift);
//                }
            }
        });
        Worker worker = workerService.getOne(id);

//        try {
//            emailService.sendPayAdvice(worker.getEmail(), "PayAdvice", "Find attached the payAdvice",
//                    reportService.generatePayAdvice(servletRequest, "PDF", payAdvice.getId()).getBody().getFile().getAbsolutePath(), payAdvice.getId());
//        } catch (IOException | SQLException e) {
//            e.printStackTrace();
//        }
    }

    @Override
    @Transactional
    public PayAdviceResult findPayAdvice(Long payAdviceId) {
        PayAdvice payAdvice = findPayAdviceById(payAdviceId);
        PayAdviceResult payadvice = toPayAdviceResult.convert(payAdvice);
//        payadvice.setIpayAdviceItemResult(payAdviceItemRepository.findAllByPayAdvice_Id(payAdviceId));

        return payadvice;
    }


    @Override
    public Page<PayAdviceResult> getAllPayAdvices(Integer page, Integer size) {
        return payAdviceRepository.findAll(PageRequest.of(page, size))
                .map(toPayAdviceResult::convert);
    }

    @Override
    public Page<PayAdviceResult> getAllPayAdvices(Long workerId, Integer page, Integer size) {
        return payAdviceRepository.findAllByWorkerIdOrderByPayAdviceDateDesc(workerId, PageRequest.of(page, size))
                .map(toPayAdviceResult::convert);
    }

    @Override
    @Transactional
    public Page<PayAdviceResult> findAllByWorkerIdAndAgencyId(Long workerId,Long agencyId, Integer page, Integer size) {
        return payAdviceRepository.findAllByWorkerIdAndAgentId(workerId,agencyId, PageRequest.of(page, size))
                .map(toPayAdviceResult::convert);
    }

    @Override
    public void acknowledgePayment(Long payAdviceId, String paymentRef) {
        PayAdvice payAdvice = findPayAdviceById(payAdviceId);
        if (payAdvice.getPayAdviceStatus().name().equalsIgnoreCase("PROCESSED")) {
            payAdvice.setPayAdviceStatus(PayAdviceStatus.PAID);
            payAdvice.setPaymentRef(paymentRef);
            payAdviceRepository.save(payAdvice);
        }
    }

    @Override
    public void sendPOP(Long payAdviceId, String paymentRef) {
        PayAdvice payAdvice = findPayAdviceById(payAdviceId);
        if (payAdvice.getPayAdviceStatus().name().equalsIgnoreCase("Unpaid")) {
            payAdvice.setPayAdviceStatus(PayAdviceStatus.PROCESSED);
            payAdvice.setPaymentRef(paymentRef);
            payAdviceRepository.save(payAdvice);
        }
    }

    @Override
    public PayAdvice findPayAdviceById(Long id) {
        return payAdviceRepository.findById(id)
                .orElseThrow(() ->
                        new RecordNotFoundException("PayAdvice " + id + " does not exist"));
    }



    @Override
    @Transactional
    public Page<PayAdviceResult> findWorkerPayAdvices(Long workerId, PageRequest of) {
        Page<PayAdvice> res = payAdviceRepository.findAllByWorkerIdOrderByPayAdviceDateDesc(workerId, of);
            return res.map(toPayAdviceResult::convert);
    }


    @Override
    @Transactional
    public Page<PayAdviceResult> getAllPayAdvicesForAgent(Long agencyId, Integer page, Integer size) {
        Page<PayAdvice> advices = payAdviceRepository.findAllByAgentIdAndPayAdviceStatus(agencyId, PayAdviceStatus.UNPAID, PageRequest.of(page, size));

        advices.forEach(e->{
            e.setShiftExpenseClaims(e.getShiftExpenseClaims());
        });
        return advices.map(toPayAdviceResult::convert);
    }

    @Override
    @Transactional
    public Page<PayAdviceResult> getAllFilteredPayAdvicesForAgent(Long agencyId, Long workerId, LocalDate startDate, LocalDate endDate, Integer page, Integer size) {
        List<PayAdvice> payAdviceList = payAdviceRepository.findAllByAgentIdAndPayAdviceStatus(agencyId, PayAdviceStatus.UNPAID);

        if(startDate!=null)  payAdviceList = payAdviceList.stream()
                .filter(p -> checkAfter(p.getPayAdviceDate().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null)  payAdviceList = payAdviceList.stream()
                .filter(p -> checkBefore(p.getPayAdviceDate().toString(), endDate))
                .collect(Collectors.toList());

        if(workerId!=null) payAdviceList = payAdviceList.stream()
                    .filter(p -> Objects.equals(p.getWorkerId(), workerId))
                    .collect(Collectors.toList());


        List<PayAdviceResult> val2 = payAdviceList.stream().map(toPayAdviceResult::convert).collect(Collectors.toList());

        return PaginationUtil.paginatePayAdvice(PageRequest.of(page, size), val2);
    }

    @Override
    @SneakyThrows
    public ResponseEntity<Resource> downloadPayAdvice(Long payAdviceId, ReportFormat format,
                                                    HttpServletRequest servletRequest) {
        return reportService.generatePayAdvice(servletRequest, format.name(), payAdviceId);
    }
    @Override
    @SneakyThrows
    @Transactional
    public ResponseEntity<Resource> downloadBacsPaymentCsv(LocalDate date, Long agencyId, HttpServletRequest servletRequest) {
        Resource resource;
        try {
            List<String[]> dataLines = getBacsInfo( date,  agencyId);
            File csvOutputFile = new File("CSV_FILE_NAME");

            try (PrintWriter pw = new PrintWriter(csvOutputFile)) {
                dataLines.stream()
                        .map(this::convertToCSV)
                        .forEach(pw::println);
            }

            val bytes = FileUtils.readFileToByteArray(csvOutputFile);

            val tempFile = Files.createTempFile("report", ".csv");
            try (OutputStream outputStream = new FileOutputStream(tempFile.toFile())) {
                outputStream.write(bytes);
            }
            resource = new UrlResource(tempFile.toUri());

        } catch (IOException e) {
            log.error("#### Failed to generate report due to : {}", e.getMessage());
            throw new InvalidRequestException("Failed to report due to : " + e.getMessage());
        }



        String contentType = getContentType(servletRequest, resource);
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }


    @Override
    public void uploadBacsPaymentCsv(MultipartFile file) {
        try (Reader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {

            // create csv bean reader
            CsvToBean csvToBean = new CsvToBeanBuilder(reader)
                    .withType(PayAdvice.class)
                    .withIgnoreLeadingWhiteSpace(true)
                    .build();

            // convert `CsvToBean` object to list of users
            List<PayAdvice> pays = csvToBean.parse();
            pays.forEach(e->{
               PayAdvice advice = getOne(e.getId());
               if(PayAdviceStatus.PAID.toString().equalsIgnoreCase(e.getStatus())){
                   advice.setPayAdviceStatus(PayAdviceStatus.PAID);
               }
               payAdviceRepository.save(advice);
            });

            log.info("Pay advices found: {}", pays);
            reader.close();



        } catch (Exception ex) {
            throw new BusinessValidationException("File type not supported. Use csv");
        }

    }


    public String convertToCSV(String[] data) {
        return Stream.of(data)
                .map(this::escapeSpecialCharacters)
                .collect(Collectors.joining(","));
    }
    public List<String[]> getBacsInfo(LocalDate date, Long agencyId) {

        List<String[]> dataLines = new ArrayList<>();
        dataLines.add(new String[]
                {
                        "Ref",
                        "Name",
                        "Sort Code",
                        "Account Number",
                        "Amount",
                        "Status"
                });

        List<PayAdvice> advices = payAdviceRepository.findAllByAgentIdAndPayAdviceDate(agencyId, date);

        if(advices.size()==0){
            throw new BusinessValidationException("No matching files found select a a valid pay date");
        }

        advices.forEach(e->{
            Worker worker = workerService.getOne(e.getWorkerId());
            List<Bank> banks = worker.getBank();
            Bank bank = new Bank();


            for(int  i=0; i < banks.size(); i++){
                bank = banks.get(i);
            }
            if(bank.getAccount() == null){
                bank.setAccount("N/A");
            }
            if(bank.getCode() == null){
                bank.setCode("N/A");
            }

            dataLines.add(new String[]
                    {
                            e.getId().toString(),
                            worker.getFirstname()+" "+worker.getLastname(),
                            "("+bank.getCode()+")",
                            bank.getAccount(),
                            e.getTotalAmount().toString(),
                            e.getPayAdviceStatus().toString()
                    });


        });

        return  dataLines;
    }

    public String escapeSpecialCharacters(String data) {
        String escapedData = data.replaceAll("\\R", " ");
        if (data.contains(",") || data.contains("\"") || data.contains("'")) {
            data = data.replace("\"", "\"\"");
            escapedData = "\"" + data + "\"";
        }
        return escapedData;
    }



    private static String getContentType(HttpServletRequest httpServletRequest, Resource resource) {

        String contentType = null;
        try {
            contentType = httpServletRequest.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            ex.printStackTrace();
            log.error("### Could not determine file type. due to {}", ex.getMessage());
        }
        if (contentType == null) {
            contentType = "application/octet-stream";
        }
        return contentType;
    }


    @Override
    public PayAdvice getOne(Long id) {
        return payAdviceRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Pay advice not found"));
    }



    private List<PayAdviceItem> createPayAdviceItem(List<Long> shiftIds, String paymentMethod) throws Exception {

        List<PayAdviceItem> payAdviceItems = new ArrayList<>();
        try {

            for (Long shift : shiftIds) {
                boolean breakFactoredIn = false;
                Shift result = shiftService.getOne(shift);
                log.info(" Shift total hours {}", result.getStart()
                        .until(result.getEnd(), ChronoUnit.HOURS));

//                 IF shift is already paid do not process
                if(result.getShiftWorkerStatus() == ShiftStatus.PAID){
                    continue;
                }

                if (result.getReleased()) {

                    List<ShiftDayTime> shiftDayTimeList = splitShiftDurationToDays(result);

                    Set<ShiftRateItem> shiftRateItems = new HashSet<>();
                    for (int j = 0; j < shiftDayTimeList.size(); j++) {

                        ShiftDayTime daytime = shiftDayTimeList.get(j);

                        var shiftRates = getDayRate( daytime.getDayOfWeek(), result, paymentMethod);

                        log.info("############# Shift rates {}", shiftRates);

                        log.info("### J {}", j);
                        for (ShiftRateItem shiftRateItem : shiftRates) {

                            LocalTime rateStartTime = shiftRateItem.getStartTime();
                            LocalTime rateEndTime = shiftRateItem.getEndTime();
                            var rateItemRateEndTime = daytime.getStart().toLocalDate().atTime(rateEndTime);
                            var rateItemRateStartTime = daytime.getStart().toLocalDate().atTime(rateStartTime);

                            log.info("#################### rateItemRateStartTime {} and rateItemRateEndTime {}", rateItemRateStartTime, rateItemRateEndTime);
                            if (rateStartTime.isAfter(rateEndTime) || rateStartTime.equals(rateEndTime)) {

                                var rateItemRateEndTimePlusOneDay = rateItemRateEndTime.plusDays(1).toLocalDate();

                                log.info("###### rateItemRateEndTime {}", rateItemRateEndTime);

                                shiftRateItem.setRateStartTime(rateItemRateStartTime);
                                shiftRateItem.setRateEndTime(rateItemRateEndTime.plusDays(1));
                                shiftRateItems.add(shiftRateItem);
                            } else {

                                if ((daytime.getShiftEndTime().isBefore(rateItemRateStartTime) ||
                                        daytime.getShiftStartTime().isBefore(rateItemRateStartTime)) &&
                                        j == 0) {

                                    //Get yesterday rate

                                    val yesterday = daytime.getStart().minusDays(1);

                                    val yesterdayDayOfWeek = yesterday.getDayOfWeek().name();

                                    val yesterdayRates =
                                            getDayRate( yesterdayDayOfWeek, result, paymentMethod);

                                    log.info("#### Yesterday rates {}", yesterdayRates);

                                    for (ShiftRateItem yesterdayRate : yesterdayRates) {

                                        LocalTime yesterdayRateStartTime =  yesterdayRate.getStartTime();
                                        LocalTime yesterdayRateEndTime =  yesterdayRate.getEndTime();

                                        var yesterdayRateItemRateEndTime = yesterday.toLocalDate().atTime(yesterdayRateEndTime);
                                        var yesterdayRateItemRateStartTime = yesterday.toLocalDate().atTime(yesterdayRateStartTime);

                                        log.info("############# yesterdayRateItemRateStartTime {} and yesterdayRateItemRateEndTime {} ",
                                                yesterdayRateItemRateStartTime, yesterdayRateItemRateEndTime);

                                        if (yesterdayRateStartTime.isAfter(yesterdayRateEndTime) ||
                                                yesterdayRateStartTime.equals(yesterdayRateEndTime)) { //Take only overlapping rate
                                            log.info("#### Taking only overlapping rates ");
                                            var yesterdayShiftRateItem = new ShiftRateItem(yesterdayDayOfWeek,
                                                    yesterdayRate.getStartTime(), yesterdayRate.getEndTime(), yesterdayRate.getRate());
                                            yesterdayShiftRateItem.setRateStartTime(yesterdayRateItemRateStartTime);
                                            yesterdayShiftRateItem.setRateEndTime(yesterdayRateItemRateEndTime.plusDays(1));
                                            shiftRateItems.add(yesterdayShiftRateItem);
                                        } else {
                                            log.info("##### Discarded  yesterday rate {}", yesterdayRate);
                                        }

                                    }
                                }

                                shiftRateItem.setRateStartTime(rateItemRateStartTime);
                                shiftRateItem.setRateEndTime(rateItemRateEndTime);
                                shiftRateItems.add(shiftRateItem);

                            }
                        }
                    }

                    shiftRateItems = shiftRateItems.stream()
                            .sorted(Comparator.comparing(ShiftRateItem::getRateStartTime))
                            .collect(Collectors.toCollection(LinkedHashSet::new));

                    log.info("-----> Shifts rates items {}", shiftRateItems);

                    for (ShiftRateItem rateItem : shiftRateItems) {

                        for (ShiftDayTime daytime : shiftDayTimeList) {

                            DailyShiftRate dailyShiftRate = getItemRatePerSlot(daytime.getShiftStartTime(),
                                    daytime.getShiftEndTime(),
                                    rateItem.getRateStartTime(),
                                    rateItem.getRateEndTime(),
                                    rateItem.getRate(),
                                    daytime.getDayOfWeek());
                            if (nonNull(dailyShiftRate)) {
                                PayAdviceItem payAdviceItem = new PayAdviceItem();
                                payAdviceItem.setDayOfTheWeek(daytime.getDayOfWeek());
                                payAdviceItem.setRate(dailyShiftRate.getRate());
                                payAdviceItem.setShiftId(result.getId());
                                payAdviceItem.setDirectorate(result.getDirectorate().getName());
                                payAdviceItem.setStartDate(dailyShiftRate.getBillStartDateTime().toLocalDate().toString());
                                payAdviceItem.setEndDate(dailyShiftRate.getBillEndDateTime().toLocalDate().toString());
                                payAdviceItem.setStartTime(dailyShiftRate.getBillStartDateTime().toLocalTime().toString());
                                payAdviceItem.setEndTime(dailyShiftRate.getBillEndDateTime().toLocalTime().toString());

                                LocalDateTime localDateTime = daytime.getStart().toLocalDate().atStartOfDay();
                                LocalDateTime endOfDate = localDateTime
                                        .toLocalDate().atTime(LocalTime.MAX);

                                long minutesWorked = dailyShiftRate.getMinutesWorked();

                                if (dailyShiftRate.getBillEndDateTime().compareTo(endOfDate) == 0) {
                                    minutesWorked = minutesWorked + 1;
                                    log.info("### Minutes worked {}", minutesWorked);
                                }

                                double hoursWorked;

                                if (!breakFactoredIn) {

                                    long hours = Long.parseLong(daytime.getBreakTime().split(" ")[0].replace("hr", "")) * 60;
                                    long minutes = Long.parseLong(daytime.getBreakTime().split(" ")[1].replace("mins", ""));

                                    long minutesWorkedAfterRemoveBreak = (minutesWorked - (hours + minutes));

                                    hoursWorked = (double) minutesWorkedAfterRemoveBreak / 60;

                                    breakFactoredIn = true;
                                } else {
                                    hoursWorked = (double) minutesWorked / 60;
                                }

                                payAdviceItem.setNumberOfHoursWorked(hoursWorked);

                                payAdviceItem.setTotal(dailyShiftRate.getRate().multiply(BigDecimal.valueOf(payAdviceItem.getNumberOfHoursWorked())));
                                payAdviceItems.add(payAdviceItem);
                            }
                        }
                    }
                }
            }
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new BusinessValidationException(exception.getMessage());
        }
        return payAdviceItems;
    }

    private List<ShiftRateItem> getDayRate( String dayOfWeek, Shift shift, String paymentMethod) {

        List<ShiftRateItem> rates = new ArrayList<>();
        List<AssignmentRate> assignmentRate = assignmentCodeRateService.findAssignmentRate(
                shift.getClient().getId(), shift.getDirectorate().getId(), shift.getShiftType().getId(), dayOfWeek, shift.getAssignmentCode().getId()
        );

        if(paymentMethod!=null && paymentMethod.equalsIgnoreCase("paye")){
            for (AssignmentRate rate : assignmentRate
            ) {
                rates.add(new ShiftRateItem(dayOfWeek, rate.getStartTime(), rate.getEndTime(), rate.getPayeRate()));
            }
        }else if(paymentMethod!=null && paymentMethod.equalsIgnoreCase("umbrella")){
            for (AssignmentRate rate : assignmentRate
            ) {
                rates.add(new ShiftRateItem(dayOfWeek, rate.getStartTime(), rate.getEndTime(), rate.getUmbrellaRate()));
            }
        }else if(paymentMethod!=null && paymentMethod.equalsIgnoreCase("pvt")){
            for (AssignmentRate rate : assignmentRate
            ) {
                rates.add(new ShiftRateItem(dayOfWeek, rate.getStartTime(), rate.getEndTime(), rate.getPrivateRate()));
            }
        }else{
            throw new BusinessValidationException("Worker payment method is not set. Go to worker profile and set payment method");
        }

        if (rates.size() == 0) {
            throw new BusinessValidationException("Rates not set");
        }
        return rates;
    }


    private Integer getNumberOfDays(LocalDateTime startDate, LocalDateTime endDate) {
        return (int) ChronoUnit.DAYS.between(startDate, endDate);
    }

    private String getDayOfTheWeekFromDate(LocalDateTime data) {
        return data.getDayOfWeek().name();
    }

    private long getNumberOfHoursWorked(LocalDate startDate, LocalDate endDate, String startTime, String endTime) {

        LocalDateTime time = startDate.atStartOfDay();
        log.info("Start time: {}", startTime);
        log.info("End time: {}", endTime);
        LocalDateTime startDateTime = startDate.atTime(LocalTime.parse(startTime));
        LocalDateTime endDateTime = endDate.atTime(LocalTime.parse(endTime));
        return startDateTime.until(endDateTime, ChronoUnit.HOURS);
    }


    void generatePayAdviceItems(AssignmentRate rate) {

    }


    private List<ShiftDayTime> splitShiftDurationToDays(Shift shift) {

        List<ShiftDayTime> shiftDayTimeList = new ArrayList<>();
        int days = getNumberOfDays(shift.getStart(), shift.getEnd()) + 1;

        if (days == 1) {
            ShiftDayTime shiftDayTime = new ShiftDayTime(
                    getDayOfTheWeekFromDate(shift.getStart()),
                    shift.getStart(),
                    shift.getStart(),
                    shift.getStart(),
                    shift.getBreakTime()
            );
            shiftDayTimeList.add(shiftDayTime);
        } else {
            int i;
            ShiftDayTime shiftDayTime;
            for (i = 1; i < days; i++) {
                LocalDateTime localDateTime = shift.getStart().toLocalDate().atStartOfDay();
                LocalDateTime endOfDate = localDateTime.toLocalDate().atTime(LocalTime.MAX);
                if (i == 1) {
                    shiftDayTime = new ShiftDayTime(
                            getDayOfTheWeekFromDate(shift.getStart()),
                            shift.getStart(),
                            shift.getStart(),
                            endOfDate,
                            shift.getBreakTime()
                    );

                } else {
                    shiftDayTime = new ShiftDayTime(
                            getDayOfTheWeekFromDate(shift.getStart().plusDays(i - 1)),
                            shift.getStart().plusDays(i - 1),
                            shift.getStart().plusDays(i - 1).toLocalDate().atStartOfDay(),
                            endOfDate.plusDays(i - 1),
                            "0hr 0mins"
                    );
                }
                shiftDayTimeList.add(shiftDayTime);

            }
            shiftDayTime = new ShiftDayTime(
                    getDayOfTheWeekFromDate(shift.getStart().plusDays(i - 1)),
                    shift.getStart().plusDays(i - 1),
                    shift.getStart().plusDays(i - 1).toLocalDate().atStartOfDay(),
                    shift.getStart().plusDays(i - 1).toLocalDate().atTime(shift.getEnd().toLocalTime()),
                    "0hr 0mins"
            );
            shiftDayTimeList.add(shiftDayTime);
        }
        return shiftDayTimeList;


    }


    private DailyShiftRate getItemRatePerSlot(LocalDateTime shiftStartTime,
                                              LocalDateTime shiftEndTime,
                                              LocalDateTime rateStartTime,
                                              LocalDateTime rateEndTime,
                                              BigDecimal rate,
                                              String dayOfWeek) {
        log.info("### shift start time {} shift end time {} rate start time {} rate end time {}", shiftStartTime,
                shiftEndTime, rateStartTime, rateEndTime);

        if (shiftStartTime.isAfter(rateEndTime)) {
            return null;
        }

        if (shiftStartTime.isAfter(rateStartTime) || shiftStartTime.isEqual(rateStartTime)) {
            if (shiftEndTime.isBefore(rateEndTime) || shiftEndTime.isEqual(rateEndTime)) {
                log.info("====1==== Take shiftStartTime {} shiftEndTime {} minutes {} ", shiftStartTime,
                        shiftEndTime, Math.abs(shiftStartTime.until(shiftEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(shiftStartTime.until(shiftEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        shiftStartTime,
                        shiftEndTime

                );
            }
        }

        if (shiftStartTime.isAfter(rateStartTime) || shiftStartTime.isEqual(rateStartTime)) {
            if (shiftEndTime.isAfter(rateEndTime)) {
                log.info("===2=== Take shiftStartTime {} rateEndTime {} minutes {} ", shiftStartTime,
                        rateEndTime, Math.abs(shiftStartTime.until(rateEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(shiftStartTime.until(rateEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        shiftStartTime,
                        rateEndTime
                );
            }
        }
        if (shiftStartTime.isAfter(rateStartTime) || shiftStartTime.isEqual(rateStartTime)) {
            if (rateEndTime.isBefore(shiftEndTime)) {
                log.info("===3=== Take rateStartTime {} shiftEndTime {} minutes {} ", rateStartTime,
                        shiftEndTime, Math.abs(rateStartTime.until(shiftEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(rateStartTime.until(shiftEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        rateStartTime,
                        shiftEndTime
                );
            }
        }

        if (shiftStartTime.isBefore(rateStartTime)) {
            if (rateEndTime.isAfter(shiftEndTime) && rateStartTime.isBefore(shiftEndTime)) {
                log.info("===4=== Take rateStartTime {} shiftEndTime {} minutes {} ", rateStartTime,
                        shiftEndTime, Math.abs(rateStartTime.until(shiftEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(rateStartTime.until(shiftEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        rateStartTime,
                        shiftEndTime
                );
            }
        }
        if (shiftStartTime.isBefore(rateStartTime)) {
            if (rateEndTime.isBefore(shiftEndTime) || rateEndTime.isEqual(shiftEndTime)) {
                log.info("===5=== Take rateStartTime {} rateEndTime {} minutes {} ", rateStartTime,
                        rateEndTime, Math.abs(rateStartTime.until(rateEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(rateStartTime.until(rateEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        rateStartTime,
                        rateEndTime
                );
            }
        }

        return null;
    }


    private boolean checkAfter(String leftDate, LocalDate rightDate) {
        if(rightDate==null) return true;
        return !convertFromString(leftDate).isBefore(rightDate);
    }

    private boolean checkBefore(String leftDate, LocalDate rightDate) {
        if(rightDate==null) return true;
        return !convertFromString(leftDate).isAfter(rightDate);
    }

    private LocalDate convertFromString(String aDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        } catch (DateTimeParseException e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return LocalDate.parse(aDate, formatter);
        }
    }




    @Override
    public void delete(Long adviceId) {
        PayAdvice advice = getOne(adviceId);

        if(advice.getPayAdviceStatus()==PayAdviceStatus.PAID && advice.getLastModifiedDate().isBefore(LocalDateTime.now().minusDays(1)))
            throw  new BusinessValidationException("Pay advice can no longer be deleted. It is either paid or was last modified more than a day ago.");

        Long shiftId = 0L;

        List<Shift> shiftsToSave = new ArrayList<>();
        List<WorkerTrainingSession> trainingsToSave = new ArrayList<>();

        for(int i = 0; i < advice.getPayAdviceItems().size(); i++ ){
            if(nonNull(advice.getPayAdviceItems().get(i).getShiftId()) && shiftId != advice.getPayAdviceItems().get(i).getShiftId()) {
                Shift shift = shiftService.getOne(advice.getPayAdviceItems().get(i).getShiftId());
                shift.setStatus(ShiftStatus.AUTHORIZED);
                shift.setShiftWorkerStatus(null);
                shiftService.save(shift);
            }
            shiftId = advice.getPayAdviceItems().get(i).getShiftId();
        }



        payAdviceRepository.delete(advice);



    }


}
