package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.dto.training.TrainingCreateDto;
import com.cap10mycap10.worklinkservice.dto.training.TrainingUpdateDto;
import com.cap10mycap10.worklinkservice.model.Training;
import com.cap10mycap10.worklinkservice.service.TrainingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)

public class TrainingController {

    private final TrainingService trainingService;

    public TrainingController(TrainingService trainingService) {
        this.trainingService = trainingService;
    }

    @PostMapping(value = "training", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Training> create(@RequestBody TrainingCreateDto trainingCreateDto) {
        log.info("Request to add training with : {}", trainingCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        trainingService.toggleAgencyTraining(trainingCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    @GetMapping(value = "training/{id}")
    public ResponseEntity<Training> findById(@PathVariable("id") Long id) {
        log.info("Request to get training with id : {}", id);
        return ResponseEntity.ok(trainingService.findById(id));
    }

    /*@ViewServices*/
    @GetMapping(value = "training")
    public ResponseEntity<List<Training>> findAll() {
        log.info("Request to get all trainings");
        return ResponseEntity.ok(trainingService.findAll());
    }

    /* @ViewServices*/
    @GetMapping(value = "training/{page}/{size}")
    public ResponseEntity<Page<Training>> findAllPaged(@PathVariable("page") int page,
                                                      @PathVariable("size") int size) {
        log.info("Request to get paged trainings : {}, {}", page, size);
        return ResponseEntity.ok(trainingService.findAllPaged(PageRequest.of(page, size)));
    }
    /* @ViewServices*/
    @GetMapping(value = "trainings-agency/{agencyId}")
    public ResponseEntity<List<Training>> findAlwlPaged(@PathVariable("agencyId") Long agencyId
                                                      ) {
//        log.info("Request to get paged trainings : {}, {}", page, size);
//        return ResponseEntity.ok(trainingService.findAllPaged(PageRequest.of(page, size)));
        return ResponseEntity.ok(trainingService.getForAgency(agencyId));
    }

    @PutMapping(value = "training", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> update(@RequestBody TrainingUpdateDto trainingUpdateDto) {
        log.info("Request to update training with : {}", trainingUpdateDto);
        trainingService.save(trainingUpdateDto);
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "training/agency/{agencyId}/{trainingId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> addTraining(@PathVariable("agencyId") Long agencyId,
                                                        @PathVariable("trainingId") Long trainingId

    ) {
        log.info("Request to add agency training with agencyId, trainingId : {}, {}",agencyId,trainingId );
        trainingService.toggleAgencyTraining(agencyId, trainingId);
        return ResponseEntity.ok().build();
    }

    @GetMapping(value = "agency-trainings/{id} ")
    public ResponseEntity<List<Training>> getForAgency( @PathVariable("id") Long agencyId ) {
        log.info("Request to add agency training with agencyId, trainingId : {}",agencyId );

        return ResponseEntity.ok(trainingService.getForAgency(agencyId));
    }

    @DeleteMapping(value = "training/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete training session with : {}", id);
        trainingService.deleteTraining(id);
        return ResponseEntity.noContent().build();
    }
}
