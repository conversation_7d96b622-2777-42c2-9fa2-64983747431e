package com.cap10mycap10.worklinkservice.dto.client;


import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.Address;
import lombok.Data;

@Data

public class ClientUpdateDto {

    private Long id;
    private String sbsCode;
    private String purchaseOrder;

    private String name;

    private String telephone;

    private Address address;

    private String email;
    private String billingEmail;

    private String logo;

    private Status status;

    private Long serviceId;
}
