package com.cap10mycap10.worklinkservice.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Value("${env.companyName:WorkLink}")
    private String companyName;

    @Value("${storage.volume.path:}")
    private String rootPath;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(companyName + " Service")
                        .description("Clients, Workers, Agencies")
                        .version("1.0")
                        .contact(new Contact()
                                .name("<PERSON><PERSON>")
                                .email("<EMAIL>")
                                .url("+263774483751")));
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        if (rootPath != null && !rootPath.isEmpty()) {
            String path2 = "file:///" + rootPath;
            registry.addResourceHandler("/tina/**").addResourceLocations(path2);
        }
    }
}
