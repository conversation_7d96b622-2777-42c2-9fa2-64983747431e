package com.cap10mycap10.worklinkservice.mapper.notification;


import com.cap10mycap10.worklinkservice.dto.notification.NotificationResultDto;
import com.cap10mycap10.worklinkservice.model.Notification;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

import java.util.Calendar;

@Component
public class NotificationToNotificationResultDto implements Converter<Notification, NotificationResultDto> {

    @Override
    public NotificationResultDto convert(Notification notification) {
        NotificationResultDto notificationResultDto = new NotificationResultDto();
        if(notification
                .getSenderAgency()!=null) {
            notificationResultDto.setAgencyId(notification.getSenderAgency().getId());
        }
        if(notification
                .getWorker()!=null) {
            notificationResultDto.setWorkerId(notification.getWorker().getId());
        }
        notificationResultDto.setId(notification.getId());
        notificationResultDto.setBody(notification.getBody());
        notificationResultDto.setTitle(notification.getTitle());
        notificationResultDto.setToken(notification.getToken());

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -7);
        System.out.println("Date = "+ cal.getTime());

        if(notification.getCreatedDate().isBefore(cal.getTime().toInstant())){
            return null;
        }else{
            return notificationResultDto;
        }
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
