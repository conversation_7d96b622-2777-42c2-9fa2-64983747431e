package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.availability.IAvailabilityResultDto;
import com.cap10mycap10.worklinkservice.model.Availability;
import com.cap10mycap10.worklinkservice.model.Worker;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;

public interface AvailabilityRepository extends JpaRepository<Availability, Long> {

    List<Availability> findAllByDateAndWorker(LocalDate date, Worker worker);


    @Query(value = "select date        ,\n" +
            "       start_time       as startTime,\n" +
            "       end_time         as endTime,\n" +
            "       reason             ,\n" +
            "       id             \n" +
            "from availability\n" +
            "where worker_id = ?1" , nativeQuery = true)
    Page<IAvailabilityResultDto> findAllWorkerAvailability(Long workerId, PageRequest of);
}

