package com.cap10mycap10.worklinkservice.events.email;


import com.cap10mycap10.worklinkservice.model.Shift;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.multipart.MultipartFile;

import jakarta.mail.MessagingException;
import java.io.IOException;
import java.util.List;

public interface EmailService {
    @Async("threadPoolTaskExecutor")
    void sendEmailAsUserReply(List<String> to, String subject, String text, String replyToEmail, String replyToName, Long agencyId);

    SimpleMailMessage sendSimpleMessage(String to, String subject, String text, Long agencyId);
}