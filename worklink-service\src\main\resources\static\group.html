<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register, Login & Chat</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.16/dist/tailwind.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
        }
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f7fafc;
        }
        /* Sections for transitions */
        .register-container, .login-container, .chat-container {
            display: block;
        }
        .show-register .register-container,
        .show-login .login-container,
        .show-chat .chat-container {
            display: block;
        }
    </style>
</head>
<body class="show-register">
<div class="container">
    <!-- Chat Container -->
    <div class="chat-container w-full max-w-6xl bg-white p-8 rounded-lg shadow-lg flex">
        <!-- Sidebar for Group List -->
        <div class="w-1/4 p-4 border-r border-gray-300"> <!-- 25% for group list -->
            <h2 class="text-xl font-bold text-gray-800 mb-4">Your Groups</h2>
            <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 w-full rounded mb-4 focus:outline-none focus:ring focus:ring-blue-200">
                + Add New Group
            </button>
            <ul class="space-y-2">
                <li><button class="w-full text-left py-2 px-4 rounded-lg hover:bg-blue-100 border-b border-gray-200">Group 1</button></li>
                <li><button class="w-full text-left py-2 px-4 rounded-lg hover:bg-blue-100 border-b border-gray-200">Group 2</button></li>
                <li><button class="w-full text-left py-2 px-4 rounded-lg hover:bg-blue-100 border-b border-gray-200">Group 3</button></li>
            </ul>
        </div>

        <!-- Main Chat Section -->
        <div class="w-3/4 p-4 flex flex-col"> <!-- 75% for chat section -->
            <div class="flex justify-between items-center mb-4 border-b border-gray-300 pb-2">
                <h2 class="text-xl font-bold text-gray-800">Group: <span id="groupName">Group 1</span></h2>
                <h2 class="text-lg font-bold text-gray-800">User: <span id="userName">User Name</span></h2> <!-- Display User's Name -->
            </div>

            <!-- Chat Messages -->
            <div class="flex-grow p-4 bg-gray-100 rounded mb-4 overflow-y-auto" id="chatMessages">
                <!-- Message examples -->
                <div class="mb-4 flex border-b border-gray-200 pb-2">
                    <strong class="text-blue-500">User1:</strong>
                    <span class="ml-2 text-gray-700">Hello!</span>
                </div>
                <div class="mb-4 flex justify-end border-b border-gray-200 pb-2">
                    <span class="text-blue-500">Hi there!</span>
                    <strong class="ml-2">You</strong>
                </div>
            </div>

            <!-- Message Input -->
            <div class="flex items-center border-t border-gray-300 pt-2">
                <input type="text" id="messageInput" placeholder="Type a message..." class="border rounded-l-lg py-3 px-4 w-full focus:outline-none focus:ring focus:border-blue-300">
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-r-lg focus:outline-none focus:ring focus:ring-blue-200">
                    Send
                </button>
            </div>
        </div>
    </div>
</div>




</body>
</html>