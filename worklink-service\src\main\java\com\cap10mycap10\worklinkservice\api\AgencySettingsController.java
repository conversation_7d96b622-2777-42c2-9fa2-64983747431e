package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.settings.AgencySettingsCreateDto;
import com.cap10mycap10.worklinkservice.model.AgencySettings;
import com.cap10mycap10.worklinkservice.service.AgencySettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/agency-settings", produces = MediaType.APPLICATION_JSON_VALUE)
public class AgencySettingsController {

    private final AgencySettingsService agencySettingsService;

    public AgencySettingsController(AgencySettingsService agencySettingsService) {
        this.agencySettingsService = agencySettingsService;
    }

    @PostMapping
    public ResponseEntity<AgencySettings> create(@RequestBody AgencySettingsCreateDto settingsCreateDto) {
        return new ResponseEntity<>(agencySettingsService.create(settingsCreateDto), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<AgencySettings> update(@PathVariable Long id, @RequestBody AgencySettingsCreateDto createDto) {
        return ResponseEntity.ok(agencySettingsService.update(id, createDto));
    }

    @GetMapping
    public ResponseEntity<AgencySettings> getAgencySettings(@RequestParam Long agencyId) {
        return ResponseEntity.ok(agencySettingsService.findAgencySettings(agencyId));
    }
}
