package com.cap10mycap10.worklinkservice.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;


@EqualsAndHashCode(callSuper = true)
@Entity
@Data
public class VehicleInventory extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dateInstalled = LocalDate.now();
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate nextCheckDate;
    private String name;
    private String description;
    private String photoUrl1;
    private String photoUrl2;
    private String photoUrl3;
    private String photoUrl4;
    private Float price;

    // Tax-related fields
    private Boolean taxExempt = false; // Whether this addon is exempt from tax
    private Boolean taxInclusive; // Whether the price is tax-inclusive (null = use agency default)

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "custom_tax_rate_id")
    private TaxRate customTaxRate; // Custom tax rate for this addon (null = use agency default)

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private Vehicle vehicle;

    @Transient
    private Long vehicleId;


    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @ManyToMany(mappedBy = "addons", fetch = FetchType.LAZY, cascade = {
            CascadeType.ALL})
    private Set<VehicleBooking> vehicleBookings  = new HashSet<>();

}
