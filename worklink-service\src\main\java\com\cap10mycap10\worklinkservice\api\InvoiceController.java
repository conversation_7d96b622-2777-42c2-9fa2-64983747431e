package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.GeneralResponse;
import com.cap10mycap10.worklinkservice.dto.billing.InvoiceCreateDto;
import com.cap10mycap10.worklinkservice.dto.invoice.InvoiceResult;
import com.cap10mycap10.worklinkservice.enums.InvoiceType;
import com.cap10mycap10.worklinkservice.enums.SettlementStatus;
import com.cap10mycap10.worklinkservice.model.SettlementStatement;
import com.cap10mycap10.worklinkservice.reports.ReportFormat;
import com.cap10mycap10.worklinkservice.service.InvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
//import zw.co.paynow.responses.WebInitResponse;

import jakarta.servlet.http.HttpServletRequest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class InvoiceController {

    private final InvoiceService invoiceService;

    public InvoiceController(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }

    public static final String ROLE_ADMIN_ADMIN = "ROLE_ADMIN_ADMIN";
    @PostMapping(value = "invoice/client/create")
    public ResponseEntity<GeneralResponse> createClientInvoice(HttpServletRequest servletRequest, @RequestParam("payerId") Long clientId,
                                                               @RequestParam("payeeId") Long agentId,
                                                               @RequestBody InvoiceCreateDto invoiceCreateDto
    ) throws Exception {
        log.info("Request to create client invoice : {}", clientId);
        invoiceCreateDto.setInvoiceType(InvoiceType.CLIENT);

        invoiceService.createInvoice(clientId, agentId, invoiceCreateDto.getShiftIds(), null,invoiceCreateDto);
        return ResponseEntity.ok(new GeneralResponse("Invoice created successfully", "00"));
    }

    @PostMapping(value = "invoice/trainer/invoice-trainings")
    public ResponseEntity<GeneralResponse> createAgencyTrainingsInvoice(HttpServletRequest servletRequest, @RequestParam("agencyId") Long agencyId,
                                                               @RequestParam("trainerId") Long trainerId,
                                                               @RequestBody InvoiceCreateDto invoiceCreateDto
    ) throws Exception {
        log.info("Request to create client invoice : {}", agencyId);
        invoiceCreateDto.setInvoiceType(InvoiceType.AGENCYTRAINING);

        invoiceService.invoiceAgencyForTrainings(agencyId, trainerId, invoiceCreateDto);
        return ResponseEntity.ok(new GeneralResponse("Invoice created successfully", "00"));
    }


    @PostMapping(value = "invoice/agency/create")
    public ResponseEntity<GeneralResponse> createAgencynvoice(
            @RequestParam("agencyId") Long agencyId,
            @RequestBody InvoiceCreateDto invoiceCreateDto
    ) throws Exception {
        log.info("Request to create client invoice : {}", agencyId);
        invoiceCreateDto.setInvoiceType(InvoiceType.AGENCY);
        invoiceService.createInvoice(null, agencyId, invoiceCreateDto.getShiftIds(), null, invoiceCreateDto);
        return ResponseEntity.ok(new GeneralResponse("Invoice created successfully", "200"));
    }


    @PostMapping(value = "invoice/admin-billing/agency/{agencyId}")
    public ResponseEntity<GeneralResponse> generateAgencyAdminBills(
            @PathVariable("agencyId") Long agencyId
    ) throws Exception {
        log.info("Request to force create due agency invoice : {}", agencyId);
        invoiceService.forceGenerateUnbilledItems(agencyId);
        return ResponseEntity.ok(new GeneralResponse("Invoice created successfully", "200"));
    }


    @GetMapping(value = "invoice/client/view")
    public ResponseEntity<Page<InvoiceResult>> getClientInvoices(@RequestParam("clientId") Long clientId,
                                                                 @RequestParam("page") Integer page,
                                                                 @RequestParam("size") Integer size) {
        log.info("Request to get client invoices : {}", clientId);
        return ResponseEntity.ok(invoiceService.getForClient(clientId, page, size));
    }
    @GetMapping(value = "invoice/admin-agency/view")
    public ResponseEntity<Page<InvoiceResult>> getAgencyInvoicesByAdmin(@RequestParam("agencyId") Long agencyId,
                                                                 @RequestParam("page") Integer page,
                                                                 @RequestParam("size") Integer size) {
        log.info("Request to get admin agency invoices : {}", agencyId);
        return ResponseEntity.ok(invoiceService.getAllAgencyInvoicesByAdmin(agencyId, page, size));
    }

    @GetMapping(value = "invoice/trainer/worker/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<InvoiceResult>> getTrainerWorkerInvoices(@PathVariable("agencyId") Long agencyId,
                                                                        @PathVariable("page") Integer page,
                                                                        @PathVariable("size") Integer size,
                                                                        @RequestParam(required = false) Long trainingId,
                                                                        @RequestParam(required = false) Boolean onlyPaid,
                                                                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to get trainer individual invoices : {}", agencyId);
        return ResponseEntity.ok(invoiceService.getTrainerWorkerInvoices(agencyId,onlyPaid, trainingId,  startDate, endDate, PageRequest.of(page, size)));
    }

    @GetMapping(value = "invoice/trainer/agency/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<InvoiceResult>> getTrainerAgencyInvoices(@PathVariable("agencyId") Long agencyId,
                                                                        @PathVariable("page") Integer page,
                                                                        @PathVariable("size") Integer size,
                                                                        @RequestParam(required = false) Long agencyIdFilter,
                                                                        @RequestParam(required = false) Boolean onlyPaid,
                                                                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to get trainer individual invoices : {}", agencyId);
        return ResponseEntity.ok(invoiceService.getTrainerAgencyInvoices(agencyId,onlyPaid,  agencyIdFilter,  startDate, endDate, PageRequest.of(page, size)));
    }

    @GetMapping(value = "invoice/worker/{workerId}/{page}/{size}")
    public ResponseEntity<Page<InvoiceResult>> getWorkerInvoices(@PathVariable("workerId") Long workerId,
                                                                 @RequestParam("page") Integer page,
                                                                 @RequestParam("size") Integer size) {
        log.info("Request to get worker invoices : {}", workerId);
        return ResponseEntity.ok(invoiceService.getWorkerInvoices( workerId,  page,  size));
    }

    @GetMapping(value = "invoice/admin-agency/view-reports")
    public ResponseEntity<Page<InvoiceResult>> getAgencyInvoiceReportsByAdmin(@RequestParam("agencyId") Long agencyId,
                                                                 @RequestParam("page") Integer page,
                                                                 @RequestParam("size") Integer size) {
        log.info("Request to get admin agency invoices : {}", agencyId);
        return ResponseEntity.ok(invoiceService.getAllAgencyInvoiceReportsByAdmin(agencyId, page, size));
    }

    @GetMapping(value = "invoice/agency/view")
    public ResponseEntity<Page<InvoiceResult>> getAgencyInvoices(@RequestParam("agencyId") Long agencyId,
                                                                 @RequestParam("page") Integer page,
                                                                 @RequestParam("size") Integer size) {
        log.info("Request to get agency invoices : {}", agencyId);
        return ResponseEntity.ok(invoiceService.getAllInvoicesForAgent(agencyId, page, size));
    }

    @GetMapping(value = "invoice/agency/view-reports")
    public ResponseEntity<Page<InvoiceResult>> getAgencyInvoiceReports(@RequestParam("agencyId") Long agencyId,
                                                                 @RequestParam("page") Integer page,
                                                                 @RequestParam("size") Integer size) {
        log.info("Request to get agency invoices : {}", agencyId);
        return ResponseEntity.ok(invoiceService.getAllInvoiceReportsForAgent(agencyId, page, size));
    }



    @GetMapping(value = "invoice/admin/view")
    public ResponseEntity<Page<InvoiceResult>> getAdminInvoices( @RequestParam("page") Integer page,
                                                                 @RequestParam("size") Integer size) {
        log.info("Request to get admin invoices");
        return ResponseEntity.ok(invoiceService.getAllInvoicesForAdmin( page, size));
    }


    @GetMapping(value = "invoice/client/view/invoice")
    public ResponseEntity<InvoiceResult> getClientInvoice(@RequestParam("invoiceId") Long invoiceId) {
        log.info("Request to create client invoice : {}", invoiceId);
        return ResponseEntity.ok(invoiceService.findInvoice(invoiceId));
    }
    @GetMapping(value = "invoice/{id}")
    public ResponseEntity<InvoiceResult> findById(@PathVariable("id") Long id) {
        log.info("Request to get invoice by id : {}", id);
        return ResponseEntity.ok(invoiceService.findInvoice(id));
    }


    @PutMapping(value = "invoice/agency/acknowledge")
    public ResponseEntity<GeneralResponse> acknowledge(@RequestParam("invoiceId") Long invoiceId,
                                                       @RequestParam("amount") BigDecimal amount,
                                                       @RequestParam("paymentRef") String paymentRef) {
        log.info("Request to acknowledge payment for invoice : {}", invoiceId);
        invoiceService.acknowledgePayment(invoiceId, paymentRef, amount);
        return ResponseEntity.ok(new GeneralResponse("Payment submitted successfully", "201"));
    }

//    @PutMapping(value = "invoice/full-payment/{invoiceId}")
//    public ResponseEntity<WebInitResponse> payment(@PathVariable("invoiceId") Long invoiceId) {
//        log.info("Request to acknowledge payment for invoice : {}", invoiceId);
//        return ResponseEntity.ok(invoiceService.fullPaynowPayment(invoiceId));
//    }

    @PutMapping(value = "invoice/agency/discount")
    public ResponseEntity<GeneralResponse> setDiscount(@RequestParam("invoiceId") Long invoiceId,
                                                       @RequestParam("discount") BigDecimal discount) {
        log.info("Request to set discount payment for invoice : {}", invoiceId);
        invoiceService.setDiscount(invoiceId, discount);
        return ResponseEntity.ok(new GeneralResponse("Discount applied.", "201"));
    }

    @PutMapping(value = "invoice/agency/publish")
    public ResponseEntity<GeneralResponse> publish(@RequestParam("invoiceId") Long invoiceId, @RequestParam("sendEmail") Boolean sendEmail) {
        log.info("Request to set discount payment for invoice : {}", invoiceId);
        invoiceService.publish(invoiceId, sendEmail);
        return ResponseEntity.ok(new GeneralResponse("Invoice Published", "201"));
    }


    @DeleteMapping(value = "invoice/{invoiceId}")
    public ResponseEntity<GeneralResponse> delete(@PathVariable Long invoiceId) {
        log.info("Request to delete invoice : {}", invoiceId);
        invoiceService.delete(invoiceId);
        return ResponseEntity.ok(new GeneralResponse("Invoice Deleted", "202"));
    }


    @Secured({ROLE_ADMIN_ADMIN})
    @DeleteMapping(value = "invoice/admin/{invoiceId}")
    public ResponseEntity<GeneralResponse> deleteAdminInvoice(@PathVariable Long invoiceId) {
        log.info("Request to delete invoice : {}", invoiceId);
        invoiceService.deleteAdminInvoice(invoiceId);
        return ResponseEntity.ok(new GeneralResponse("Invoice Deleted", "202"));
    }



    @GetMapping(value = "invoice/download")
    public ResponseEntity<Resource> downloadInvoice(@RequestParam Long invoiceId,
                                                    @RequestParam ReportFormat format,
                                                    HttpServletRequest servletRequest) {

        return invoiceService.downloadInvoice(invoiceId, format,servletRequest);
    }


        @GetMapping("/invoices/agency/settlement")
        public ResponseEntity<Page<InvoiceResult>> getPaidInvoicesDueForSettlement(
                @RequestParam(required = false) Long rentalId,
                @RequestParam(required = false) String status,
                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
                @RequestParam(required = false) Long bookingId,
                @RequestParam(defaultValue = "0") int page,
                @RequestParam(defaultValue = "25") int size) {

            Page<InvoiceResult> invoices = invoiceService.getPaidInvoicesDueForSettlement(rentalId, status, startDate, endDate, bookingId, page, size);
            return ResponseEntity.ok(invoices);
        }



        @PostMapping("/invoices/{carRentalId}/settle")
        public ResponseEntity<Void> settleInvoices(@RequestParam Set<Long> invoices, @PathVariable("carRentalId") Long carRentalId) {
            invoiceService.settleInvoices(carRentalId, invoices);
            return ResponseEntity.ok().build();
        }


        @GetMapping
        public ResponseEntity<Page<SettlementStatement>> getSettlementsByFilters(
                @RequestParam(required = false) Long carRentalId,
                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate createdDateStart,
                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate createdDateEnd,
                @RequestParam(required = false) Long id,
                @RequestParam(required = false) Double amount,
                @RequestParam(required = false) String status,
                @RequestParam(defaultValue = "0") int page,
                @RequestParam(defaultValue = "25") int size) {

            Page<SettlementStatement> settlements = invoiceService.getSettlementsByFilters(carRentalId, createdDateStart, createdDateEnd, id, amount, status, page, size);
            return ResponseEntity.ok(settlements);
        }



}
