package com.cap10mycap10.worklinkservice.dto.invoice;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShiftDayTime {

    private String dayOfWeek;

    private LocalDate shiftDate;

    private LocalDateTime shiftStartTime;

    private LocalDateTime shiftEndTime;

    private String breakTime;

}
