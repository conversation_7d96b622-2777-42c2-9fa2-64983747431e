package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.model.Notification;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BroadcastService {
    Notification broadcast(NotificationCreateDto notificationCreateDto );

    List<Notification> findAgencySent(Long id);

    void delete(Long id);

    List<Notification> findClientSent(Long id);
    List<Notification> findAdminSent();

    NotificationCreateDto findByWorkerId(Long id);


    void deleteById(Long id);

    Notification getOne(Long notificationId);

    void addGeneralSignature(Long workerId, MultipartFile file);
}
