package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.TrainingFeedbackRepository;
import com.cap10mycap10.worklinkservice.dto.trainingfeedback.TrainingFeedbacksRes;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.TrainingFeedback;
import com.cap10mycap10.worklinkservice.model.TrainingSession;
import com.cap10mycap10.worklinkservice.model.WorkerTrainingSession;
import com.cap10mycap10.worklinkservice.service.TrainingFeedbackService;
import com.cap10mycap10.worklinkservice.service.TrainingSessionService;
import com.cap10mycap10.worklinkservice.service.WorkerTrainingSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TrainingFeedbackServiceImpl implements TrainingFeedbackService {
    @Autowired
    private TrainingFeedbackRepository trainingFeedbackRepository;
    @Autowired
    private WorkerTrainingSessionService workerTrainingSessionService;

    @Autowired
    private TrainingSessionService trainingSessionService;

    @Override
    public TrainingFeedback save(TrainingFeedback trainingFeedback, Long bookingId) {
        WorkerTrainingSession booking = workerTrainingSessionService.getOne(bookingId);

        if(booking.getTrainingSession().getStartDateTime().isBefore(LocalDateTime.now())){
            throw new BusinessValidationException("Cannot give feedback on a training which hasn't started");
        }
        if(booking.getTrainingSession().getEndDateTime().isBefore(LocalDateTime.now())){
            throw new BusinessValidationException("Cannot give feedback on a training which hasn't completed.");
        }
        if(booking.getSkippedTraining()){
            throw new BusinessValidationException(booking.getWorker().getFirstname() +
                    " Did not attend training therefore cannot give feedback on the training");
        }
        trainingFeedback.setBooking(booking);
        return trainingFeedbackRepository.save(trainingFeedback);
    }

    @Override
    public TrainingFeedback findById(Long id) {
        return getOne(id);
    }



    @Override
    @Transactional
    public  TrainingFeedbacksRes findBySession(Long id) {
        TrainingSession trainingSession = trainingSessionService.getOne(id);
        List<TrainingFeedback> trainingFeedbackList = trainingFeedbackRepository.findBySessionId(id);
        Set<WorkerTrainingSession> workerTrainingSessionSet = trainingSession.getWorkerTrainingSessions();

        int attendance = 0;

        for(WorkerTrainingSession workerTrainingSession:workerTrainingSessionSet){
            if(workerTrainingSession.getTrainingStatus().name().equals("CLOSED") && !workerTrainingSession.getSkippedTraining()){
                attendance++;
            }
        }

        TrainingFeedbacksRes trainingFeedbacksRes = new TrainingFeedbacksRes();
        trainingFeedbacksRes.setFeedbackList(trainingFeedbackList);
        trainingFeedbacksRes.setTrainingSession(trainingSession);
        trainingFeedbacksRes.setAttendance(attendance);

        return trainingFeedbacksRes ;
    }


    @Override
    public void deleteById(Long id) {
        TrainingFeedback trainingFeedback = getOne(id);
        try {
            trainingFeedbackRepository.deleteById(id);
            trainingFeedbackRepository.flush();
        }catch (Exception ex){
            throw new BusinessValidationException("TrainingFeedback cannot be deleted");
        }
    }


    public TrainingFeedback getOne(Long id) {
        return trainingFeedbackRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("TrainingFeedback not found"));
    }
}
