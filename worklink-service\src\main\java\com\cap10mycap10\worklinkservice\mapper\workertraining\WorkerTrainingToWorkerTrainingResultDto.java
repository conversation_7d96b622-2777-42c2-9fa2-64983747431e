package com.cap10mycap10.worklinkservice.mapper.workertraining;

import com.cap10mycap10.worklinkservice.dao.WorkerTrainingRepository;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingResultDto;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerToWorkerResultDto;
import com.cap10mycap10.worklinkservice.model.WorkerTraining;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;

import static java.util.Objects.nonNull;

@Component
@Slf4j
public class WorkerTrainingToWorkerTrainingResultDto implements Converter<WorkerTraining, WorkerTrainingResultDto> {

    private final WorkerTrainingRepository workerTrainingRepository;

    @Autowired
    private   AgencyToAgencyResultDto toAgencyResultDto;
    @Autowired
    private WorkerToWorkerResultDto toWorkerResultDto;
    public WorkerTrainingToWorkerTrainingResultDto(WorkerTrainingRepository workerTrainingRepository) {
        this.workerTrainingRepository = workerTrainingRepository;
    }

    @Override
    public WorkerTrainingResultDto convert(WorkerTraining workerTraining) {
        WorkerTrainingResultDto workerTrainingResultDto = new WorkerTrainingResultDto();
        workerTrainingResultDto.setId(workerTraining.getId());

        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        if (workerTraining.getTrainingDate() != null) {
            String string = workerTraining.getTrainingDate().format(pattern);
            workerTrainingResultDto.setTrainingDate(string);
        }

        if (workerTraining.getTrainingExpiry() != null) {
            String string = workerTraining.getTrainingExpiry().format(pattern);
            workerTrainingResultDto.setTrainingExpiry(string);
        }

        if (workerTraining.getStatus() != null) {
            workerTrainingResultDto.setStatus(workerTraining.getStatus().toString());
        }

        if (workerTraining.getDocument() != null) {
            workerTrainingResultDto.setDocument(workerTraining.getDocument());
        }


        if (workerTraining.getComment() != null) {
            workerTrainingResultDto.setComment(workerTraining.getComment());
        }

        if(nonNull(workerTraining.getWorkerTrainingSession())) {
            workerTrainingResultDto.setShowCertificate(workerTraining.getWorkerTrainingSession().getShowCertificate());
            workerTrainingResultDto.setSupervisor(workerTraining.getWorkerTrainingSession().getTrainingSession().getSupervisor());

        }

        workerTrainingResultDto.setName(workerTraining.getName());

        if(nonNull(workerTraining.getAgency()))workerTrainingResultDto.setAgency(toAgencyResultDto.convert(workerTraining.getAgency()));
        if(nonNull(workerTraining.getWorker()))workerTrainingResultDto.setWorker(toWorkerResultDto.convert(workerTraining.getWorker()));



        workerTrainingResultDto.setType(workerTraining.getType());
        workerTrainingResultDto.setCode(workerTraining.getCode());
        workerTrainingResultDto.setDescription(workerTraining.getDescription());
//        workerTrainingResultDto.setHascoCourseId(workerTraining.getHascoCourseId());
//        workerTrainingResultDto.setHascoId(workerTraining.getHascoId());


        return workerTrainingResultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
