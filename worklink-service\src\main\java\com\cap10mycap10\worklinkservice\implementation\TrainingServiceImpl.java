package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.TrainingRepository;
import com.cap10mycap10.worklinkservice.dto.training.TrainingCreateDto;
import com.cap10mycap10.worklinkservice.dto.training.TrainingUpdateDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Training;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ServicesService;
import com.cap10mycap10.worklinkservice.service.TrainingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
@Slf4j
public class TrainingServiceImpl implements TrainingService {
    private final TrainingRepository trainingRepository;

    private final ServicesService servicesService;

    @Autowired
    private AgencyService agencyService;

    public TrainingServiceImpl(TrainingRepository trainingRepository, ServicesService servicesService) {
        this.trainingRepository = trainingRepository;
        this.servicesService = servicesService;
    }

    @Override
    public void toggleAgencyTraining(TrainingCreateDto trainingCreateDto) {
        Training training = new Training();
        training.setName(trainingCreateDto.getName());
        training.setDescription(trainingCreateDto.getDescription());
        training.setCode(trainingCreateDto.getCode());
        training.setServices(servicesService.getOne(trainingCreateDto.getServiceId()));
        trainingRepository.save(training);

    }

    @Override
    public void deleteTraining(Long id) {
        Training training = getOne(id);
        try {
            trainingRepository.delete(training);
        }catch (DataIntegrityViolationException e){
            throw new BusinessValidationException("Training is related to existing data and can no longer be deleted.");
        }
    }

    @Override
    public Training findById(Long id) {
        return getOne(id);
    }

    @Override
    public List<Training> findAll() {
        return trainingRepository.findAllByOrderByName();
    }

    @Override
    public Page<Training> findAllPaged(PageRequest of) {
        return trainingRepository.findAll(of);
    }

    @Override
    public Training save(TrainingUpdateDto trainingUpdateDto) {
        Training training = getOne(trainingUpdateDto.getTrainingId());

        training.setName(trainingUpdateDto.getName());
        training.setDescription(trainingUpdateDto.getDescription());
        training.setServices(servicesService.getOne(trainingUpdateDto.getServiceId()));
        training.setCode(trainingUpdateDto.getCode());
        trainingRepository.save(training);
        return training;
    }

    @Override
    @Transactional
    public void toggleAgencyTraining(Long agencyId, Long trainingId) {
        Training training = getOne(trainingId);
        Agency agency = agencyService.getOne(agencyId);

        Set<Training> trainings = agency.getTrainings();

        if(trainings.contains(training)){
            trainings.remove(training);
        }else{
            trainings.add(training);
        }

        agency.setTrainings(trainings);

        agencyService.save(agency);
    }

    @Override
    public Training getOne(Long id) {
        return trainingRepository.findById(id).orElseThrow(
                () -> new RecordNotFoundException(String.format("Training with id: %s was not found", id)));
    }
    @Override
    public Optional<Training> findByIHascoId(Long id) {

        List<Training> res = trainingRepository.findAllByHascoId(id);
        return res.stream().findFirst();
    }

    @Override
    @Transactional
    public List<Training> getForAgency(Long agencyId) {
        return new ArrayList<>(agencyService.getOne(agencyId).getTrainings());
    }
}
