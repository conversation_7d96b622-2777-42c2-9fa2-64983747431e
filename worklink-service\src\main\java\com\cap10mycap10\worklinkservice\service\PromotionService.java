package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.Promotion;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.model.VehicleBooking;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.ZonedDateTime;
import java.util.Set;

public interface PromotionService {
    PromotionDto save(PromotionDto promotionDto);
    PromotionDto findById(Long id);

    void modifyPendingPromotions();

    Page<PromotionDto> findAll(Long agencyId, Set<Status> statuses, String query, Pageable of);
    PromotionDto update(PromotionDto promotionDto);
    void cancel(Long id);
    void activate(Long id);
    Promotion getOne(Long id);

    Promotion getOneByCode(String id);

    Promotion getLatestValidPublicPromotion(VehicleBooking vehicleBooking, boolean fromAdminWebsite);

    Page<Promotion> getLatestPublicPromotions(Vehicle vehicle, ZonedDateTime searchStartDate, ZonedDateTime searchEndDate);
    Page<Promotion> getLatestPromotions(Vehicle vehicle, ZonedDateTime searchStartDate, ZonedDateTime searchEndDate);

    /**
     * Adds vehicles to promotions in batches
     * This method is called by the scheduler to gradually add vehicles to promotions
     * that have more than 50 vehicles matching their filter criteria
     */
    void addVehiclesToPromotions();
}
