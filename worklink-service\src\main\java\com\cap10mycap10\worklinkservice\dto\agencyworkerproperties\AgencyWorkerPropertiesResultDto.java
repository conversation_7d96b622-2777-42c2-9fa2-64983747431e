package com.cap10mycap10.worklinkservice.dto.agencyworkerproperties;

import com.cap10mycap10.worklinkservice.model.Training;
import lombok.Data;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
public class AgencyWorkerPropertiesResultDto {

    private Long id;

    private Long agencyId;

    private Long workerId;

    private List<Training> trainings = new ArrayList<>();

    private String paymentMethod;

    private String employmentStartDate;

    private String contractEndDate;

    private String nextCheckDate;

    private String trainingDate;

    private String trainingExpiry;

    private String rightToWork;

    private String dbsNumber;

    private String dbsExpiry;

    private String expiry;

    private String restrictions;

    private String restrictionExpiry;

    private String createdBy;

    private LocalDate visaExpiry;
    private Boolean paperwork;
    private String approver;
    private String position;
    private String comment;
    private String signed;
}
