package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.WorkerStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.*;
import org.hibernate.search.mapper.pojo.mapping.definition.annotation.Indexed;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;


@Entity
@Indexed
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class Worker extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long deputyId;
    @Column(nullable = false)
    private String firstname;
    @Column(nullable = false)
    private String lastname;
    private String address;
    private String postcode;
    private String profilePic;
    @Enumerated(EnumType.STRING)
    private Gender gender;
    private String phoneNumber;
    @Column(unique = true, nullable = false)
    private String email;
    @Column(unique = true, nullable = false)
    private String username;
    @Column(unique = true)
    private String employmentNumber;
    private String Nationality;

    private LocalDate dob;
    private String cv;
    private Long hascoId;
    @OneToOne()
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private AssignmentCode assignmentCode;
    @Enumerated(EnumType.STRING)
    @Transient
    private WorkerStatus status;
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany( mappedBy="worker", fetch = FetchType.LAZY)
    private Set<Payslip> payslips = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany(mappedBy = "worker")
    private Set<Device> devices = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany(mappedBy = "worker")
    private List<Bank> bank = new ArrayList<>();
    @EqualsAndHashCode.Exclude
    @JsonIgnore
    @ToString.Exclude
    @OneToMany(mappedBy = "worker")
    private Set<WorkerTrainingSession> workerTrainingSessions = new HashSet<>();
    @ManyToMany(mappedBy = "workers" )
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Set<Agency> agencySet = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "worker")
    private Set<Shift> shifts = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany( mappedBy="worker")
    private Set<Availability> availabilities = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany( mappedBy="worker")
    private Set<Note> notes = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany( mappedBy="worker")
    private Set<Notification> notifications = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany( mappedBy="worker")
    private Set<WorkerTraining> trainings = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany( mappedBy="worker")
    private Set<WorkerCompliance> compliance = new HashSet<>();
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany(mappedBy = "worker", fetch = FetchType.LAZY)
    Set<AgencyWorkerProperties> properties;

    @ManyToMany(fetch = FetchType.EAGER, mappedBy = "chatGroupMembers")
    Set<ChatGroup> chatGroups = new HashSet<>();

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Worker tag = (Worker) o;
        return Objects.equals(username, tag.username);
    }
    public String getAddress() {
        return address;
    }
    public void setAddress(String address) {
        this.address = address;
    }
    public String getPostcode() {
        return postcode;
    }
    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }
    public String getProfilePic() {
        return profilePic;
    }
    public void setProfilePic(String profilePic) {
        this.profilePic = profilePic;
    }
    public Set<Availability> getAvailabilities() {
        return availabilities;
    }
    public void setAvailabilities(Set<Availability> availabilities) {
        this.availabilities = availabilities;
    }
    @Override
    public int hashCode() {
        return Objects.hash(username);
    }
    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Worker{");
        sb.append("id=").append(id);
        sb.append(", firstname='").append(firstname).append('\'');
        sb.append(", lastname='").append(lastname).append('\'');
        sb.append(", gender=").append(gender);
        sb.append(", phoneNumber='").append(phoneNumber).append('\'');
        sb.append(", email='").append(email).append('\'');
        sb.append(", username='").append(username).append('\'');
        sb.append(", assignmentCodeName=").append(assignmentCode);
        sb.append(", employmentNumber=").append(assignmentCode);
        sb.append(", address=").append(address);
        sb.append(", postcode=").append(postcode);
        sb.append(", profilePic=").append(profilePic);
        sb.append(", nationality=").append(assignmentCode);
        sb.append(", cv=").append(assignmentCode);
        sb.append(", dob=").append(assignmentCode);
        sb.append('}');
        return sb.toString();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstname() {
        return firstname;
    }

    public void setFirstname(String firstname) {
        this.firstname = firstname;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmploymentNumber() {
        return employmentNumber;
    }

    public void setEmploymentNumber(String employmentNumber) {
        this.employmentNumber = employmentNumber;
    }

    public String getNationality() {
        return Nationality;
    }

    public void setNationality(String nationality) {
        Nationality = nationality;
    }

    public LocalDate getDob() {
        return dob;
    }

    public void setDob(LocalDate dob) {
        this.dob = dob;
    }

    public String getCv() {
        return cv;
    }

    public void setCv(String cv) {
        this.cv = cv;
    }

    public AssignmentCode getAssignmentCode() {
        return assignmentCode;
    }

    public void setAssignmentCode(AssignmentCode assignmentCode) {
        this.assignmentCode = assignmentCode;
    }

    public Set<Device> getDevices() {
        return devices;
    }

    public void setDevices(Set<Device> devices) {
        this.devices = devices;
    }

    public Set<Agency> getAgencySet() {
        return agencySet;
    }

    public void setAgencySet(Set<Agency> agencySet) {
        this.agencySet = agencySet;
    }

    public Set<AgencyWorkerProperties> getProperties() {
        return properties;
    }

    public void setProperties(Set<AgencyWorkerProperties> properties) {
        this.properties = properties;
    }

    public Set<Note> getNotes() {
        return notes;
    }

    public void setNotes(Set<Note> notes) {
        this.notes = notes;
    }

    public Set<Notification> getNotifications() {
        return notifications;
    }

    public void setNotifications(Set<Notification> notifications) {
        this.notifications = notifications;
    }

    public Set<Payslip> getPayslips() {
        return payslips;
    }

    public void setPayslips(Set<Payslip> payslips) {
        this.payslips = payslips;
    }

    public Set<WorkerTraining> getTrainings() {
        return trainings;
    }

    public void setTrainings(Set<WorkerTraining> trainings) {
        this.trainings = trainings;
    }

    public Long getHascoId() {
        return hascoId;
    }

    public void setHascoId(Long hascoId) {
        this.hascoId = hascoId;
    }

    public List<Bank> getBank() {
        return bank;
    }

    public void setBank(List<Bank> bank) {
        this.bank = bank;
    }

    public Long getDeputyId() {
        return deputyId;
    }

    public void setDeputyId(Long deputyId) {
        this.deputyId = deputyId;
    }

    public Set<WorkerCompliance> getCompliance() {
        return compliance;
    }

    public void setCompliance(Set<WorkerCompliance> compliance) {
        this.compliance = compliance;
    }

    public Set<WorkerTrainingSession> getWorkerTrainingSessions() {
        return workerTrainingSessions;
    }

    public WorkerStatus getStatus() {
        return status;
    }

    public void setStatus(WorkerStatus status) {
        this.status = status;
    }

    public void setWorkerTrainingSessions(Set<WorkerTrainingSession> workerTrainingSessions) {
        this.workerTrainingSessions = workerTrainingSessions;
    }

    public Boolean checkCompliance() {
        for(WorkerTraining training:trainings){
            if(nonNull(training.getTrainingExpiry())&&training.getTrainingExpiry().isBefore(LocalDate.now()))
              return false;
        }

        for(WorkerCompliance compliance1:compliance){
            if(compliance1.getComplianceDate().isBefore(LocalDate.now()))
              return false;
        }
        return true;
    }

    public boolean isMyAgency(Agency agency){
        return this.agencySet.contains(agency);
    }


    public void setActiveAgency(Long agencyId) {
        Optional<AgencyWorkerProperties> prop = this.properties.stream().filter(p -> Objects.equals(p.getAgency().getId(), agencyId)).findFirst();
        if(prop.isPresent()) {
            AgencyWorkerProperties pro = prop.get();
            this.status = pro.getStatus();
        }

    }


    public List<InvoiceItem> getInvoiceItems(LocalDateTime start, LocalDateTime end, int breakTimeMins, String umbrella, Shift shift) {
        Set<AssignmentRate> rates = shift.getAssignmentCode().getAssignmentRates().stream()
                .filter(r -> {
                     var s1 = r.getAssignmentCode();
                     var s2 =shift.getAssignmentCode();
                            return s1==s2 ;
                }).collect(Collectors.toSet());
        Comparator<AssignmentRate> compareByDate = (AssignmentRate c1, AssignmentRate c2) -> c1.getStartTime().compareTo(c2.getStartTime());
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        List<LocalDateTime[]> dates = splitIntoDailyRanges(start, end);

        for (LocalDateTime[] date:dates) {
            //Filter Rates for the day on ass code


            List<AssignmentRate> dayRates = rates.stream()
                    .filter(r -> ((r.getDayOfTheWeek() == date[0].getDayOfWeek())))
                    .sorted(compareByDate).collect(Collectors.toList());

            List<TimeRange> timeRanges = new ArrayList<>();

            dayRates.forEach(r->{timeRanges.add(new TimeRange(r.getStartTime(), r.getEndTime()));});
            this.checkRatesFullDayCoverage(timeRanges, date[0].getDayOfWeek());

            List<InvoiceItem> dayBill =  calculatePaymentForDay( start,  end,  dayRates, umbrella, shift.getId());
            invoiceItems.addAll(dayBill);
        }
        return invoiceItems;
    }


    public static List<InvoiceItem> calculatePaymentForDay(LocalDateTime start, LocalDateTime end, List<AssignmentRate> rates, String umbrella, Long shiftId) {
        List<InvoiceItem> invoiceItems = new ArrayList<>();

        for (AssignmentRate rate : rates) {
            LocalDateTime currentStart = start.isBefore(LocalDateTime.of(start.toLocalDate(), rate.getStartTime())) ?
                    LocalDateTime.of(start.toLocalDate(), rate.getStartTime()) : start;
            LocalDateTime currentEnd = end.isAfter(LocalDateTime.of(end.toLocalDate(), rate.getEndTime())) && rate.getEndTime()!= LocalTime.MIDNIGHT?
                    LocalDateTime.of(end.toLocalDate(), rate.getStartTime()) : end;

//            if(start.toLocalTime()==LocalTime.MIDNIGHT) start =  LocalDateTime.of(start.toLocalDate(), LocalTime.MIDNIGHT);
//            if(end.toLocalTime()==LocalTime.MIDNIGHT) end =  LocalDateTime.of(start.toLocalDate(), LocalTime.MIDNIGHT);

            if(currentEnd.isAfter(currentStart) || currentEnd.isEqual(currentEnd.toLocalDate().atStartOfDay())) {
                long minutesWorked = ChronoUnit.MINUTES.between(currentStart, currentEnd);
                if (minutesWorked < 0) minutesWorked *= -1;
                if(start.toLocalTime()==LocalTime.MIDNIGHT && end.toLocalTime()==LocalTime.MIDNIGHT) minutesWorked  = 24*60;

                BigDecimal totalPayment =  rate.getUmbrellaRate().multiply(BigDecimal.valueOf(minutesWorked / 60.0));

                InvoiceItem invoiceItem = new InvoiceItem();
                invoiceItem.setRate(rate.getUmbrellaRate());
                invoiceItem.setTotal(totalPayment);
                invoiceItem.setShiftId(shiftId);
                invoiceItem.setNumberOfHoursWorked(minutesWorked/60);
                invoiceItem.setDescription("Worker hours for transport job");
                invoiceItems.add(invoiceItem);
            }
        }

        return invoiceItems;
    }

    public  boolean checkRatesFullDayCoverage(List<TimeRange> timeRanges, DayOfWeek dayOfWeek) {
        if(timeRanges.isEmpty())
            throw new BusinessValidationException("Worker rates are not complete. Add for  assignment code "+ this.assignmentCode.getCode()+ " on "+ dayOfWeek);

        timeRanges.sort(Comparator.comparing(tr -> tr.start));
        LocalTime lastEnd = LocalTime.MIDNIGHT;
        for (TimeRange tr : timeRanges) {
            if (!lastEnd.equals(tr.start)) {
                throw new BusinessValidationException("Worker rates are not complete. Add for start time before "+tr.start+" and end time after "+lastEnd+", assignment code "+ this.assignmentCode.getCode()+ " on "+ dayOfWeek);
            }
            lastEnd = tr.end;
        }
        return lastEnd.equals(LocalTime.MIDNIGHT);
    }

    static class TimeRange {
        LocalTime start;
        LocalTime end;

        TimeRange(LocalTime start, LocalTime end) {
            this.start = start;
            this.end = end;
        }
    }

    public static List<LocalDateTime[]> splitIntoDailyRanges(LocalDateTime start, LocalDateTime end) {
        List<LocalDateTime[]> ranges = new ArrayList<>();
        LocalDateTime currentStart = start;
        while (currentStart.isBefore(end)) {
            LocalDateTime currentEnd = currentStart.toLocalDate().atStartOfDay().plusDays(1).minusSeconds(1);
            if (currentEnd.isAfter(end)) { currentEnd = end; }
            ranges.add(new LocalDateTime[]{currentStart, currentEnd});
            currentStart = currentEnd.plusSeconds(1);
        }
        return ranges;
    }
}
