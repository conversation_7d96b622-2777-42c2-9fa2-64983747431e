package com.cap10mycap10.worklinkservice.model;


/*@Entity*/
/*@AllArgsConstructor
@NoArgsConstructor
@Data*/

public class ApprovedAgency  extends AbstractAuditingEntity{

    /*@EmbeddedId
    private ClientAgencyId id;

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("agency_id")
    private Agency agency;

    @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("client_id")
    private Client client;

    @Email
    private String agencyEmail;*/


}
