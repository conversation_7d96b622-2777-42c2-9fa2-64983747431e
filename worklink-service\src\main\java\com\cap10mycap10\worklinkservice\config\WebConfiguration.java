package com.cap10mycap10.worklinkservice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedMethods("POST", "PUT", "GET", "OPTIONS", "DELETE", "HEAD")
                .allowedOrigins("*").maxAge(3600)
                .allowedHeaders("*");
//        .allowedHeaders("x-requested-with", "authorization",
//                        "x-auth-token", "origin", "content-type", "accept");
    }
}