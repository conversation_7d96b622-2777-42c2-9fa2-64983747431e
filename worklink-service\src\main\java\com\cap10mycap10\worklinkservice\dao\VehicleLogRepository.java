package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.model.VehicleLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface VehicleLogRepository extends JpaRepository<VehicleLog, Long> {

    Optional<VehicleLog> findByTransportId(Long id);
    Page<VehicleLog> findByVehicleAgencyIdAndStatus(Long id, LogStatus status, Pageable of);
    Page<VehicleLog> findByWorkerIdAndStatus(Long id,LogStatus status, Pageable of);

    Page<VehicleLog> findByVehicleIdAndStatus(Long id, LogStatus status, Pageable of);

    @Query(value = "select  * FROM vehicle_log  where vehicle_id =?1 order by end_mileage desc   LIMIT 1    " , nativeQuery = true)
    Optional<VehicleLog> findTopByVehicleAndOrderByEndMileageDesc(Long vehicle);

}
