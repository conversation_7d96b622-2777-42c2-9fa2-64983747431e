package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EmailSenderFactoryTest {

    @Mock
    private AgencyEmailConfigurationService agencyEmailConfigService;

    @Mock
    private JavaMailSender defaultEmailSender;

    @InjectMocks
    private EmailSenderFactory emailSenderFactory;

    private AgencyEmailConfiguration testConfig;

    @BeforeEach
    void setUp() {
        // Set up default configuration values
        ReflectionTestUtils.setField(emailSenderFactory, "defaultCompanyName", "Default Company");
        ReflectionTestUtils.setField(emailSenderFactory, "defaultFromEmail", "<EMAIL>");
        ReflectionTestUtils.setField(emailSenderFactory, "defaultSupportEmail", "<EMAIL>");
        ReflectionTestUtils.setField(emailSenderFactory, "defaultWebsiteUrl", "https://default.com");
        ReflectionTestUtils.setField(emailSenderFactory, "defaultCompanyLogo", "https://default.com/logo.png");

        // Inject the mocked defaultEmailSender
        ReflectionTestUtils.setField(emailSenderFactory, "defaultEmailSender", defaultEmailSender);

        testConfig = AgencyEmailConfiguration.builder()
                .id(1L)
                .agencyId(123L)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .smtpAuth(true)
                .smtpStarttlsEnable(true)
                .smtpStarttlsRequired(true)
                .smtpSslEnable(false)
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .replyToEmail("<EMAIL>")
                .supportEmail("<EMAIL>")
                .websiteUrl("https://agency.com")
                .logoUrl("https://agency.com/logo.png")
                .isActive(true)
                .isVerified(true)
                .build();
    }

    @Test
    void testGetEmailSender_WithNullAgencyId_ReturnsDefaultSender() {
        // When
        JavaMailSender result = emailSenderFactory.getEmailSender(null);

        // Then
        assertEquals(defaultEmailSender, result);
        verify(agencyEmailConfigService, never()).findVerifiedActiveByAgencyId(anyLong());
    }

    @Test
    void testGetEmailSender_WithValidAgencyConfig_ReturnsAgencySender() {
        // Given
        when(agencyEmailConfigService.findVerifiedActiveByAgencyId(123L))
                .thenReturn(Optional.of(testConfig));

        // When
        JavaMailSender result = emailSenderFactory.getEmailSender(123L);

        // Then
        assertNotNull(result);
        assertNotEquals(defaultEmailSender, result);
        verify(agencyEmailConfigService).findVerifiedActiveByAgencyId(123L);
    }

    @Test
    void testGetEmailSender_WithNoAgencyConfig_ReturnsDefaultSender() {
        // Given
        when(agencyEmailConfigService.findVerifiedActiveByAgencyId(123L))
                .thenReturn(Optional.empty());

        // When
        JavaMailSender result = emailSenderFactory.getEmailSender(123L);

        // Then
        assertEquals(defaultEmailSender, result);
        verify(agencyEmailConfigService).findVerifiedActiveByAgencyId(123L);
    }

    @Test
    void testGetEmailSender_WithInvalidAgencyConfig_ReturnsDefaultSender() {
        // Given
        AgencyEmailConfiguration invalidConfig = AgencyEmailConfiguration.builder()
                .agencyId(123L)
                .isActive(true)
                .isVerified(true)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword("password123")
                .fromEmail("<EMAIL>")
                // Missing fromName - makes it not ready to use
                .build();

        when(agencyEmailConfigService.findVerifiedActiveByAgencyId(123L))
                .thenReturn(Optional.of(invalidConfig));

        // When
        JavaMailSender result = emailSenderFactory.getEmailSender(123L);

        // Then
        assertEquals(defaultEmailSender, result);
        verify(agencyEmailConfigService).findVerifiedActiveByAgencyId(123L);
    }

    @Test
    void testGetEmailConfiguration_WithNullAgencyId_ReturnsDefaultConfig() {
        // When
        EmailSenderFactory.EmailConfiguration result = emailSenderFactory.getEmailConfiguration(null);

        // Then
        assertNotNull(result);
        assertEquals("Default Company", result.getFromName());
        assertEquals("<EMAIL>", result.getFromEmail());
        assertEquals("<EMAIL>", result.getSupportEmail());
        assertEquals("https://default.com", result.getWebsiteUrl());
        assertEquals("https://default.com/logo.png", result.getLogoUrl());
        assertFalse(result.isCustomConfiguration());
    }

    @Test
    void testGetEmailConfiguration_WithValidAgencyConfig_ReturnsAgencyConfig() {
        // Given
        when(agencyEmailConfigService.findVerifiedActiveByAgencyId(123L))
                .thenReturn(Optional.of(testConfig));

        // When
        EmailSenderFactory.EmailConfiguration result = emailSenderFactory.getEmailConfiguration(123L);

        // Then
        assertNotNull(result);
        assertEquals("Test Agency", result.getFromName());
        assertEquals("<EMAIL>", result.getFromEmail());
        assertEquals("<EMAIL>", result.getSupportEmail());
        assertEquals("https://agency.com", result.getWebsiteUrl());
        assertEquals("https://agency.com/logo.png", result.getLogoUrl());
        assertTrue(result.isCustomConfiguration());
    }

    @Test
    void testGetEmailConfiguration_WithNoAgencyConfig_ReturnsDefaultConfig() {
        // Given
        when(agencyEmailConfigService.findVerifiedActiveByAgencyId(123L))
                .thenReturn(Optional.empty());

        // When
        EmailSenderFactory.EmailConfiguration result = emailSenderFactory.getEmailConfiguration(123L);

        // Then
        assertNotNull(result);
        assertEquals("Default Company", result.getFromName());
        assertFalse(result.isCustomConfiguration());
    }

    @Test
    void testHasCustomEmailConfiguration_WithValidConfig_ReturnsTrue() {
        // Given
        when(agencyEmailConfigService.hasVerifiedActiveConfiguration(123L)).thenReturn(true);

        // When
        boolean result = emailSenderFactory.hasCustomEmailConfiguration(123L);

        // Then
        assertTrue(result);
        verify(agencyEmailConfigService).hasVerifiedActiveConfiguration(123L);
    }

    @Test
    void testHasCustomEmailConfiguration_WithNoConfig_ReturnsFalse() {
        // Given
        when(agencyEmailConfigService.hasVerifiedActiveConfiguration(123L)).thenReturn(false);

        // When
        boolean result = emailSenderFactory.hasCustomEmailConfiguration(123L);

        // Then
        assertFalse(result);
        verify(agencyEmailConfigService).hasVerifiedActiveConfiguration(123L);
    }

    @Test
    void testHasCustomEmailConfiguration_WithNullAgencyId_ReturnsFalse() {
        // When
        boolean result = emailSenderFactory.hasCustomEmailConfiguration(null);

        // Then
        assertFalse(result);
        verify(agencyEmailConfigService, never()).hasVerifiedActiveConfiguration(anyLong());
    }



    @Test
    void testEmailConfigurationBuilder() {
        // Test the EmailConfiguration builder pattern
        EmailSenderFactory.EmailConfiguration config = EmailSenderFactory.EmailConfiguration.builder()
                .fromEmail("<EMAIL>")
                .fromName("Test Company")
                .replyToEmail("<EMAIL>")
                .supportEmail("<EMAIL>")
                .websiteUrl("https://example.com")
                .logoUrl("https://example.com/logo.png")
                .isCustomConfiguration(true)
                .build();

        assertEquals("<EMAIL>", config.getFromEmail());
        assertEquals("Test Company", config.getFromName());
        assertEquals("<EMAIL>", config.getReplyToEmail());
        assertEquals("<EMAIL>", config.getSupportEmail());
        assertEquals("https://example.com", config.getWebsiteUrl());
        assertEquals("https://example.com/logo.png", config.getLogoUrl());
        assertTrue(config.isCustomConfiguration());
    }
}
