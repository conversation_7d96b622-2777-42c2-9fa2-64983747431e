package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.shift.ShiftExpenseClaimDto;
import com.cap10mycap10.worklinkservice.model.ShiftExpenseClaim;

import java.util.List;

public interface ShiftExpenseClaimService {
    
    void addShiftExpenseClaim(ShiftExpenseClaimDto expenseRateDto);

    List<ShiftExpenseClaim> findShiftsClaims(List<Long> shiftIds);

    ShiftExpenseClaim save(ShiftExpenseClaimDto agencyExpenseRateDto);

    ShiftExpenseClaim getOne(Long id);
}
