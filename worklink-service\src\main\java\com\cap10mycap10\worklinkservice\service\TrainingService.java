package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.training.TrainingCreateDto;
import com.cap10mycap10.worklinkservice.dto.training.TrainingUpdateDto;
import com.cap10mycap10.worklinkservice.model.Training;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Optional;

public interface TrainingService {

    void toggleAgencyTraining(TrainingCreateDto trainingCreateDto);



    void deleteTraining(Long id);

    Training findById(Long id);

    List<Training> findAll();

    Page<Training> findAllPaged(PageRequest of);

    Training save(TrainingUpdateDto trainingUpdateDto);
    void toggleAgencyTraining(Long agencyId, Long trainingId);

    Training getOne(Long id);
    Optional<Training> findByIHascoId(Long id);

    List<Training> getForAgency(Long agencyId);
}
