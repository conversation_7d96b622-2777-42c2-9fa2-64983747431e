package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.payment.IPayment;
import com.cap10mycap10.worklinkservice.model.Payment;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Set;

public interface PaymentRepository extends JpaRepository<Payment, Long> {
//    List<IPayment> findAllByInvoiceId(Long invoiceId);
    Set<Payment> findAllByInvoiceId(Long invoiceId);


}
