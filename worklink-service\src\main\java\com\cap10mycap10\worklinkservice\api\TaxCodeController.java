package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.taxCode.TaxCodeDto;
import com.cap10mycap10.worklinkservice.dto.taxCode.TaxCodeUpdateDto;
import com.cap10mycap10.worklinkservice.model.TaxCode;
import com.cap10mycap10.worklinkservice.service.TaxCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class TaxCodeController {

    private final TaxCodeService taxCodeService;

    public TaxCodeController(TaxCodeService taxCodeService) {
        this.taxCodeService = taxCodeService;
    }

    @PostMapping(value = "taxcode", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity create(@RequestBody TaxCodeDto taxCodeDto) {
        log.info("Request to add service with : {}", taxCodeDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        taxCodeService.addTaxCode(taxCodeDto);
        return ResponseEntity.created(uri).build();
    }

    /* @ViewServices*/
    @GetMapping(value = "taxcode/{id}")
    public ResponseEntity<TaxCode> findById(@PathVariable("id") Long id) {
        log.info("Request to get service with id : {}", id);
        return ResponseEntity.ok(taxCodeService.findById(id));
    }

    /*@ViewServices*/
    @GetMapping(value = "taxcodes")
    public ResponseEntity<List<TaxCode>> findById() {
        log.info("Request to get all services");
        return ResponseEntity.ok(taxCodeService.findAll());
    }

    /* @ViewServices*/
    @GetMapping(value = "taxCode/{page}/{size}")
    public ResponseEntity<Page<TaxCode>> findById(@PathVariable("page") int page,
                                                  @PathVariable("size") int size) {
        log.info("Request to get paged services : {}, {}", page, size);
        return ResponseEntity.ok(taxCodeService.findAllPaged(PageRequest.of(page, size)));
    }

    /* @UpdateServices*/
    @PutMapping(value = "taxcode", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TaxCode> update(@RequestBody TaxCodeUpdateDto taxCodeUpdateDto) {
        log.info("Request to update service with : {}", taxCodeUpdateDto);
        taxCodeService.save(taxCodeUpdateDto);
        return ResponseEntity.ok().build();
    }

    /*@DeleteServices*/
    @DeleteMapping(value = "taxcode/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        taxCodeService.deleteTaxCode(id);
        return ResponseEntity.noContent().build();
    }

}

