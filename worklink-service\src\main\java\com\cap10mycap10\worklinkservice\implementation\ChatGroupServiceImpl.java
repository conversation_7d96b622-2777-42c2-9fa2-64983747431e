package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.ChatGroupMessageRepository;
import com.cap10mycap10.worklinkservice.dao.ChatGroupRepository;
import com.cap10mycap10.worklinkservice.dao.ShiftRepository;
import com.cap10mycap10.worklinkservice.dto.chatgroup.CreateGroupRequest;
import com.cap10mycap10.worklinkservice.dto.chatgroupmessage.ChatGroupMessageResponseDto;
import com.cap10mycap10.worklinkservice.enums.MessageType;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.mapper.chatgroupmessage.ChatGroupMessageToChatGroupMessageResponseDto;
import com.cap10mycap10.worklinkservice.model.ChatGroup;
import com.cap10mycap10.worklinkservice.model.ChatGroupMessage;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.ChatGroupMessageService;
import com.cap10mycap10.worklinkservice.service.ChatGroupService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ChatGroupServiceImpl implements ChatGroupService {

    private final WorkerService workerService;

    private final ChatGroupRepository chatGroupRepository;

    private final ChatGroupMessageRepository chatGroupMessageRepository; 

    private final SimpMessagingTemplate messagingTemplate;

    private final ShiftRepository shiftRepository;

    private final ChatGroupMessageToChatGroupMessageResponseDto toChatGroupMessageResponseDto;

    @Override
    public ChatGroup createChatGroup(CreateGroupRequest request) {
        ChatGroup chatGroup = new ChatGroup();
        chatGroup.setGroupName(request.getGroupName());
        chatGroup.setChatGroupMembers(new HashSet<>(Math.toIntExact(request.getWorkerId())));
        chatGroupRepository.save(chatGroup);
        Worker worker = workerService.getOne(request.getWorkerId());

        ChatGroupMessage chatGroupMessage = new ChatGroupMessage();
        chatGroupMessage.setChatGroup(chatGroup);
        chatGroupMessage.setMessageType(MessageType.JOIN);
        chatGroupMessage.setSender(worker);
        chatGroupMessage.setMessageSentAt(LocalDateTime.now());
        chatGroupMessage.setContent(worker.getFirstname() + " " + worker.getLastname() + " Created the group:: "+ chatGroup.getGroupName() + " on " + LocalDateTime.now());
        chatGroupMessageRepository.save(chatGroupMessage);

        return chatGroup;
    }

    @Override
    public ChatGroup getChatGroupById(Long chatGroupId) {
        return  chatGroupRepository.findById(chatGroupId).orElseThrow(
                ()-> new RecordNotFoundException("Chat Group with id: " + chatGroupId + " not found"));
    }

    @Override
    public ChatGroup createChatGroup(String groupName, List<Shift> shifts) {
        ChatGroup chatGroup = new ChatGroup();
        chatGroup.setGroupName(groupName);
        chatGroup.setShift(new ArrayList<>(shifts));
        chatGroupRepository.save(chatGroup);

        ChatGroupMessage chatGroupMessage = new ChatGroupMessage();
        chatGroupMessage.setChatGroup(chatGroup);
        chatGroupMessage.setMessageType(MessageType.GROUP_CREATE);
        chatGroupMessage.setSender(null);
        chatGroupMessage.setMessageSentAt(LocalDateTime.now());
        chatGroupMessage.setContent("System Created The Group");

        chatGroup.getMessages().add(chatGroupMessage);
        chatGroupRepository.save(chatGroup);
        chatGroupMessageRepository.save(chatGroupMessage);

        shifts.forEach(shift -> {
            shift.setShiftChatGroup(chatGroup);
            shiftRepository.save(shift);
        });

        messagingTemplate.convertAndSend("/topic/group/" + chatGroupMessage.getChatGroup().getId(), chatGroupMessage);
        return chatGroup;
    }


    @Override
    public ChatGroup joinChatGroup(Long chatGroupId, Long workerId) {
        Worker worker = workerService.getOne(workerId);
        ChatGroup chatGroup = getChatGroupById(chatGroupId);

        if(chatGroup.getChatGroupMembers().stream().anyMatch(chatGroupMember -> chatGroupMember.getId().equals(workerId)))
            throw new BusinessValidationException("Worker with id: " + workerId + " already joined the chat group");

        chatGroup.getChatGroupMembers().add(worker);
        ChatGroupMessage chatGroupMessage = new ChatGroupMessage();
        chatGroupMessage.setChatGroup(chatGroup);
        chatGroupMessage.setMessageType(MessageType.JOIN);
        chatGroupMessage.setSender(worker);
        chatGroupMessage.setMessageSentAt(LocalDateTime.now());
        chatGroupMessage.setContent(worker.getFirstname() + " JOINED " + chatGroup.getGroupName() + " on " + LocalDateTime.now() );

        chatGroupRepository.save(chatGroup);
        chatGroupMessageRepository.save(chatGroupMessage);

        messagingTemplate.convertAndSend("/topic/group/" + chatGroupMessage.getChatGroup().getId(), chatGroupMessage);
        return chatGroup;
    }

    @Override
    public ChatGroup findChatGroupByShiftID(Long shiftId) {
        Shift shift = shiftRepository.getOne(shiftId);
        return chatGroupRepository.findByShift_Id(shiftId);
    }

    @Override
    public ChatGroup findChatGroupByShiftIdAndWorkerId(Long shiftId, Long workerId) {
        workerService.getOne(workerId);
        ChatGroup chatGroup = findChatGroupByShiftID(shiftId);
        if(chatGroup == null){
            throw new BusinessValidationException("No chat group for shift "+ shiftId + " found");
        }
        return chatGroupRepository.findByChatGroupMembers_Id(workerId).stream()
                .filter(filteredChatGroup -> Objects.equals(filteredChatGroup.getId(), chatGroup.getId()))
                .findAny()
                .orElseThrow(()-> new BusinessValidationException("User not in any chatGroup"));

    }

    @Override
    public List<ChatGroupMessageResponseDto> findChatGroupMessagesByShiftIdAndWorkerId(Long shiftId, Long workerId) {
        Worker worker = workerService.getOne(workerId);
        ChatGroup chatGroup = findChatGroupByShiftID(shiftId);
        if(chatGroup == null){
            throw new BusinessValidationException("No chat group found");
        }

        chatGroupRepository.findByChatGroupMembers_Id(workerId).stream()
                .filter(filteredChatGroup -> Objects.equals(filteredChatGroup.getId(), chatGroup.getId()))
                .findAny()
                .orElseThrow(()-> new BusinessValidationException("Worker with id: " + worker.getId()+  " is not in the current chatGroup with id {}"+ chatGroup.getId()));

        return chatGroupMessageRepository.findByChatGroupId(chatGroup.getId()).stream()
                .map(toChatGroupMessageResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ChatGroupMessageResponseDto> findChatGroupMessagesByChatGroupId(Long groupId) {
        ChatGroup chatGroup = chatGroupRepository.findById(groupId)
                .orElseThrow(()-> new BusinessValidationException("No chat group found"));
        return chatGroupMessageRepository.findByChatGroupId(chatGroup.getId()).stream()
                .map(toChatGroupMessageResponseDto)
                .collect(Collectors.toList());
    }
}
