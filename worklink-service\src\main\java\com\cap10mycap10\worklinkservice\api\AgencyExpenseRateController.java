package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.agencyexpenses.AgencyExpenseRateDto;
import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.model.AgencyExpenseRate;
import com.cap10mycap10.worklinkservice.model.ExpenseRate;
import com.cap10mycap10.worklinkservice.service.AgencyExpenseRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class AgencyExpenseRateController {

    private final AgencyExpenseRateService agencyExpenseRateService;

    public AgencyExpenseRateController(AgencyExpenseRateService agencyExpenseRateService) {
        this.agencyExpenseRateService = agencyExpenseRateService;
    }


    @PostMapping(value = "agency/expense-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ExpenseRate> create(@RequestBody AgencyExpenseRateDto expenseRateDto) {
        log.info("Request to add agency rate with : {}", expenseRateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        agencyExpenseRateService.addAgencyExpenseRate(expenseRateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    @GetMapping(value = "agency/expense-rate/{agencyId}")
    public ResponseEntity<List<AgencyExpenseRate>> findAgencyExpenseRates(@PathVariable("agencyId") Long agencyId
    ) {
        log.info("Request to get agency expense rates : {} ", agencyId);
        return ResponseEntity.ok(agencyExpenseRateService.findAgencyRates(agencyId ));
    }

    /* @UpdateServices*/
    @PutMapping(value = "agency/expense-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> update(@RequestBody AgencyExpenseRateDto expenseRateUpdateDto) {
        log.info("Request to update service with : {}", expenseRateUpdateDto);
        agencyExpenseRateService.save(expenseRateUpdateDto);
        return ResponseEntity.ok().build();
    }

}
