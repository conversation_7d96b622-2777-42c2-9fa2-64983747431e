package com.cap10mycap10.worklinkservice.demo;

import com.cap10mycap10.worklinkservice.model.AgencyEmailConfiguration;
import com.cap10mycap10.worklinkservice.service.EncryptionService;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Demonstration of how SMTP password encryption works in the worklink-service.
 * This shows how passwords are automatically encrypted when stored and decrypted when retrieved.
 */
public class EncryptionDemo {

    @Test
    public void demonstratePasswordEncryption() {
        // Create encryption service
        EncryptionService encryptionService = new EncryptionService();
        ReflectionTestUtils.setField(encryptionService, "encryptionKey", "MySecretEncryptionKey123456789012");

        System.out.println("=== SMTP Password Encryption Demo ===\n");

        // Original password
        String originalPassword = "mySecretSMTPPassword123!";
        System.out.println("1. Original SMTP Password: " + originalPassword);

        // Encrypt the password (this happens automatically when saving to database)
        String encryptedPassword = encryptionService.encrypt(originalPassword);
        System.out.println("2. Encrypted Password (stored in database): " + encryptedPassword);
        System.out.println("   Length: " + encryptedPassword.length() + " characters");

        // Decrypt the password (this happens automatically when retrieving from database)
        String decryptedPassword = encryptionService.decrypt(encryptedPassword);
        System.out.println("3. Decrypted Password (for SMTP usage): " + decryptedPassword);

        // Verify they match
        System.out.println("4. Passwords match: " + originalPassword.equals(decryptedPassword));

        System.out.println("\n=== AgencyEmailConfiguration Demo ===\n");

        // Create an email configuration (in real usage, the JPA converter handles encryption)
        AgencyEmailConfiguration config = AgencyEmailConfiguration.builder()
                .agencyId(123L)
                .smtpHost("smtp.gmail.com")
                .smtpPort(587)
                .smtpUsername("<EMAIL>")
                .smtpPassword(originalPassword) // This would be encrypted by JPA converter
                .fromEmail("<EMAIL>")
                .fromName("Test Agency")
                .smtpAuth(true)
                .smtpStarttlsEnable(true)
                .isActive(true)
                .isVerified(true)
                .build();

        System.out.println("5. Email Configuration Created:");
        System.out.println("   Agency ID: " + config.getAgencyId());
        System.out.println("   SMTP Host: " + config.getSmtpHost());
        System.out.println("   SMTP Username: " + config.getSmtpUsername());
        System.out.println("   SMTP Password (raw): " + config.getSmtpPassword());
        System.out.println("   Decrypted Password: " + config.getDecryptedSmtpPassword());

        System.out.println("\n=== Security Features ===\n");

        // Show that each encryption is different (due to random IV)
        String encrypted1 = encryptionService.encrypt(originalPassword);
        String encrypted2 = encryptionService.encrypt(originalPassword);
        System.out.println("6. Same password, different encryptions (random IV):");
        System.out.println("   Encryption 1: " + encrypted1);
        System.out.println("   Encryption 2: " + encrypted2);
        System.out.println("   Are different: " + !encrypted1.equals(encrypted2));
        System.out.println("   Both decrypt correctly: " +
                          (originalPassword.equals(encryptionService.decrypt(encrypted1)) &&
                          originalPassword.equals(encryptionService.decrypt(encrypted2))));

        // Show encryption detection
        System.out.println("\n7. Encryption Detection:");
        System.out.println("   Is '" + originalPassword + "' encrypted? " + encryptionService.isEncrypted(originalPassword));
        System.out.println("   Is '" + encryptedPassword + "' encrypted? " + encryptionService.isEncrypted(encryptedPassword));

        System.out.println("\n=== API Usage for Other Services ===\n");
        System.out.println("8. Other services can call:");
        System.out.println("   GET /api/agency-email-config/agency/{agencyId}/active");
        System.out.println("   This returns the configuration with decrypted password for SMTP usage.");
        System.out.println("   The password is automatically decrypted by the JPA converter.");

        System.out.println("\n=== Demo Complete ===");
    }
}
