package com.cap10mycap10.worklinkservice.mapper.transport;

import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.transport.TransportWorkerSpecDto;
import com.cap10mycap10.worklinkservice.mapper.shift.ShiftToShiftResultDto;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.cap10mycap10.worklinkservice.model.TransportWorkerSpec;
import com.cap10mycap10.worklinkservice.service.AssignmentCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;


@Service
public class ToTransportingStaffMapper implements Function<TransportWorkerSpec, TransportWorkerSpecDto> {
    @Autowired
    private AssignmentCodeService assignmentCodeService;
    @Autowired
    private ShiftToShiftResultDto toBookingResultDto;
    @Override
    public TransportWorkerSpecDto apply(TransportWorkerSpec transportWorkerSpec) {

        TransportWorkerSpecDto transportWorkerSpecDto = new TransportWorkerSpecDto();
        transportWorkerSpecDto.setGender(transportWorkerSpec.getGender());
        transportWorkerSpecDto.setNumberOfStaff(transportWorkerSpec.getNumberOfStaff());

        AssignmentCode assignmentCode = transportWorkerSpec.getAssignmentCode();

        transportWorkerSpecDto.setAssignmentCodeName(assignmentCode.getName());
        transportWorkerSpecDto.setAssignmentCode(transportWorkerSpec.getAssignmentCode().getId());
        transportWorkerSpecDto.setId(transportWorkerSpec.getId() );

        if(nonNull(transportWorkerSpec.getBookings())&& transportWorkerSpec.getBookings().size()>0) {
            List<BookingResultDto> bookings = transportWorkerSpec
                    .getBookings()
                    .stream()
                    .map(toBookingResultDto::convert)
                    .collect(Collectors.toList());

            transportWorkerSpecDto.setBookings(bookings);
        }
        return transportWorkerSpecDto;
    }
}
