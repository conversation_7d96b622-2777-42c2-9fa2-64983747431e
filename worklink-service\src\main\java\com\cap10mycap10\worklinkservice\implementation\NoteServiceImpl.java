package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.NoteRepository;
import com.cap10mycap10.worklinkservice.dto.note.NoteCreateDto;
import com.cap10mycap10.worklinkservice.dto.note.NoteUpdateDto;
//import com.cap10mycap10.worklinkservice.dto.note.INoteResultDto;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
//import com.cap10mycap10.worklinkservice.mapper.note.NoteToNoteResultDto;
import com.cap10mycap10.worklinkservice.model.Note;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.NoteService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
@Slf4j
public class NoteServiceImpl implements NoteService {

    private final WorkerService workerService;

    private final NoteRepository noteRepository;

//    private final NoteToNoteResultDto toAnoteToNoteResultDto;

    public NoteServiceImpl(WorkerService workerService, NoteRepository noteRepository) {
        this.workerService = workerService;
        this.noteRepository = noteRepository;
    }

    @Override
    public void addNote(NoteCreateDto noteCreateDto) {
        Note note = new Note();

        note.setNote(noteCreateDto.getNote());
        note.setWorker(workerService.getOne(noteCreateDto.getWorkerId()));
        note.setAgency(workerService.getOne(noteCreateDto.getAgencyId()));

        noteRepository.save(note);
    }

    @Override
    public void deleteNote(Long id) {

        Note note = noteRepository.findById(id).orElseThrow(() -> new RecordNotFoundException("Note not found"));


            noteRepository.delete(note);
            noteRepository.flush();


    }

    @Override
    public Note findById(Long id) {
        return null;
    }

    @Override
    public Set<Note> findWorkerNotes(Long workerId, PageRequest of) {
        Worker worker = workerService.getOne(workerId);

        return worker.getNotes();

    }

    @Override
    public Page<Note> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public Note save(NoteUpdateDto noteUpdateDto) {
        return null;
    }

    @Override
    public Note getOne(Long id) {
        return null;
    }
}
