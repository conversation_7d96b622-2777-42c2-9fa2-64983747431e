package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.VatRateRepository;
import com.cap10mycap10.worklinkservice.dto.billing.VatRateCreateDto;
import com.cap10mycap10.worklinkservice.dto.billing.VatRateDto;
import com.cap10mycap10.worklinkservice.model.VatRate;
import com.cap10mycap10.worklinkservice.service.VatRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import java.util.List;

@Service
@Slf4j
public class VatRateServiceImpl implements VatRateService {

    private final VatRateRepository vatRateRepository;

    public VatRateServiceImpl(VatRateRepository vatRateRepository) {
        this.vatRateRepository = vatRateRepository;
    }


    @Override
    public void save(VatRateCreateDto vatRateDto) {
        VatRate vatRate = new VatRate();
        vatRate.setVatRate(vatRateDto.getVatRate());
        vatRateRepository.save(vatRate);
    }

    @Override
    public void update(VatRateDto vatRateDto) {
        VatRate vatRate = getOne(vatRateDto.getId());
        vatRate.setVatRate(vatRateDto.getVatRate());
        vatRateRepository.save(vatRate);
    }

    @Override
    public VatRate findById(Long id) {
        return getOne(id);
    }

    @Override
    public List<VatRate> findAll() {
        return vatRateRepository.findAll();
    }

    @Override
    public Page<VatRate> findAllPaged(PageRequest of) {
        return vatRateRepository.findAll(of);
    }

    @Override
    public void deleteById(Long id) {
        VatRate vatRate = getOne(id);
        vatRateRepository.delete(vatRate);
    }

    @Override
    public VatRate getOne(Long id) {
        return vatRateRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Charge rate does not exist"));
    }
}
