package com.cap10mycap10.worklinkservice.search;


import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
import com.cap10mycap10.worklinkservice.model.Agency;
import org.hibernate.search.mapper.orm.Search;
import org.hibernate.search.mapper.orm.session.SearchSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.NoResultException;
import java.util.List;

@Service
public class AgencySearchService {


    private final EntityManager entityManager;

    @Autowired
    private AgencyToAgencyResultDto toAgencyResultDto;


    @Autowired
    public AgencySearchService(final EntityManagerFactory entityManagerFactory) {
        super();
        this.entityManager = entityManagerFactory.createEntityManager();

    }


    public void initializeHibernateSearch() {
        // TODO: Implement Hibernate Search 6.x initialization
        // Temporarily disabled for compilation
    }

    @Transactional
    public Page<AgencyResultDto> fuzzySearch(String searchTerm, int page, int size) {
        // TODO: Implement Hibernate Search 6.x fuzzy search
        // Temporarily returning empty page for compilation
        return new PageImpl<>(List.of(), PageRequest.of(page, size), 0);
    }
}
