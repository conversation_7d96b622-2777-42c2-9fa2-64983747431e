package com.cap10mycap10.worklinkservice.dao;

public interface WorkerAgencyRepository /*extends JpaRepository<WorkerAgency, WorkerAgencyId>*/ {

   /* List<WorkerAgency> findWorkerAgenciesByAgency_Id(Long agencyId);

    @Query(value = "select worker_email from worker_agency where agency_id=?1", nativeQuery = true)
    List<String> findEmailsByAgency_Id(Long agencyId);

    @Query(value = "select agency_id from worker_agency where worker_id=?1", nativeQuery = true)
    List<Long> findAgencyIds(Long workerId);

    int countWorkerAgenciesByAgency_Id(Long id);

    int countWorkerAgencyByWorker_Id(Long id);*/
}
