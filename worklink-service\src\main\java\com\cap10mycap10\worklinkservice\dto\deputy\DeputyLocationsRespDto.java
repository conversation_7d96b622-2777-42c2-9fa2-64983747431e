package com.cap10mycap10.worklinkservice.dto.deputy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeputyLocationsRespDto {

    private Integer id;
//    private String Portfolio;
    private String code;
    private boolean active;
//    private String ParentCompany;
    private String companyName;
    private String tradingName;
    private String businessNumber;
//    private String CompanyNumber;
//    private String IsWorkplace;
//    private String IsPayrollEntity;
//    private String PayrollExportCode;
//    private String Address;
//    private String Contact;
//    private String Creator;
    private LocalDateTime created;
    private LocalDateTime modified;
}


