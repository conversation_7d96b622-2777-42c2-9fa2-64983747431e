package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.permissions.promocode.UpdatePromoCode;
import com.cap10mycap10.worklinkservice.permissions.promocode.ViewPromoCode;
import com.cap10mycap10.worklinkservice.service.PromotionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.util.Set;

import static com.cap10mycap10.worklinkservice.config.AppConfiguration.isAdmin;
import static java.util.Objects.nonNull;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class PromoCodeController {
    @Autowired
    private PromotionService promotionService;

    @UpdatePromoCode
    @PostMapping(value = "promo-code", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PromotionDto> create(@Valid @RequestBody PromotionDto promotionDto) {
        log.info("Request to add promo code with : {}", promotionDto);
        if(isAdmin()){
            promotionDto.setForAdmin();
        }

        if (!isAdmin() && nonNull(promotionDto.getAgency()) && promotionDto.getAgency().getAgencyId() == null) {
            throw new IllegalArgumentException("Agency must be specified for non-admin users");
        }
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(promotionService.save(promotionDto));
    }

    @UpdatePromoCode
    @PutMapping(value = "promo-code", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PromotionDto> update(@Valid @RequestBody PromotionDto promotionDto) {
        log.info("Request to update promo code with : {}", promotionDto);
        if(isAdmin()){
            promotionDto.setForAdmin();
        }

        if (!isAdmin() && nonNull(promotionDto.getAgency()) && promotionDto.getAgency().getAgencyId() == null) {
            throw new IllegalArgumentException("Agency must be specified for non-admin users");
        }
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri).body(promotionService.save(promotionDto));
    }

    @UpdatePromoCode
    @PutMapping(value = "promo-code/activate/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> activate(@NotNull @PathVariable("id") Long id) {
        log.info("Request to activate promo code: {}", id);
        promotionService.activate(id);
        return ResponseEntity.ok().build();
    }
    @ViewPromoCode
    @GetMapping(value = "promo-code/{id}")
    public ResponseEntity<PromotionDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get promo code with id : {}", id);
        return ResponseEntity.ok(promotionService.findById(id));
    }



    @ViewPromoCode
    @GetMapping(value = "promo-code")
    public ResponseEntity<Page<PromotionDto>> findAll(@RequestParam(value = "agencyId", required = false) Long agencyId,
                                                      @RequestParam(value = "statuses", required = false) Set<Status> statuses,
                                                      @RequestParam(value = "query", required = false) String query,
                                                      @RequestParam(value = "page", defaultValue = "0") Integer page,
                                                      @RequestParam(value = "size", defaultValue = "25") Integer size,
                                                      @RequestParam(value = "sort", defaultValue = "createdDate") String sort,
                                                      @RequestParam(value = "direction", defaultValue = "DESC") String direction) {
        log.info("Request to get agency promo code with id : {}", agencyId);

        if (!isAdmin() && agencyId == null) {
            throw new IllegalArgumentException("Agency must be specified for non-admin users");
        }

        PageRequest pageRequest = PageRequest.of(page, size, Sort.Direction.fromString(direction), sort);
        return ResponseEntity.ok(promotionService.findAll(agencyId, statuses, query, pageRequest));
    }







    @UpdatePromoCode
    @DeleteMapping(value = "promo-code/{id}")
    public ResponseEntity<Object> delete(@PathVariable("id") Long id) {
        log.info("Request to cancel promo code with id : {}", id);
        promotionService.cancel(id);
        return ResponseEntity.noContent().build();
    }

}
