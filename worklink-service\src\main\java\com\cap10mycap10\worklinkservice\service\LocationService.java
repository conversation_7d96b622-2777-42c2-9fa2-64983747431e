package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationCreateDto;
import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationResultDto;
import com.cap10mycap10.worklinkservice.dto.shiftlocation.ShiftLocationUpdateDto;
import com.cap10mycap10.worklinkservice.model.Location;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface LocationService {

    ShiftLocationResultDto save(ShiftLocationCreateDto shiftLocationCreateDto);

    ShiftLocationResultDto findById(Long id);

    List<ShiftLocationResultDto> findAll(Long agencyId);

    Page<ShiftLocationResultDto> findAllPaged(PageRequest of);

    List<ShiftLocationResultDto> searchAllColumns(String of, Long agencyId);

    void deleteById(Long id);

    ShiftLocationResultDto save(ShiftLocationUpdateDto shiftLocationUpdateDto);


    Location getOne(Long shiftLocationId);

}
