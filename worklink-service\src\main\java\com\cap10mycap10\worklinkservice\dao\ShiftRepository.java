package com.cap10mycap10.worklinkservice.dao;


import com.cap10mycap10.worklinkservice.dto.shift.IShiftCompliance;
import com.cap10mycap10.worklinkservice.dto.shift.IShiftReportStatus;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.model.Worker;
import feign.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ShiftRepository extends JpaRepository<Shift, Long> {



    @Query(value = "select * from shift where agency_id =?1\n" +
            "   and status = ?2  and (is_admin_billed = ?3 or is_admin_billed IS NULL) order by client_id asc", nativeQuery = true)
    List<Shift> findAllByAgencyIdAndStatusAndIsAdminBilled(Long agencyId, String status,Boolean isAdminBilled);


    @Query(value = "select * from shift where date(start) between ?1 and ?2 and datediff(start, CURDATE()) >= 0 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findAllShifts(LocalDateTime startDate, LocalDateTime endDate, PageRequest of);

    @Query(value = "select * from shift where id = ?1 and worker_id = ?2", nativeQuery = true)
    Optional<Shift> findShiftByIdAndWorkerId(Long shiftid, Long workerId);

    @Query(value = "select id from shift where agency_id = ?1 and is_admin_billed = false", nativeQuery = true)
    List<Long> findByAgencyIdAndNotAdminBilled(Long agencyId);

    @Query(value = "select count(*) from shift where client_id =?1 and status = 'WAITING'", nativeQuery = true)
    int findNumberOfShiftsBookedAndClientId(Long id);


    @Query(value = "select count(*) from shift", nativeQuery = true)
    int findNumberOfShifts();

    @Query(value = "select count(*) from shift where client_id =?1 and status = 'NEW'", nativeQuery = true)
    int findNumberOfShiftsByClient(Long clientId);

    @Override
    @Query(value = "select * from shift order by datediff(start,CURDATE()) asc", nativeQuery = true)
    List<Shift> findAll();

    @Query(value = "select agency_id from shift_agency where shift_id =?1", nativeQuery = true)
    List<Long> getShiftAgencies(Long shiftId);


    @Query(value = "select\n" +
            "                  count(case\n" +
            "                           when status ='NEW' then 1\n" +
            "                             else null end) as newShift ,\n" +
            "                   count(case\n" +
            "                             when status ='CANCELLED' then 1\n" +
            "                             else null end) as cancelled,\n" +
            "                   count(case\n" +
            "                             when status ='IN_QUERY' then 1\n" +
            "                             else null end) as queried,\n" +
            "                   count(case\n" +
            "                            when status = 'WAITING' then 1\n" +
            "                            else null end) as booked,\n" +
            "                   count(case\n" +
            "                             when status ='AUTHORIZED' then 1\n" +
            "                             else null end) as authorized\n" +
            "\n" +
            "            from shift;", nativeQuery = true)
     IShiftReportStatus countShiftsByStatus();

    @Query(value = "select * from shift where status=?1 and date(start) between ?3 and ?4  and start>=current_date  order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findAllPaged(String status, PageRequest of, LocalDateTime startDate, LocalDateTime endDate);

    @Query(value = "select * from shift where client_id=?1 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findAllPagedClients(Long clientId, PageRequest of);

    @Query(value = "select * from shift where status=?1 and client_id = ?2 and date(start) between ?3 and ?4 and start>= current_date order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findAllPagedClient(String status, Long clientId, PageRequest of, LocalDateTime startDate, LocalDateTime endDate);

    @Query(value = "select * from shift where id in (\n" +
            "    select shift_id from shift_agency where agency_id =?1\n" +
            "    )  date(start) between ?2 and ?3 and start>=current_date order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findShiftByAgencyId(Long agencyId, LocalDateTime startDate, LocalDateTime endDate, Pageable of);

    @Query(value = "select * from shift where id in (\n" +
            "    select shift_id from shift_agency where agency_id =?1\n" +
            "    ) and date(start) between ?2 and ?3 and start>=current_date and status = ?4 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findShiftByAgencyId(Long agencyId, LocalDateTime startDate, LocalDateTime endDate, String status, Pageable of);

    @Query(value = "select * from shift where agency_id =?1 \n" +
            "     and status =?2 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    List<Shift> findShiftByAgencyIdAndStatus(Long agencyId, String status);
    @Query(value = "select * from shift where agency_id =?1 \n" +
            "     and status =?2 and (is_agency_billed = false or shift_worker_status is null) order by datediff(start,CURDATE()) asc", nativeQuery = true)
    List<Shift> findAgencyAuthorized(Long agencyId, String status);

    @Query(value = "select * from shift where agency_id =?1 \n" +
            "     and status =?2 and last_modified_date  >= ?3 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    List<Shift> findShiftByAgencyIdAndStatusAndDate(Long agencyId, String status, LocalDate date);

    @Query(value = "select * from shift where  \n" +
            "     status =?1 and last_modified_date  >= ?2 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findByStatusAndDate(String status, LocalDate date, Pageable pageable);

    @Query(value = "select * from shift where status=?2 and client_id = ?1 and last_modified_date  >= ?3 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findByStatusAndDateAndClientId(Long clientId, String status,LocalDate date,Pageable of);



    @Query(value = "select * from shift where status=?2 and client_id = ?1 and is_agency_billed = false  order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findClientAuthorized(Long clientId, String status,LocalDate date,Pageable of);


//    @Query(value =
//            "SELECT s.id as shiftId, wk.firstname as firstname, wk.lastname as lastname, wk.id as workerId, s.start as start, wc.compliance_expiry as complianceExpiry, wt.training_expiry as trainingExpiry FROM `shift` as s \n" +
//            "INNER JOIN worker_training as wt ON wt.worker_id = s.worker_id AND wt.training_expiry >=DATE_ADD(CURDATE(), INTERVAL 7 DAY)\n" +
//            "INNER JOIN worker_compliance as wc ON wc.worker_id = s.worker_id AND wc.compliance_expiry >=DATE_ADD(CURDATE(), INTERVAL -77 DAY)\n" +
//            "INNER JOIN worker as wk ON wk.id = s.worker_id\n" +
//            "where s.worker_id is not null \n" +
//            "and s.status in ('BOOKED', 'WAITING', 'IN_QUERY', 'AUTHORIZED', 'APPLIED', 'WAITING_APPROVAL' )\n" +
//            "and s.agency_id = ?1\n" +
//            "GROUP BY s.id\n"
//            , nativeQuery = true)
//    Page<IShiftCompliance> findShiftComplianceIssues(Long agencyId, Pageable of);


    @Query(value =
            "SELECT s.id as shiftId, wk.firstname as firstname, wk.lastname as lastname, wk.id as workerId, s.start as start , COUNT(wc.id) as totalCompliance, COUNT(wt.id) as totalTrainings FROM `shift` as s \n" +
                    "INNER JOIN worker_training as wt ON wt.worker_id = s.worker_id AND wt.training_expiry <=s.start\n" +
                    "INNER JOIN worker_compliance as wc ON wc.worker_id = s.worker_id AND wc.compliance_expiry <=s.start\n" +
                    "INNER JOIN worker as wk ON wk.id = s.worker_id\n" +
                    "where s.worker_id is not null \n" +
                    "and s.status in ('BOOKED', 'WAITING', 'IN_QUERY', 'AUTHORIZED', 'APPLIED', 'WAITING_APPROVAL' )\n" +
                    "and s.agency_id = ?1\n" +
                    "GROUP BY s.id\n"
            , nativeQuery = true)
    Page<IShiftCompliance> findShiftComplianceIssues(Long agencyId, Pageable of);



    Page<Shift> findAllByAgencyAndReleased(Agency agent, Boolean released, Pageable pageable);

    @Query(value = "select * from shift where id in (\n" +
            "    select shift_id from shift_agency where agency_id =?1\n" +
            "    ) and status =?2 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    List<Shift> findNewShiftsByAgencyId(Long agencyId, String status);

    @Query(value = "select * from shift where status=?1 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findAllByStatus(String status, PageRequest of);
//    Page<Shift> findAllByStatus(String status, PageRequest of);

    @Query(value = "select * from shift where status=?2 and client_id = ?1 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    Page<Shift> findAllPagedClientIdStatus(Long clientId, String status,Pageable of);

    @Query(value = "select * from shift where worker_id =?1 and  status =?2 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    List<Shift> findAllByWorker_IdAndShiftStatus(Long workerId, String status);

    List<Shift> findAllByWorkerIdAndStatus(Long worker, ShiftStatus status);


    @Query(value = "select\n" +
            "                  count(case\n" +
            "                           when status ='NEW' then 1\n" +
            "                             else null end) as newShift ,\n" +
            "                   count(case\n" +
            "                             when status ='CANCELLED' then 1\n" +
            "                             else null end) as cancelled,\n" +
            "                   count(case\n" +
            "                             when status ='IN_QUERY' then 1\n" +
            "                             else null end) as queried,\n" +
            "                   count(case\n" +
            "                            when status = 'WAITING' then 1\n" +
            "                            else null end) as booked,\n" +
            "                   count(case\n" +
            "                             when status ='AUTHORIZED' then 1\n" +
            "                             else null end) as authorized\n" +
            "\n" +
            "            from shift where client_id =?1", nativeQuery = true)
    List<IShiftReportStatus> countClientShiftsByStatus(Long clientId);


    @Query(value = "select\n" +
            "                  count(case\n" +
            "                           when status ='NEW' then 1\n" +
            "                             else null end) as newShift ,\n" +
            "                   count(case\n" +
            "                             when status ='CANCELLED' then 1\n" +
            "                             else null end) as cancelled,\n" +
            "                   count(case\n" +
            "                             when status ='CANCELLED' then 1\n" +
            "                             else null end) as billed,\n" +
            "                   count(case\n" +
            "                             when status ='IN_QUERY' then 1\n" +
            "                             else null end) as queried,\n" +
            "                   count(case\n" +
            "                            when status = 'WAITING' then 1\n" +
            "                            else null end) as booked,\n" +
            "                   count(case\n" +
            "                             when status ='AUTHORIZED' then 1\n" +
            "                             else null end) as authorized\n" +
            "\n" +
            "            from shift where agency_id =?1", nativeQuery = true)
    List<IShiftReportStatus> countAgencyShiftsByStatus(Long agencyId);


    @Query(value = "select\n" +
            "                  count(case\n" +
            "                           when status ='NEW' then 1\n" +
            "                             else null end) as newShift ,\n" +
            "                   count(case\n" +
            "                             when status ='CANCELLED' then 1\n" +
            "                             else null end) as cancelled,\n" +
            "                   count(case\n" +
            "                             when status ='IN_QUERY' then 1\n" +
            "                             else null end) as queried,\n" +
            "                   count(case\n" +
            "                             when status ='BILLED' then 1\n" +
            "                             else null end) as billed,\n" +
            "                   count(case\n" +
            "                            when status = 'WAITING' then 1\n" +
            "                            else null end) as booked,\n" +
            "                   count(case\n" +
            "                             when status ='AUTHORIZED' then 1\n" +
            "                             else null end) as authorized\n" +
            "\n" +
            "            from shift where worker_id =?1", nativeQuery = true)
    List<IShiftReportStatus> countWorkerShiftsByStatus(Long workerId);


    @Query(value = "select count(*) from shift where client_id in (\n" +
            "    select client_id from approved_agency where approved_agency.agency_id =?1) and date(start) between ?3 and ?4 and start>=current_date and status = 'NEW'", nativeQuery = true)
    int findCountsShiftByAgencyId(Long agencyId, LocalDateTime startDate, LocalDateTime endDate, String status);
    @Query(value = "SELECT SUM(total_hrs)\n" +
            "FROM pay_advice\n" +
            "WHERE worker_id = ?1 and pay_advice_status = 'PAID';", nativeQuery = true)
    int findJobHrs(Long workerId);

    @Query(value = "select distinct count(client_id) from shift where worker_id =?1", nativeQuery = true)
    int countClientDistinctByWorker_Id(Long id);

    @Query(value = "select  count(*) from shift where worker_id =?1 and status = 'WAITING'", nativeQuery = true)
    int countShiftsBookedByWorkerId(Long id);

    @Query(value = "select * from shift where id in (?1)", nativeQuery = true)
    List<Shift> findByIdIn(List<Long> ids);

    @Query(value = "select *"+
            "from shift\n" +
            "where gender = ?2\n" +
            "   or gender = 'NO_PREFERENCE' and client_id in (\n" +
            "    select client_id\n" +
            "    from agency_client\n" +
            "    where agency_client.agency_id in (\n" +
            "        select agency_id\n" +
            "        from agency_worker\n" +
            "        where worker_id = ?1))\n" +
            "order by datediff(start, CURDATE()) asc", nativeQuery = true)
    Page<Shift> findWorkerShifts(Long workerId, String gender, Integer code, Pageable pageable);

    @Query(value = "select *\n"+
            "from shift\n" +
            "where status = ?2\n" +
            "  and shift_worker_status is null\n" +
            "  and worker_id = ?1\n" +
            "order by datediff(start, CURDATE()) asc", nativeQuery = true)
    List<Shift> findShiftsAuthosizedAndUnpaid(Long workerId, String status, Pageable of);


    @Query(value = "select * \n" +
            "from shift\n" +
            "where status = ?2\n" +
            "  and worker_id = ?1\n" +
            "order by datediff(start, CURDATE()) asc", nativeQuery = true)
    List<Shift> findShiftsPagedOtherStatus(Long workerId, String status, Pageable of);

    @Query(value = "select *\n" +
            "from shift\n" +
            "where shift_worker_status = ?2\n" +
            "  and worker_id = ?1\n" +
            "order by datediff(start, CURDATE()) asc", nativeQuery = true)
    List<Shift> findShiftsByWorkerStatus(Long workerId, String status, Pageable of);



    @Query(value = "select *\n" +
            "from shift\n" +
            "where worker_id = ?1\n" +
            "and agency_id = ?2" +
            "  and status in ('AUTHORIZED', 'BILLED') \n" +
            "  and released = true and shift_worker_status IS NULL \n" +
            "order by datediff(start, CURDATE()) asc", nativeQuery = true)
    List<Shift> findWorkerShiftsForBilling(Long workerId, Long agencyId,Pageable of);


    @Query(value = "select *\n" +
            "from shift\n" +
            "where " +
            "status= ?2 and gender = ?3\n" +
            "   or gender = 'NO_PREFERENCE'" +
            "" +
            " and id in (\n" +
            "    select shift_id\n" +
            "    from shift_agency\n" +
            "    where shift_agency.agency_id in (\n" +
            "        select agency_id\n" +
            "        from agency_worker_properties\n" +
            "        where status = 'APPROVED'\n" +
            "        and worker_id = ?1))\n" +
            "" +
            " and assignment_code_id in (\n" +
            "    select assignment_code_id\n" +
            "    from worker\n" +
            "    where worker.id = ?1) \n" +
            "and status = ?2\n" +
            "order by datediff(start, CURDATE()) asc", nativeQuery = true)
    List<Shift> findShiftsPagedStatus(Long workerId, String status, String gender, Integer code, Pageable of);




    Page<Shift> findAllShiftsByWorkerId(Long workerId, LocalDate startDate, LocalDate endDate, String status, PageRequest of);
    List<Shift> findAllByStatus(ShiftStatus status);


    @Query(value = "SELECT worker.id as id, worker.firstname as firstName, worker.gender as gender, worker.assignment_code_id as assignmentCodeId \n" +
            "FROM worker \n" +
            "INNER JOIN agency_worker ON worker.id = agency_worker.worker_id \n" +
            "INNER JOIN agency ON agency.id = agency_worker.agency_id \n" +
            "INNER JOIN shift_agency ON shift_agency.agency_id = agency.id \n" +
            "INNER JOIN shift ON shift_agency.shift_id = shift.id \n" +
            "WHERE shift.id = :shiftId \n" +
            "AND (shift.gender = 'NO_PREFERENCE' OR shift.gender = worker.gender) \n" +
            "AND shift.assignment_code_id = worker.assignment_code_id \n",
            nativeQuery = true)
    List<Object[]> workersEligibleForShift(@Param("shiftId") Long shiftId);


    @Query(value = "SELECT * FROM shift WHERE shift.transport_id = ?1 AND shift.status = ?2", nativeQuery = true)
    List<Shift> findByTransportAndTransportBookingStatus(Long transportId, String transportBookingStatus);
//    Shift findByWorkerAndTransport(Worker worker, Transport transport);

    @Query(value = "SELECT * FROM shift WHERE shift.worker_id = ?1 AND shift.transport_id = ?2", nativeQuery = true)
    Shift findByWorkerIdAndTransportId(Long workerId, Long transportId);

    @Query(value = "SELECT COUNT(*) FROM shift WHERE worker_id = ?2 AND transport_id = ?1", nativeQuery = true)
    Integer findByTransportIdAndWorkerId( Long transportId, Long workerId);


    @Query(value = "SELECT DISTINCT * \n" +
            "FROM shift \n" +
            "JOIN transport ON shift.transport_id = transport.id \n" +
            "JOIN agency ON transport.agency_id = agency.id \n" +
            "WHERE agency.id = ?1", nativeQuery = true)
    List<Shift> findByAgencyId(Long agencyId);
    List<Shift> findAllByWorkerAndStatus(Worker worker, ShiftStatus status);

    Page<Shift> findAllShiftsByWorkerIdAndAgencyId(Long workerId, Long agentId, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    Page<Shift> findAllShiftsByWorkerIdAndClientId(Long workerId, Long clientId, LocalDate startDate, LocalDate endDate, LocalDate startDate1, String status, PageRequest of);

    Page<Shift> findAllShiftsByWorkerIdAndClientAndAgency(Long workerId, Long agentId, Long clientId, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    Page<Shift> findAllShiftsByWorkerIdAndClientAndAgency(Long workerId, Long agentId, Long location, Long clientId, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    Page<Shift> findAllShiftsByWorkerId(Long workerId, Long location, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    Page<Shift> findAllShiftsByWorkerIdAndAgencyId(Long workerId, Long agentId, Long location, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    Page<Shift> findAllShiftsByWorkerIdAndClientId(Long workerId, Long clientId, Long location, LocalDate startDate, LocalDate endDate, LocalDate startDate1, String status, PageRequest of);

}
