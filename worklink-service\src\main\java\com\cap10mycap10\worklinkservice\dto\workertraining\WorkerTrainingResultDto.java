package com.cap10mycap10.worklinkservice.dto.workertraining;

import com.cap10mycap10.worklinkservice.dao.AgencyRepository;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.enums.TrainingType;
import com.cap10mycap10.worklinkservice.model.Agency;
import lombok.Data;
@Data
public class WorkerTrainingResultDto {
    private Long id;

    private String code;

    private String name;
    private TrainingType type;

    private String description;
    private String document;
    private Boolean uploaded;

    private String serviceId;

    private String trainingDate;

    private String trainingExpiry;

    private String workerId;
    private Long hascoCourseId;
    private Long hascoId;

    private AgencyResultDto agency;
    private WorkerResultDto worker;
    private String status;
    private String supervisor;
    private String comment;
    private Boolean showCertificate;


}
