package com.cap10mycap10.worklinkservice.feigndtos.feigndtos.request;

import lombok.Data;

import jakarta.validation.constraints.Size;


@Data
public class UserDto {

    @Size(min = 1, message = "{Size.userDto.firstName}")
    private String firstName;

    @Size(min = 1, message = "{Size.userDto.lastName}")
    private String lastName;

    @Size(min = 5, max = 50)
    private String username;

    @Size(min = 1, message = "{Size.userDto.email}")
    private String email;

    private Long agentId;

    private Long clientId;

    private Long workerId;

    private Long roleId;
    private String userType;



}