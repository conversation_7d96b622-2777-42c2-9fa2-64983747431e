package com.cap10mycap10.worklinkservice.dto.invoice;


import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.dto.payment.IPayment;
import com.cap10mycap10.worklinkservice.enums.InvoiceType;
import com.cap10mycap10.worklinkservice.model.Payment;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.persistence.Transient;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
public class InvoiceResult {

    private Long id;
    private Long agencyId;
    private InvoiceType invoiceType;
    private Long clientId;
    private Long payeeId;
    private Long workerId;
    private Long vehicleBookingId;
    private String workerName;
    private String clientName;
    private ClientDto client;
    private String agencyName;
    private AgencyResultDto agency;
    private BigDecimal totalAmount;
    private BigDecimal subTotalAmount;
    private BigDecimal discount;
    private String description;
    private String invoiceStatus;
    private BigDecimal vatAmount;
    private BigDecimal vatPercentage;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate invoiceDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dueDate;
    private Boolean published;
    private List<InvoiceItemResult> invoiceItemResult;
    private Set<Payment> paymentRef;
    private Set<Payment> payments;
    private String redirectUrl;
    private String clientSecret;
    private BigDecimal serviceCharge;
    private String serviceChargeDesc;
}
