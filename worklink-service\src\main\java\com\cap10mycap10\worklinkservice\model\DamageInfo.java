package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.DamageInfoType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.*;

@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Slf4j
@Setter
public class DamageInfo{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String area;
    private String description;
    private Double charge;


    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DamageInfoType type = DamageInfoType.OUT;
    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private VehicleBooking vehicleBooking;
}
