package com.cap10mycap10.worklinkservice.dto.client;

import com.cap10mycap10.worklinkservice.feigndtos.AdministratorCreateDto;
import com.cap10mycap10.worklinkservice.model.Address;
import com.cap10mycap10.worklinkservice.model.ClientDocs;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import java.util.HashSet;
import java.util.Set;

@Data

public class ClientDto {


    private Long id;
    @NotNull(message = "Name cannot be blank")
    private String name;
    private String sbsCode;
    private String purchaseOrder;
    private String telephone;
    private Address address;
    @NotNull(message = "Email cannot be blank")
    @Email(message = "Email should be valid")
    private String email;
    @NotNull(message = "Email cannot be blank")
    @Email(message = "Email should be valid")
    private String billingEmail;
    private String logo;
    private Integer totalBooking;
    private Float rating;
    private Boolean verified;
    private Long agencyId;
    @NotNull(message = "User details missing")
    private AdministratorCreateDto administratorCreateDto;
    private Long serviceId;
    Set<ClientDocs> clientDocs = new HashSet<>();
}
