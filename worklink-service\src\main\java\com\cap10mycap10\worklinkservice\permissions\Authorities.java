package com.cap10mycap10.worklinkservice.permissions;

public enum Authorities {

    CREATE_AGENCY,
    VIEW_AGENCY,
    UPDATE_AGENCY,
    DELETE_AGENCY,

    CREATE_ASSIGNMENT_CODE,
    VIEW_ASSIGNMENT_CODE,
    UPDATE_<PERSON>SI<PERSON><PERSON>MENT_CODE,
    DELETE_ASSIG<PERSON>MENT_CODE,

    CREATE_CLIENT,
    VIEW_CLIENT,
    UPDATE_CLIENT,
    DELETE_CLIENT,

    CREATE_SHIFT_DIRECTORATE,
    VIEW_SHIFT_DIRECTORATE,
    UPDATE_SHIFT_DIRECTORATE,
    DELETE_SHIFT_DIRECTORATE,

    CREATE_SHIFT_LOCATION,
    VIEW_SHIFT_LOCATION,
    UPDATE_SHIFT_LOCATION,
    DELETE_SHIFT_LOCATION,

    CREATE_SERVICES,
    VIEW_SERVICES,
    UPDATE_SERVICES,
    DELETE_SERVICES,

    CREATE_SHIFT,
    VIEW_SHIFT,
    UPDATE_SHIFT,
    DELETE_SHIFT,

    CREATE_SHIFT_TYPE,
    VIEW_SHIFT_TYPE,
    UPDATE_SHIFT_TYPE,
    DELETE_SHIFT_TYPE,

    CREATE_WORKER,
    VIEW_WORKER,
    UPDATE_WORKER,
    DELETE_WORKER

}
