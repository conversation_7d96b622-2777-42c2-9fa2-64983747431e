package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.enums.PayAdviceStatus;
import com.cap10mycap10.worklinkservice.model.PayAdvice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDate;
import java.util.List;

public interface PayAdviceRepository extends JpaRepository<PayAdvice, Long> {

    Page<PayAdvice> findAllByWorkerIdOrderByPayAdviceDateDesc(Long workerId, Pageable page);
    Page<PayAdvice> findAllByWorkerIdAndAgentId(Long workerId,Long agencyID, Pageable page);

    Page<PayAdvice> findAllByAgentIdAndPayAdviceStatus(Long agencyId, PayAdviceStatus payAdviceStatus, Pageable of);
    List<PayAdvice> findAllByAgentIdAndPayAdviceStatus(Long agencyId, PayAdviceStatus payAdviceStatus);
    List<PayAdvice> findAllByAgentIdAndPayAdviceDate(Long agencyId, LocalDate date);
}
