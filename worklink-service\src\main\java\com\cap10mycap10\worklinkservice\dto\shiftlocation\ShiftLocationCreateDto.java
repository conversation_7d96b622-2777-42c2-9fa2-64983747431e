package com.cap10mycap10.worklinkservice.dto.shiftlocation;

import lombok.Data;

import jakarta.validation.constraints.Size;

@Data

public class ShiftLocationCreateDto {

    private String name;

    @Size(max = 32)
    private String postcode;

    private String phoneNumber;

    private Long clientId;

    private String address;
    private String postalCode;
    private String state;
    private String description;
    private String city;
    private String country;
    private String iso2;
    private String iso3;
    private String capital;
    private String adminName;
    private String timeZone;
    private String locationType;
    private Long agencyId; // Optional field for agency ownership
}
