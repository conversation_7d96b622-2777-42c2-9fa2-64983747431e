package com.cap10mycap10.worklinkservice.dto.worker;

import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data

public class WorkerUpdateDto {

    private Long id;

    private String firstname;

    private String lastname;

    private Gender gender;

    private String phoneNumber;

    private String email;

    private String username;

    private Long assignmentCodeId;


    private String employmentNumber;

    private String nationality;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dob;

    private String postcode;

    private String address;

    private String profilePic;

    private String cv;

    private Status status;

    private Long agencyId;
}
