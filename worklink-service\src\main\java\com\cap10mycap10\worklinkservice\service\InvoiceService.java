package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.billing.InvoiceCreateDto;
import com.cap10mycap10.worklinkservice.dto.invoice.InvoiceResult;
import com.cap10mycap10.worklinkservice.model.Invoice;
import com.cap10mycap10.worklinkservice.model.SettlementStatement;
import com.cap10mycap10.worklinkservice.reports.ReportFormat;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
//import zw.co.paynow.responses.WebInitResponse;

import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

public interface InvoiceService {

    void createInvoice(Long clientId, Long agencyId, List<Long> shiftIds, List<Long> workerTrainingSessionIds, InvoiceCreateDto invoiceCreateDto) throws Exception;

    void invoiceAgencyForTrainings(Long agencyId, Long trainerId, InvoiceCreateDto invoiceCreateDto) throws Exception;

    InvoiceResult findInvoice(Long invoiceId);
    InvoiceResult forceGenerateUnbilledItems(Long agencyId) throws Exception;

    Page<InvoiceResult> getForClient(Integer page, Integer size);

    Page<InvoiceResult> getForClient(Long clientId, Integer page, Integer size);
    Page<InvoiceResult> getAllAgencyInvoicesByAdmin(Long agencyId, Integer page, Integer size);

    Page<InvoiceResult> getWorkerInvoices(Long workerId, Integer page, Integer size);

    Page<InvoiceResult> getTrainerWorkerInvoices(Long agencyId,Boolean onlyPaid, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of);

    Page<InvoiceResult> getTrainerAgencyInvoices(Long agencyId, Boolean onlyPaid, Long agencyIdFilter,  LocalDate startDate, LocalDate endDate, PageRequest of);

    Page<InvoiceResult> getAllAgencyInvoiceReportsByAdmin(Long agencyId, Integer page, Integer size);


    void acknowledgePayment(Long invoiceId, String paymentRef, BigDecimal amount);

//    WebInitResponse fullPaynowPayment(Long invoiceId);

    void setDiscount(Long invoiceId, BigDecimal paymentRef);

    void publish(Long invoiceId, Boolean sendEmail);
    void delete(Long invoiceId);
    void deleteAdminInvoice(Long invoiceId);


    Invoice findInvoiceById(Long id);

    Invoice getOne(Long id);

    Page<InvoiceResult> getPaidInvoicesDueForSettlement(Long rentalId, String status, LocalDate startDate, LocalDate endDate, Long bookingId, int page, int size);

    Page<InvoiceResult> getAllInvoicesForAgent(Long agencyId, Integer page, Integer size);
    Page<InvoiceResult> getAllInvoiceReportsForAgent(Long agencyId, Integer page, Integer size);

    Page<InvoiceResult> getAllInvoicesForAdmin(Integer page, Integer size);

    ResponseEntity<Resource> downloadInvoice(Long invoiceId, ReportFormat format, HttpServletRequest servletRequest);

    @Transactional
    void settleInvoices(Long carRentalId, Set<Long> invoiceIds);

    Page<SettlementStatement> getSettlementsByFilters(Long carRentalId,
                                                      LocalDate createdDateStart,
                                                      LocalDate createdDateEnd,
                                                      Long id,
                                                      Double amount,
                                                      String status,
                                                      int page,
                                                      int size);
}
