package com.cap10mycap10.worklinkservice.dao;


import com.cap10mycap10.worklinkservice.model.Bank;
import com.cap10mycap10.worklinkservice.model.TrainingFeedback;
import com.cap10mycap10.worklinkservice.model.WorkerTrainingSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TrainingFeedbackRepository extends JpaRepository<TrainingFeedback, Long> {
    @Query(value = "select * from training_feedback " +
            "where booking_id in " +
            "(select id from worker_training_session " +
            "where training_session_id in " +
            "(select id from training_session where id = ?1)" +
            ")", nativeQuery = true)
    List<TrainingFeedback> findBySessionId(Long sessionId);
}
