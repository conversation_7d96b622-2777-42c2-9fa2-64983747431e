package com.cap10mycap10.worklinkservice.dto;

import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleInventoryDto;
import com.cap10mycap10.worklinkservice.dto.tax.TaxRateDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify that VehicleInventoryDto can be serialized to JSON without Hibernate proxy issues.
 * This addresses the original error:
 * "No serializer found for class org.hibernate.proxy.pojo.bytebuddy.ByteBuddyInterceptor"
 */
public class VehicleInventoryDtoSerializationTest {

    @Test
    void testVehicleInventoryDtoSerialization() throws Exception {
        // Given - Create a VehicleInventoryDto with TaxRateDto (no Hibernate proxies)
        VehicleInventoryDto dto = new VehicleInventoryDto();
        dto.setId(1L);
        dto.setName("GPS Navigation");
        dto.setDescription("Premium GPS navigation system");
        dto.setPrice(25.0f);
        dto.setTaxExempt(false);
        dto.setTaxInclusive(true);
        dto.setDateInstalled(LocalDate.of(2024, 1, 1));
        dto.setVehicleId(100L);
        
        // Create TaxRateDto (not a Hibernate proxy)
        TaxRateDto taxRateDto = new TaxRateDto();
        taxRateDto.setId(1L);
        taxRateDto.setName("Standard VAT");
        taxRateDto.setPercentage(new BigDecimal("15.00"));
        taxRateDto.setDescription("Standard VAT rate");
        taxRateDto.setActive(true);
        taxRateDto.setAgencyId(100L);
        
        dto.setCustomTaxRate(taxRateDto);
        dto.setCreatedDate(LocalDateTime.of(2024, 1, 1, 10, 0));
        dto.setLastModifiedDate(LocalDateTime.of(2024, 1, 2, 15, 30));
        dto.setVersion(1);

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());

        // When - Serialize to JSON (this should not throw Hibernate proxy exceptions)
        String json = objectMapper.writeValueAsString(dto);

        // Then - Verify serialization was successful
        assertNotNull(json);
        assertTrue(json.contains("GPS Navigation"));
        assertTrue(json.contains("Standard VAT"));
        assertTrue(json.contains("\"percentage\":15.00"));
        assertTrue(json.contains("\"taxExempt\":false"));
        assertTrue(json.contains("\"taxInclusive\":true"));
        
        // Verify we can deserialize back
        VehicleInventoryDto deserializedDto = objectMapper.readValue(json, VehicleInventoryDto.class);
        assertNotNull(deserializedDto);
        assertEquals("GPS Navigation", deserializedDto.getName());
        assertEquals("Standard VAT", deserializedDto.getCustomTaxRate().getName());
        assertEquals(new BigDecimal("15.00"), deserializedDto.getCustomTaxRate().getPercentage());
    }
}
