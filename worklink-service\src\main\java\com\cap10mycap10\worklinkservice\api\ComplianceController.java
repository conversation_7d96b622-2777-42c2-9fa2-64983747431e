package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.dto.compliance.ComplianceCreateDto;
import com.cap10mycap10.worklinkservice.dto.compliance.ComplianceUpdateDto;
import com.cap10mycap10.worklinkservice.model.Compliance;
import com.cap10mycap10.worklinkservice.service.ComplianceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)

public class ComplianceController {

    private final ComplianceService complianceService;

    public ComplianceController(ComplianceService complianceService) {
        this.complianceService = complianceService;
    }

    @PostMapping(value = "compliance", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Compliance> create(@RequestBody ComplianceCreateDto complianceCreateDto) {
        log.info("Request to add compliance with : {}", complianceCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        complianceService.addCompliance(complianceCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    @GetMapping(value = "compliance/{id}")
    public ResponseEntity<Compliance> findById(@PathVariable("id") Long id) {
        log.info("Request to get compliance with id : {}", id);
        return ResponseEntity.ok(complianceService.findById(id));
    }

    /*@ViewServices*/
    @GetMapping(value = "compliance")
    public ResponseEntity<List<Compliance>> findAll() {
        log.info("Request to get all compliances");
        return ResponseEntity.ok(complianceService.findAll());
    }

    /* @ViewServices*/
    @GetMapping(value = "compliance/{page}/{size}")
    public ResponseEntity<Page<Compliance>> findAllPaged(@PathVariable("page") int page,
                                                      @PathVariable("size") int size) {
        log.info("Request to get paged compliances : {}, {}", page, size);
        return ResponseEntity.ok(complianceService.findAllPaged(PageRequest.of(page, size)));
    }

    @PutMapping(value = "compliance", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> update(@RequestBody ComplianceUpdateDto complianceUpdateDto) {
        log.info("Request to update compliance with : {}", complianceUpdateDto);
        complianceService.save(complianceUpdateDto);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping(value = "compliance/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        complianceService.deleteCompliance(id);
        return ResponseEntity.noContent().build();
    }
}
