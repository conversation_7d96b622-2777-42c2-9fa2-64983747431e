package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.expenses.ExpenseRateDto;
import com.cap10mycap10.worklinkservice.dto.expenses.ExpenseRateUpdateDto;
import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.model.ExpenseRate;
import com.cap10mycap10.worklinkservice.service.ExpenseRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ExpenseRateController {

    private final ExpenseRateService expenseRateService;

    public ExpenseRateController(ExpenseRateService expenseRateService) {
        this.expenseRateService = expenseRateService;
    }


    @PostMapping(value = "expense-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ExpenseRate> create(@RequestBody ExpenseRateDto expenseRateDto) {
        log.info("Request to add service with : {}", expenseRateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        expenseRateService.addExpenseRate(expenseRateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    /* @ViewServices*/
    @GetMapping(value = "expense-rate/{id}")
    public ResponseEntity<ExpenseRate> findById(@PathVariable("id") Long id) {
        log.info("Request to get service with id : {}", id);
        return ResponseEntity.ok(expenseRateService.findById(id));
    }

    /*@ViewServices*/
    @GetMapping(value = "expense-rate")
    public ResponseEntity<List<ExpenseRate>> findAll() {
        log.info("Request to get all services");
        return ResponseEntity.ok(expenseRateService.findAll());
    }

    /* @ViewServices*/
    @GetMapping(value = "expense-rate/{page}/{size}")
    public ResponseEntity<Page<ExpenseRate>> findExpenseRates(@PathVariable("page") int page,
                                                      @PathVariable("size") int size) {
        log.info("Request to get paged services : {}, {}", page, size);
        return ResponseEntity.ok(expenseRateService.findAllPaged(PageRequest.of(page, size)));
    }

    /* @UpdateServices*/
    @PutMapping(value = "expense-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> update(@RequestBody ExpenseRateUpdateDto expenseRateUpdateDto) {
        log.info("Request to update service with : {}", expenseRateUpdateDto);
        expenseRateService.save(expenseRateUpdateDto);
        return ResponseEntity.ok().build();
    }

    /*@DeleteServices*/
    @DeleteMapping(value = "expense-rate/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        expenseRateService.deleteExpenseRate(id);
        return ResponseEntity.noContent().build();
    }
}
