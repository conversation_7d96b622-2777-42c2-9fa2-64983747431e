package com.cap10mycap10.worklinkservice.mapper.assignment;

import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeCreateDto;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.cap10mycap10.worklinkservice.service.ServicesService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class AssignmentCodeDtoToAssignmentCode implements Converter<AssignmentCodeCreateDto, AssignmentCode> {

    private final AuthenticationFacade authenticationFacade;

    private final ServicesService service;

    public AssignmentCodeDtoToAssignmentCode(AuthenticationFacade authenticationFacade, ServicesService service) {
        this.authenticationFacade = authenticationFacade;
        this.service = service;
    }

    @Override
    public AssignmentCode convert(AssignmentCodeCreateDto assignmentCodeCreateDto) {

        AssignmentCode assignmentCode = new AssignmentCode();
        assignmentCode.setCode(assignmentCodeCreateDto.getCode());
        assignmentCode.setName(assignmentCodeCreateDto.getName());
        assignmentCode.setCreatedBy(authenticationFacade.getAuthentication().getName());
        assignmentCode.setServices(service.getOne(assignmentCodeCreateDto.getServiceId()));

        return assignmentCode;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
