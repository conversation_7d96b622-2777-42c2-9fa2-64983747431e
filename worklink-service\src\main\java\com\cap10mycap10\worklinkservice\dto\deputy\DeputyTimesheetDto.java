package com.cap10mycap10.worklinkservice.dto.deputy;


import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import lombok.Data;

import jakarta.validation.constraints.Min;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data

public class DeputyTimesheetDto {

    private Long Id;
    private Long Employee;
    private Long EmployeeHistory;
    private Long EmployeeAgreement;
    private LocalDateTime Date;
    private Long StartTime;
    private Long EndTime;
    private LocalDateTime Mealbreak;
    private float TotalTime;
    private float TotalTimeInv;
    private int Cost;
    private int Roster;
    private String EmployeeComment;
    private String SupervisorComment;
    //    private String Supervisor;
    private boolean Disputed;
    private boolean TimeApproved;
    private long TimeApprover;
    //    private boolean Discarded;
    private int ValidationFlag;
    private int OperationalUnit;
    private boolean IsInProgress;
    private boolean IsLeave;
    private Long LeaveId;
    //    private String LeaveRule;
    private boolean Invoiced;
    //    private String InvoiceComment;
    private boolean PayRuleApproved;
    //    private String Exported;
    private long StagingId;
    private boolean PayStaged;
    private long PaycycleId;
    //    private String File;
//    private String CustomFieldData;
    private boolean RealTime;
    private boolean AutoProcessed;
    private boolean AutoRounded;
    private boolean AutoTimeApproved;
    private boolean AutoPayRuleApproved;
    private long Creator;
    private LocalDateTime Created;
    private LocalDateTime Modified;
    private float OnCost;
    private LocalDateTime StartTimeLocalized;
    private LocalDateTime EndTimeLocalized;
}


