package com.cap10mycap10.worklinkservice.dto.agencyworkerproperties;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDate;

@Data
public class AgencyWorkerPropertiesCreateDto {

    private Long agencyId;

    private Long workerId;

    private String paymentMethod;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate employmentStartDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate contractEndDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate nextCheckDate;

    private String rightToWork;

    private String dbsNumber;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dbsExpiry;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate expiry;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate signDate;

    private Integer rating;

    private String restrictions;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate restrictionExpiry;



    private Boolean eligible;
    private String proof;
    private String visa;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate visaExpiry;
    private Boolean paperwork;
    private String approver;
    private String position;
    private String comment;
    private String signed;

    private String paycycle;
    private String weekHrs;
    private String rtiId;
    private String startBasis;
}
