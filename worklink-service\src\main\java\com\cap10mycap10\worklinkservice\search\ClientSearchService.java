package com.cap10mycap10.worklinkservice.search;

import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.mapper.client.ClientToClientDto;
import com.cap10mycap10.worklinkservice.model.Client;
import org.apache.lucene.search.Query;
import org.hibernate.search.mapper.orm.Search;
import org.hibernate.search.mapper.orm.session.SearchSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.NoResultException;
import java.util.List;

@Service
public class ClientSearchService {

    private final EntityManager entityManager;
    @Autowired
    private ClientToClientDto toClientDto;


    @Autowired
    public ClientSearchService(EntityManagerFactory entityManagerFactory) {
        this.entityManager = entityManagerFactory.createEntityManager();
    }

    public void initializeHibernateSearch() {
        // TODO: Implement Hibernate Search 6.x initialization
        // Temporarily disabled for compilation
    }

    @Transactional
    public Page<ClientDto> fuzzySearch(String searchTerm, int page, int size) {
        // TODO: Implement Hibernate Search 6.x fuzzy search
        // Temporarily returning empty page for compilation
        return new PageImpl<>(List.of(), PageRequest.of(page, size), 0);
    }
}
