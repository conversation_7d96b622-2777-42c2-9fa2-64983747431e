package com.cap10mycap10.worklinkservice.dto.shiftexpenseclaim;

import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.AgencyExpenseRate;
import lombok.Data;

import java.math.BigDecimal;

@Data

public class ShiftExpenseClaimResultDto {

    private Long id;
    private Long shiftId;
    private BigDecimal rate;
    private BigDecimal amount;
    private String description;
    private Status status;
    private AgencyExpenseRate agencyExpenseRate;

}