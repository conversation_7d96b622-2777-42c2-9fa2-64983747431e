package com.cap10mycap10.worklinkservice.dto.agencyworkerproperties;

public interface IAgencyWorkerProperties {
    Long getId();

    Long getAgencyId();

    Long getWorkerId();

//    List<Training> trainings = new ArrayList<>();

    String getPaymentMethod();

    String getEmploymentStartDate();

    String getContractEndDate();

    String getNextCheckDate();

    String getTrainingDate();

    String getTrainingExpiry();

    String getRightToWork();

    String getDbsNumber();

    String getDbsExpiry();

    String getExpiry();

    String getRestrictions();

    String getRestrictionExpiry();



    Boolean getEligible();
    String getProof();
    String getVisa();
    String getVisaExpiry();
    String getSignDate();
    Boolean getPaperwork();
    String getApprover();
    String getPosition();
    String getComment();
    String getSigned();
    String getStatus();


}
