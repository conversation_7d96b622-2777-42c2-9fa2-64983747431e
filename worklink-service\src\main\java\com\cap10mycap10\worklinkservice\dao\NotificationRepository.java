package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.model.Notification;
import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface NotificationRepository extends JpaRepository<Notification, Long> {
    List<Notification> findAllBySenderAgencyOrderByCreatedDateDesc(Agency agency);
    List<Notification> findAllBySenderClientOrderByIdDesc(Client client);
    List<Notification> findAllBySenderTypeOrderByIdDesc(WorklinkUserType type);
    List<Notification> findAllBySenderTypeAndSendToAllOrderByIdDesc(WorklinkUserType type, Boolean sendToAll);
    List<Notification> findAllBySenderTypeAndRecipientAssCodeOrderByIdDesc(WorklinkUserType type, AssignmentCode assCode);

}
