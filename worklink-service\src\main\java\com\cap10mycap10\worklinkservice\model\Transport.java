package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.dto.transport.AuthorizeTransportDto;
import com.cap10mycap10.worklinkservice.dto.transport.LegibleWorkersDto;
import com.cap10mycap10.worklinkservice.dto.transport.TransportWorkerSpecDto;
import com.cap10mycap10.worklinkservice.dto.transport.TransportWorkerTimesDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Entity
@Data
@Slf4j
public class Transport extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private ShiftDirectorate pickupLocationDirectorate;
    @Column(length = 20)
    private String pickupLocationContactNumber;
    @Column(length = 15)
    private String pickupPostCode;
    @Column(nullable = false, length = 50)
    private String destination;


    // Risk Descriptions
    @Column(length = 500)
    private String riskDescriptions;



    @Column(nullable = false, length = 15)
    private String destinationPostCode;
    @Column(nullable = false, length = 20)
    private String destinationContactNumber;
    @Column(nullable = false, length = 50)
    private String transportReason;
    @Column(length = 100)
    private String riskDoc;
    @Column(length = 100)
    private String patientDocumentOne;
    @Column(length = 100)
    private String patientDocumentTwo;
    @Column(length = 100)
    private String patientDocumentThree;


    // Risks
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level assaultStaff;
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level physicalAggression;
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level verballyAggressive;
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level selfNeglect;
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level selfHarm;
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level absconsionRisk;
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level genderIssues;
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level racialIssues;
    @Enumerated(EnumType.STRING)
    @Column(length = 1)
    private Level sexuallyInappropriate;


    private boolean fullyBooked;

    @Column(length = 50)
    private String otherRisks;
    @Column(length = 30)
    private String patientName;
    @Column(nullable = false)
    private Boolean isPassengerAwareOfTransport;
    @Column(nullable = false)
    private Boolean passengerRequiresRestraints;
    @Column(length = 50)
    private String otherRestraints;
    @Column(length = 50)
    private String reasonsForRestrains;
    @Column(length = 50)
    private String specialRequests;
    private Float mileage;
    @Column(length = 100)
    private String patientDocuments;


    @Column(length = 100)
    private String propertyItems;
    @Column(length = 100)
    private String medicationItems;
    @Column(length = 100)
    private Float cashHandover;
    private Boolean pDroppedOff;
    @Column(length = 50)
    private String pComment;
    @Column(length = 100)
    private String signature;

    // Patient Feedback
    private char pfCleanliness = 0;
    private char pfCourtesy = 0;
    private char pfKnowledge = 0;
    private char pfTreatment = 0;
    private char pfAdvice = 0;
    private char pfComfort = 0;
    private char pfExperience = 0;


    @Column(length = 15)
    private String mha;
    @Column(length = 100)
    private String pcaddress;
    @Column(length = 50)
    private String pcemail;
    @Column(length = 30)
    private String pcbusiness;
    @Column(length = 30)
    private String pward;
    @Column(length = 30)
    private String pname;
    @Column(nullable = false)
    private LocalDate pdob;
    @Column(length = 10)
    private String nhs;
    @Column(length = 30)
    private String diagnosis;
    @Column(length = 30)
    private String dname;
    @Column(length = 40)
    private String dbusiness;
    @Column(length = 30)
    private String dward;
    @Column(length = 50)
    private String pmeds;
    @Column(length = 30)
    private String dcontact;
    @Column(length = 50)
    private String demail;
    @Column(length = 40)
    private String medication;
    @Column(length = 40)
    private String physicalHealth;
    @Column(length = 30)
    private String infectionControl;
    @Column(length = 30)
    private String rapidTranq;
    @Column(length = 3, nullable = false)
    @Enumerated(EnumType.STRING)
    private DecisionEnum rapidStatus = DecisionEnum.NA;
    private Boolean covid;
    @Column(length = 10)
    private String bpostCode;
    @Column(length = 2)
    private Integer wardEscort;
    @Column(length = 50)
    private String offerFood;
    @Column(length = 10)
    private String allergies;
    @Column(length = 20)
    private String submittedBy;
    @Column(nullable = false, length = 50)
    private String semail;
    @Column(length = 20)
    private String sphone;
    private Boolean canOfferFood;
    @Column(length = 10)
    private String pOrderNum;
    @Column(length = 15)
    private String sbsCode;
    @Column(length = 30)
    private String bname;
    @Column(length = 40, nullable = false)
    private String baddress;
    @Column(length = 20)
    private String binvoice;
    @Column(length = 15)
    private String bphone;
    @Column(length = 50, nullable = false)
    private String bemail;
    @Column(length = 50)
    private String authority;
    private Float breakTime;
    @Column(nullable = false)
    private Boolean walk;
    @Column(length = 50)
    private String walkInfo;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 15)
    private TransportStatus transportStatus = TransportStatus.NEW;
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 14)
    private Gender passengerGender;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime dateTimeBooked;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime end;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime start;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    @Column(nullable = false)
    private LocalDateTime dateTimeRequired;




    // Team leader Entry Fields
    private LocalDateTime dropTime;
    @Column(length = 20)
    private String patientRecipient;
    @Column(length = 30)
    private String recipientContact;
    @Column(length = 15)
    private String recipientRole;
    @Column(length = 100)
    private String recipientSignature;
    @Column(length = 30)
    private String newAddress;
    @Column(length = 10)
    private String newPostCode;
    @Column(length = 15)
    private String newPhone;
    @Column(length = 50)
    private String newEmail;



    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "transport", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private Set<TransportWorkerSpec> workerSpec = new HashSet<>();
    @OneToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Rating rating;
    @ManyToOne(cascade = CascadeType.ALL)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Client client;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Agency agency;


    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Vehicle vehicle;

    @ManyToOne(cascade = CascadeType.ALL)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Invoice clientInvoice;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Worker teamLeader;


    @OneToOne(fetch = FetchType.LAZY, mappedBy = "transport", cascade = CascadeType.MERGE)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private VehicleLog vehicleLog;



    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Worker driver;
    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE })
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinTable(name = "transport_agency", joinColumns = @JoinColumn(name = "transport_id"),
            inverseJoinColumns = @JoinColumn(name = "agency_id"))
    private Set<Agency> agencies = new HashSet<>();


    public void refreshState(){
        long pending =  this.workerSpec.stream().filter(e-> !e.isFullyBooked()).count();
        boolean essentials = nonNull(this.vehicle) && nonNull(this.teamLeader) ;
        if(pending==0 && essentials &&this.transportStatus==TransportStatus.BOOKED)
            this.transportStatus = TransportStatus.PENDING;
        if((pending>0 || !essentials) && this.transportStatus==TransportStatus.PENDING)
            this.transportStatus = TransportStatus.BOOKED;
    }

    public void commitJob(){
        long pending =  this.workerSpec.stream().filter(e-> !e.isFullyBooked()).count();
        boolean essentials = nonNull(this.vehicle) && nonNull(this.teamLeader) ;
        if(pending>0)
            throw new BusinessValidationException("Cannot commit to job before all workers required are assigned");
        if(!essentials)
            throw new BusinessValidationException("Cannot commit to job before both vehicle and team leader are assigned");
        this.refreshState();
    }

    public void authorize(AuthorizeTransportDto authorizeTransportDto, boolean generateInvoice){
        if(LocalDateTime.now().isBefore(dateTimeRequired))
            throw new BusinessValidationException("You cannot authorize a transport job before it has started");
        this.start = authorizeTransportDto.getStart();
        this.end = authorizeTransportDto.getEnd();
        this.breakTime = authorizeTransportDto.getBreakTime();
        this.transportStatus = TransportStatus.AUTHORIZED;
//        this.workerSpec.forEach(w->w.authorize( start, end));

        if(generateInvoice) invoiceJob();
    }

    private void invoiceJob(){
        clientInvoice = new Invoice(this);
    }

    public void startJob() {
        if(this.dateTimeRequired.isAfter(LocalDateTime.now())){
            log.error("Attempt to begin job before required start time. Job Details: {}", this);
            return;
        }

        this.transportStatus = TransportStatus.WAITING;
        this.workerSpec.forEach(TransportWorkerSpec::startJob);

    }

    public void startJobByForce() {
        this.transportStatus = TransportStatus.WAITING;
        this.workerSpec.forEach(TransportWorkerSpec::startJobByForce);
    }

    public List<Worker> getAllWorkers() {
        return this.getWorkerSpec().stream().flatMap(
                ws->ws.getBookings().stream().map(Shift::getWorker)
        ).collect(Collectors.toList());
    }

    public List<Worker> getWorkersToNotify() {
        return this.getWorkerSpec().stream().flatMap(
                ws->ws.getBookings().stream().filter(Shift::isBooked).filter(tb->!tb.getWorkerNotified()).map(Shift::getWorker)
        ).collect(Collectors.toList());
    }

    public List<LegibleWorkersDto> sortWorkers(Set<Worker> workers) {
        List<LegibleWorkersDto> legibleWorkers = new ArrayList<>();
        this.workerSpec.forEach(e->{
            LegibleWorkersDto legibleWorkersDto = new LegibleWorkersDto(e.getId());

            workers.forEach(wk->{
                if(e.workerIsValid(wk))
                    legibleWorkersDto.getWorkers().add(new LegibleWorkersDto.LegibleWorkerDto(wk));
            });

            legibleWorkers.add(legibleWorkersDto);
        });
        return legibleWorkers;
    }

    public void expireJob() {
        this.transportStatus = TransportStatus.EXPIRED;
        this.workerSpec.forEach(TransportWorkerSpec::expireBookings);
    }

    public void setVehicle(Vehicle vehicle) {
        if(!isEditable())
            throw  new BusinessValidationException("This transport job cannot be edited anymore");
        this.vehicle = vehicle;
    }

    public void setTeamLeader(Worker teamLeader) {
        if(!isEditable())
            throw  new BusinessValidationException("This transport job cannot be edited anymore");
        this.teamLeader = teamLeader;
    }



    private boolean isEditable(){
        return this.transportStatus == TransportStatus.NEW || this.transportStatus == TransportStatus.WAITING ||
                this.transportStatus == TransportStatus.BOOKED ||this.transportStatus == TransportStatus.PENDING;
    }

    public void cancelJob() {
        this.transportStatus = TransportStatus.CANCELLED;
        this.workerSpec.forEach(TransportWorkerSpec::cancelJob);
    }

    public void updateWorkerSpec(TransportWorkerSpecDto transportWorkerSpec) {
        Optional<TransportWorkerSpec> spec = this.workerSpec.stream().filter(e -> Objects.equals(e.getAssignmentCode().getId(), transportWorkerSpec.getAssignmentCode())).findFirst();
        spec.ifPresent(value -> value.setNumberOfStaff(transportWorkerSpec.getNumberOfStaff()));
    }

    public String getRef() {
        if(nonNull(agency)&&nonNull(agency.getRefPrefix()))
            return agency.getRefPrefix() + id.toString();
        else
            return id.toString();

    }

    public void markNotifiedBookings() {
        this.workerSpec.forEach(ws->{
            ws.getBookings().forEach(bk->{
                bk.setWorkerNotified(true);
            });
        });
    }

    public boolean teamLeaderNotified() {
        for(TransportWorkerSpec ws : workerSpec){
            for(Shift bk: ws.getBookings()){
                if(bk.getWorker().getId().equals(teamLeader.getId()))
                    if(bk.getWorkerNotified())
                        return true;
            }
        }
        return false;

    }

    public int getRequiredStaff() {
        int i = 0;
        for(TransportWorkerSpec ws: this.workerSpec){
            i+=ws.getNumberOfStaff();
        }
        return i;
    }


    public void updateBookingTimes(TransportWorkerTimesDto vehicleLogDto) {
        this.workerSpec.forEach(w->{
            w.updateBookingTimes(vehicleLogDto);
        });
    }

    public Float getHoursSpent() {
        float mins = (float) this.start.until(end, ChronoUnit.MINUTES);
        return  (mins/60);
    }
}
