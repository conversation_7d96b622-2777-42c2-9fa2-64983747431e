package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AgencyExpenseRateRepository;
import com.cap10mycap10.worklinkservice.dao.ExpenseRateRepository;
import com.cap10mycap10.worklinkservice.dto.expenses.ExpenseRateDto;
import com.cap10mycap10.worklinkservice.dto.expenses.ExpenseRateUpdateDto;
import com.cap10mycap10.worklinkservice.model.ExpenseRate;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ExpenseRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import java.util.List;


@Service
@Slf4j
public class ExpenseRateServiceImpl implements ExpenseRateService {

    private final ExpenseRateRepository expenseRateRepository;
    private final AgencyService agencyService;
    private final AgencyExpenseRateRepository agencyExpenseRateRepository;

    public ExpenseRateServiceImpl(ExpenseRateRepository expenseRateRepository, AgencyService agencyService, AgencyExpenseRateRepository agencyExpenseRateRepository) {
        this.expenseRateRepository = expenseRateRepository;
        this.agencyService = agencyService;
        this.agencyExpenseRateRepository = agencyExpenseRateRepository;
    }

    @Override
    public void addExpenseRate(ExpenseRateDto expenseRateDto) {
        ExpenseRate expenseRate = new ExpenseRate();
        expenseRate.setName(expenseRateDto.getName());
        expenseRate.setUnit(expenseRateDto.getUnit());
        expenseRateRepository.save(expenseRate);
    }

    @Override
    public void deleteExpenseRate(Long id) {
        ExpenseRate expenseRate = getOne(id);
        expenseRateRepository.delete(expenseRate);
    }

    @Override
    public ExpenseRate findById(Long id) {
        return getOne(id);
    }

    @Override
    public List<ExpenseRate> findAll() {
        return expenseRateRepository.findAll();
    }

    @Override
    public Page<ExpenseRate> findAllPaged(PageRequest of) {
        return expenseRateRepository.findAll(of);
    }

    @Override
    public ExpenseRate save(ExpenseRateUpdateDto expenseRateUpdateDto) {
        ExpenseRate expenseRate = getOne(expenseRateUpdateDto.getExpenseRateId());
        expenseRate.setName(expenseRateUpdateDto.getName());
        expenseRate.setUnit(expenseRateUpdateDto.getUnit());
        expenseRateRepository.save(expenseRate);
        return expenseRate;
    }

    @Override
    public ExpenseRate getOne(Long id) {
        return expenseRateRepository.findById(id).orElseThrow(
                () -> new RecordNotFoundException("Expense rate not found")
        );
    }
}
