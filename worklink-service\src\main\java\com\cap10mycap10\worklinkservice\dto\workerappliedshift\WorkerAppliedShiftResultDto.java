package com.cap10mycap10.worklinkservice.dto.workerappliedshift;

import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import lombok.Data;

@Data

public class WorkerAppliedShiftResultDto {

    private Long id;

    private WorkerResultDto workerResultDto;

    private String agencyId;

    private String agencyName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public WorkerResultDto getWorkerResultDto() {
        return workerResultDto;
    }

    public void setWorkerResultDto(WorkerResultDto workerResultDto) {
        this.workerResultDto = workerResultDto;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }
}
