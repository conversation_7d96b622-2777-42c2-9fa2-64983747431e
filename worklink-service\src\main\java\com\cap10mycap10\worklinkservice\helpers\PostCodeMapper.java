package com.cap10mycap10.worklinkservice.helpers;

import com.google.gson.GsonBuilder;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.GeocodingApiRequest;
import com.google.maps.model.GeocodingResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;



@Slf4j
public class PostCodeMapper {
    @Value("${google.maps.api.key}")
    public String GOOGLE_MAPS_API_KEY;

    public  boolean validatePostCode(String postCode){

        GeoApiContext context = new GeoApiContext.Builder()
                .apiKey(GOOGLE_MAPS_API_KEY)
                .build();
        GeocodingApiRequest req = GeocodingApi.newRequest(context).address(postCode);

        try {
             new GsonBuilder().setPrettyPrinting().create().toJson(req.await());
        } catch (Exception e) {
            log.info("Could not convert postal code to address{}", e.getMessage());
            return false;
        }
        return true;
    }

    public  String getLatLongFromPostCode(String postCode) {
        log.info("Google Maps api key {}", GOOGLE_MAPS_API_KEY);
        GeoApiContext context = new GeoApiContext.Builder()
                .apiKey("AIzaSyDyB1QqQaF9iJLZsaoexWIB6RXtGSLZO34")
                .build();
        GeocodingApiRequest req = GeocodingApi.newRequest(context).address(postCode);

        try {
            GeocodingResult[] results = req.await();
            if (results != null && results.length > 0) {
                double latitude = results[0].geometry.location.lat;
                double longitude = results[0].geometry.location.lng;

                return "latitude " + latitude + ": longitude" + longitude;  // Return lat and long as a string
            }
        } catch (Exception e) {
            return "Could not translate post code: " + e.getMessage();
        }
        return "No results found"; // Handle case with no results
    }


}
