package com.cap10mycap10.worklinkservice.service;

public interface WorkerAgencyService {

    /*WorkerAgency save(AgencyCreateDto agencyCreateDto);

    WorkerAgency save(AgencyUpdateDto agencyUpdateDto);

    WorkerAgency findById(Long workerId, Long agencyId);

    List<WorkerAgency> findAll();

    Page<WorkerAgency> findAllPaged(PageRequest of);

    void deleteById(Long workerId, Long agencyId);

    Agency getOne(Long workerId, Long agencyId);

    Page<Worker> findAllWorkersPaged(Long workerId, PageRequest of);

    Page<Agency> findAllAgenciesPaged(Long agencyId, PageRequest of);

    List<WorkerAgency> findWorkersByAgency(Long agencyId);

    List<String> findEmailsByAgency(Long agencyId);*/
}
