//package com.cap10mycap10.worklinkservice.mapper.shifttype;
//
//import com.cap10mycap10.worklinkservice.dto.shifttype.ShiftTypeResultDto;
//import com.cap10mycap10.worklinkservice.model.ShiftType;
//import com.fasterxml.jackson.databind.JavaType;
//import com.fasterxml.jackson.databind.type.TypeFactory;
//import com.fasterxml.jackson.databind.util.Converter;
//import org.springframework.stereotype.Component;
//
//@Component
//public class ShiftTypeToShiftTypeResultDto implements Converter<ShiftType, ShiftTypeResultDto> {
//    @Override
//    public ShiftTypeResultDto convert(ShiftType shiftType) {
//        ShiftTypeResultDto resultDto = new ShiftTypeResultDto();
//        resultDto.setId(shiftType.getId());
//        resultDto.setCity(shiftType.getCity());
//        resultDto.setStatus(shiftType.getStatus().city());
//
//        resultDto.setCreatedBy(shiftType.getCreatedBy());
//        return resultDto;
//    }
//
//    @Override
//    public JavaType getInputType(TypeFactory typeFactory) {
//        return null;
//    }
//
//    @Override
//    public JavaType getOutputType(TypeFactory typeFactory) {
//        return null;
//    }
//}
