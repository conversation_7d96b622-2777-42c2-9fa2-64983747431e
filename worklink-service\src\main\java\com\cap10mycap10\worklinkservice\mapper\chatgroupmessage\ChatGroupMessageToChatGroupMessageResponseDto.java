package com.cap10mycap10.worklinkservice.mapper.chatgroupmessage;

import com.cap10mycap10.worklinkservice.dto.chatgroupmessage.ChatGroupMessageResponseDto;
import com.cap10mycap10.worklinkservice.model.ChatGroupMessage;
import org.springframework.stereotype.Service;

import java.util.function.Function;

@Service
public class ChatGroupMessageToChatGroupMessageResponseDto implements Function<ChatGroupMessage, ChatGroupMessageResponseDto> {
    @Override
    public ChatGroupMessageResponseDto apply(ChatGroupMessage chatGroupMessage) {
        ChatGroupMessageResponseDto responseDto = new ChatGroupMessageResponseDto();
        responseDto.setChatGroupMessageId(chatGroupMessage.getId());
        responseDto.setMessage(chatGroupMessage.getContent());
        responseDto.setChatGroupName(chatGroupMessage.getChatGroup().getGroupName());
        responseDto.setMessageType(chatGroupMessage.getMessageType().toString());
        if(chatGroupMessage.getSender() != null ){
            responseDto.setSender(chatGroupMessage.getSender().getFirstname() + " " + chatGroupMessage.getSender().getLastname());
        }
        responseDto.setSentAt(chatGroupMessage.getMessageSentAt());

        return responseDto;
    }

}
