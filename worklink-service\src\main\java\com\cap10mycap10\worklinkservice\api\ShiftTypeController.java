package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.model.ShiftType;
import com.cap10mycap10.worklinkservice.service.ShiftTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ShiftTypeController {

    private final ShiftTypeService shiftTypeService;

    public ShiftTypeController(final ShiftTypeService shiftTypeService) {
        this.shiftTypeService = shiftTypeService;
    }

    /*@CreateShiftType*/
    @PostMapping(value = "shift-type", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ShiftType> create(@RequestBody ShiftType shiftTypeCreateDto) {
        log.info("Request to add shift-type with : {}", shiftTypeCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(shiftTypeService.save(shiftTypeCreateDto));
    }

    /*@ViewShiftType*/
    @GetMapping(value = "shift-type/{id}")
    public ResponseEntity<ShiftType> findById(@PathVariable("id") Long id) {
        log.info("Request to view shift-type with id : {}", id);
        return ResponseEntity.ok(shiftTypeService.findById(id));
    }

    /*@ViewShiftType*/
    @GetMapping(value = "shift-types")
    public ResponseEntity<List<ShiftType>> findById() {
        log.info("Request to get all  shift-types");
        return ResponseEntity.ok(shiftTypeService.findAll());
    }

    /*@ViewShiftType*/
    @GetMapping(value = "shift-types/{page}/{size}")
    public ResponseEntity<Page<ShiftType>> findById(@PathVariable("page") int page,
                                                             @PathVariable("size") int size) {
        log.info("Request to view paged shift-types with : {}, {}", page,size);
        return ResponseEntity.ok(shiftTypeService.findAllPaged(PageRequest.of(page, size)));
    }

    /*@UpdateShiftType*/
    @PutMapping(value = "shift-type", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ShiftType> update(@RequestBody ShiftType shiftTypeUpdateDto) {
        log.info("Request to update shift-type with : {}", shiftTypeUpdateDto);
        return ResponseEntity.ok(shiftTypeService.save(shiftTypeUpdateDto));
    }


    @PutMapping(value = "shift-type-activate/{id}")
    public ResponseEntity<ShiftType> activateShiftType(@PathVariable("id") Long id) {
        log.info("Request to activate shift-type id : {}", id);
        shiftTypeService.activate(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "shift-type-deactivate/{id}")
    public ResponseEntity<ShiftType> deactivateShiftType(@PathVariable("id") Long id) {
        log.info("Request to activate shift-type id : {}", id);
        shiftTypeService.deactivate(id);
        return ResponseEntity.ok().build();
    }

/*@DeleteShiftType*/
    @DeleteMapping(value = "shift-type/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete shift-type with id : {}", id);
        shiftTypeService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
