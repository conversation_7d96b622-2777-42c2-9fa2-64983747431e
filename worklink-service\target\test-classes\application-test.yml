spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password:
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
    show-sql: false
  h2:
    console:
      enabled: true

# Disable external services for testing
eureka:
  client:
    enabled: false

# Application-specific configuration
app:
  encryption:
    key: TestEncryptionKey123456789012345

# Test environment configuration
env:
  companyName: Test Company
  email: <EMAIL>
  supportEmail: <EMAIL>
  website: https://example.com
  mailPassword: testpassword
  paynowIntegrationId: 12345
  paynowIntegrationKey: test-key
  paynowResultUrl: http://localhost:8080/test/paynow-paid
  paynowReturnUrl: http://localhost:8080/test/success
  companyLogo: https://example.com/logo.svg
  systemCurrency: $
  systemCurrency3: USD
  STRIPE_SECRET_KEY: sk_test_123
  STRIPE_PUBLIC_KEY: pk_test_123
  STRIPE_WEBHOOK_SECRET: whsec_test_123

logging:
  level:
    com.cap10mycap10: DEBUG
    org.springframework.security: DEBUG
