package com.cap10mycap10.worklinkservice.mapper.asset.admin;

import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleInventoryDto;
import com.cap10mycap10.worklinkservice.dto.tax.TaxRateDto;
import com.cap10mycap10.worklinkservice.model.TaxRate;
import com.cap10mycap10.worklinkservice.model.VehicleInventory;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

@Component
public class VehicleInventoryToVehicleInventoryDto {

    public VehicleInventoryDto convert(VehicleInventory vehicleInventory) {
        if (vehicleInventory == null) {
            return null;
        }

        VehicleInventoryDto dto = new VehicleInventoryDto();
        dto.setId(vehicleInventory.getId());
        dto.setDateInstalled(vehicleInventory.getDateInstalled());
        dto.setNextCheckDate(vehicleInventory.getNextCheckDate());
        dto.setName(vehicleInventory.getName());
        dto.setDescription(vehicleInventory.getDescription());
        dto.setPhotoUrl1(vehicleInventory.getPhotoUrl1());
        dto.setPhotoUrl2(vehicleInventory.getPhotoUrl2());
        dto.setPhotoUrl3(vehicleInventory.getPhotoUrl3());
        dto.setPhotoUrl4(vehicleInventory.getPhotoUrl4());
        dto.setPrice(vehicleInventory.getPrice());
        
        // Tax-related fields
        dto.setTaxExempt(vehicleInventory.getTaxExempt());
        dto.setTaxInclusive(vehicleInventory.getTaxInclusive());
        
        // Convert TaxRate to TaxRateDto to avoid Hibernate proxy issues
        if (vehicleInventory.getCustomTaxRate() != null) {
            dto.setCustomTaxRate(convertTaxRateToDto(vehicleInventory.getCustomTaxRate()));
        }
        
        // Set vehicleId if vehicle is present
        if (vehicleInventory.getVehicle() != null) {
            dto.setVehicleId(vehicleInventory.getVehicle().getId());
        }
        
        // Audit fields - convert Instant to LocalDateTime for createdDate, keep LocalDateTime for lastModifiedDate
        if (vehicleInventory.getCreatedDate() != null) {
            dto.setCreatedDate(vehicleInventory.getCreatedDate().atZone(java.time.ZoneId.of("UTC")).toLocalDateTime());
        }
        if (vehicleInventory.getLastModifiedDate() != null) {
            dto.setLastModifiedDate(vehicleInventory.getLastModifiedDate());
        }
        if (vehicleInventory.getVersion() != null) {
            dto.setVersion(vehicleInventory.getVersion().intValue());
        }
        
        return dto;
    }

    public Set<VehicleInventoryDto> convertSet(Set<VehicleInventory> vehicleInventories) {
        if (vehicleInventories == null) {
            return null;
        }
        
        return vehicleInventories.stream()
                .map(this::convert)
                .collect(Collectors.toSet());
    }

    private TaxRateDto convertTaxRateToDto(TaxRate taxRate) {
        if (taxRate == null) {
            return null;
        }

        TaxRateDto dto = new TaxRateDto();
        dto.setId(taxRate.getId());
        dto.setName(taxRate.getName());
        dto.setPercentage(taxRate.getPercentage());
        dto.setDescription(taxRate.getDescription());
        dto.setActive(taxRate.isActive());
        dto.setAgencyId(taxRate.getAgencyId());
        
        return dto;
    }
}
