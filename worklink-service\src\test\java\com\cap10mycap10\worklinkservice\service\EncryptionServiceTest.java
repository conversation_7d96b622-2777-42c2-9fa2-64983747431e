package com.cap10mycap10.worklinkservice.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for EncryptionService.
 */
class EncryptionServiceTest {

    private EncryptionService encryptionService;

    @BeforeEach
    void setUp() {
        encryptionService = new EncryptionService();
        // Set a test encryption key
        ReflectionTestUtils.setField(encryptionService, "encryptionKey", "TestEncryptionKey123456789012");
    }

    @Test
    void testEncryptAndDecrypt() {
        // Given
        String plaintext = "mySecretPassword123";

        // When
        String encrypted = encryptionService.encrypt(plaintext);
        String decrypted = encryptionService.decrypt(encrypted);

        // Then
        assertNotNull(encrypted);
        assertNotEquals(plaintext, encrypted);
        assertEquals(plaintext, decrypted);
    }

    @Test
    void testEncryptNullValue() {
        // When
        String encrypted = encryptionService.encrypt(null);

        // Then
        assertNull(encrypted);
    }

    @Test
    void testEncryptEmptyString() {
        // When
        String encrypted = encryptionService.encrypt("");

        // Then
        assertEquals("", encrypted);
    }

    @Test
    void testEncryptWhitespaceString() {
        // When
        String encrypted = encryptionService.encrypt("   ");

        // Then
        assertEquals("   ", encrypted);
    }

    @Test
    void testDecryptNullValue() {
        // When
        String decrypted = encryptionService.decrypt(null);

        // Then
        assertNull(decrypted);
    }

    @Test
    void testDecryptEmptyString() {
        // When
        String decrypted = encryptionService.decrypt("");

        // Then
        assertEquals("", decrypted);
    }

    @Test
    void testIsEncrypted() {
        // Given
        String plaintext = "myPassword";
        String encrypted = encryptionService.encrypt(plaintext);

        // When & Then
        assertTrue(encryptionService.isEncrypted(encrypted));
        assertFalse(encryptionService.isEncrypted(plaintext));
        assertFalse(encryptionService.isEncrypted(null));
        assertFalse(encryptionService.isEncrypted(""));
        assertFalse(encryptionService.isEncrypted("short"));
    }

    @Test
    void testEncryptionIsRepeatable() {
        // Given
        String plaintext = "testPassword";

        // When
        String encrypted1 = encryptionService.encrypt(plaintext);
        String encrypted2 = encryptionService.encrypt(plaintext);

        // Then
        assertNotEquals(encrypted1, encrypted2); // Should be different due to random IV
        assertEquals(plaintext, encryptionService.decrypt(encrypted1));
        assertEquals(plaintext, encryptionService.decrypt(encrypted2));
    }

    @Test
    void testLongPassword() {
        // Given
        String longPassword = "ThisIsAVeryLongPasswordThatContainsSpecialCharacters!@#$%^&*()_+{}|:<>?[]\\;'\".,/`~1234567890";

        // When
        String encrypted = encryptionService.encrypt(longPassword);
        String decrypted = encryptionService.decrypt(encrypted);

        // Then
        assertEquals(longPassword, decrypted);
    }

    @Test
    void testSpecialCharacters() {
        // Given
        String specialChars = "!@#$%^&*()_+{}|:<>?[]\\;'\".,/`~";

        // When
        String encrypted = encryptionService.encrypt(specialChars);
        String decrypted = encryptionService.decrypt(encrypted);

        // Then
        assertEquals(specialChars, decrypted);
    }

    @Test
    void testUnicodeCharacters() {
        // Given
        String unicode = "Héllo Wörld 🌍 测试 العربية";

        // When
        String encrypted = encryptionService.encrypt(unicode);
        String decrypted = encryptionService.decrypt(encrypted); // Fix: decrypt the encrypted value, not the original

        // Then
        assertEquals(unicode, decrypted);
    }

    @Test
    void testDecryptInvalidData() {
        // Given
        String invalidEncryptedData = "invalidBase64Data!@#";

        // When & Then
        assertThrows(RuntimeException.class, () -> encryptionService.decrypt(invalidEncryptedData));
    }

    @Test
    void testGenerateRandomKey() {
        // When
        String key1 = EncryptionService.generateRandomKey();
        String key2 = EncryptionService.generateRandomKey();

        // Then
        assertNotNull(key1);
        assertNotNull(key2);
        assertNotEquals(key1, key2);
        assertTrue(key1.length() > 0);
        assertTrue(key2.length() > 0);
    }

    @Test
    void testEncryptionWithDifferentKeys() {
        // Given
        EncryptionService service1 = new EncryptionService();
        EncryptionService service2 = new EncryptionService();
        ReflectionTestUtils.setField(service1, "encryptionKey", "Key1234567890123456789012345678");
        ReflectionTestUtils.setField(service2, "encryptionKey", "DifferentKey1234567890123456789");
        
        String plaintext = "testPassword";

        // When
        String encrypted1 = service1.encrypt(plaintext);
        String encrypted2 = service2.encrypt(plaintext);

        // Then
        assertNotEquals(encrypted1, encrypted2);
        assertEquals(plaintext, service1.decrypt(encrypted1));
        assertEquals(plaintext, service2.decrypt(encrypted2));
        
        // Cross-decryption should fail
        assertThrows(RuntimeException.class, () -> service1.decrypt(encrypted2));
        assertThrows(RuntimeException.class, () -> service2.decrypt(encrypted1));
    }
}
