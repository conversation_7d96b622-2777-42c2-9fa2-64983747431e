package com.cap10mycap10.worklinkservice.mapper.worker;


import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class WorkerToWorkerResultDto implements Converter<Worker, WorkerResultDto> {

    private final AgencyToAgencyResultDto agencyToAgencyResultDto;

    public WorkerToWorkerResultDto(AgencyToAgencyResultDto agencyToAgencyResultDto) {
        this.agencyToAgencyResultDto = agencyToAgencyResultDto;
    }

    @Override
    public WorkerResultDto convert(Worker worker) {
        WorkerResultDto resultDto = new WorkerResultDto();
        resultDto.setId(worker.getId());
        resultDto.setFirstname(worker.getFirstname());
        resultDto.setLastname(worker.getLastname());
        resultDto.setEmail(worker.getEmail());
        resultDto.setAssignmentCode(worker.getAssignmentCode().getName());
        resultDto.setAssignmentCodeId(worker.getAssignmentCode().getId());
        resultDto.setGender(worker.getGender());
        resultDto.setPhoneNumber(worker.getPhoneNumber());
        resultDto.setUsername(worker.getUsername());
        resultDto.setEmploymentNumber(worker.getEmploymentNumber());
        resultDto.setNationality(worker.getNationality());
        resultDto.setCv(worker.getCv());
        resultDto.setCompliant(worker.checkCompliance());
        resultDto.setDob(worker.getDob());
        resultDto.setPostcode(worker.getPostcode());
        resultDto.setAddress(worker.getAddress());
        resultDto.setProfilePic(worker.getProfilePic());
        resultDto.setCreatedBy(worker.getCreatedBy());
        resultDto.setIHascoId(worker.getHascoId());
        //resultDto.setAgencyName(worker.getAgency().getCity());
        resultDto.setAssignmentCodeId(worker.getAssignmentCode().getId());
        resultDto.setAssignmentName(worker.getAssignmentCode().getName());
        resultDto.setAssignmentCode(worker.getAssignmentCode().getCode());
        resultDto.setStatus(worker.getStatus());
        return resultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
