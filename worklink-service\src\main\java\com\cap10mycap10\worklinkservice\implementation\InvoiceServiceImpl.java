package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.ShiftRateItem;
import com.cap10mycap10.worklinkservice.dto.billing.InvoiceCreateDto;
import com.cap10mycap10.worklinkservice.dto.invoice.DailyShiftRate;
import com.cap10mycap10.worklinkservice.dto.invoice.InvoiceResult;
import com.cap10mycap10.worklinkservice.dto.invoice.ShiftDayTime;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.InvalidRequestException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.mapper.invoice.InvoiceToInvoiceResult;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.reports.ReportFormat;
import com.cap10mycap10.worklinkservice.service.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.cap10mycap10.worklinkservice.implementation.PaginationUtil.paginateInvoice;
import static java.util.Objects.nonNull;


@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class InvoiceServiceImpl implements InvoiceService {
    @Value("${env.companyName}")
    private String companyName;
    private final ShiftService shiftService;
    
    @Autowired
    private ShiftRepository shiftRepository;

    @Autowired
    private WorkerService workerService;

    @Autowired
    private TrainingSessionService trainingSessionService;

    @Autowired
    private WorkerTrainingSessionRepository workerTrainingSessionRepository;

    @Autowired
    private TrainingSessionRepository trainingSessionRepository;
    @Autowired
    private WorkerTrainingSessionService workerTrainingSessionService;
    @Autowired
    private SettlementStatementRepository settlementStatementRepository;
    private final InvoiceRepository invoiceRepository;
    private final PaymentRepository paymentRepository;
    private final InvoiceItemRepository invoiceItemRepository;
    private final AssignmentCodeRateService assignmentCodeRateService;
    private final EmailService emailService;
    private final ClientService clientService;
    private final AgencyService agencyService;
    private final ReportService reportService;
    private final InvoiceToInvoiceResult toInvoiceResult;
    private final AgencySettingsService agencySettingsService;
    private final TaxCalculationService taxCalculationService;
    private final ChargeRateService chargeRateService;

    public InvoiceServiceImpl(ShiftService shiftService, InvoiceRepository invoiceRepository,
                              PaymentRepository paymentRepository, InvoiceItemRepository invoiceItemRepository,
                              AssignmentCodeRateService assignmentCodeRateService,
                              EmailService emailService, ClientService clientService,
                              AgencyService agencyService, ReportService reportService,
                              InvoiceToInvoiceResult toInvoiceResult,
                              AgencySettingsService agencySettingsService, TaxCalculationService taxCalculationService,
                              ChargeRateService chargeRateService) {
        this.shiftService = shiftService;
        this.invoiceRepository = invoiceRepository;
        this.paymentRepository = paymentRepository;
        this.invoiceItemRepository = invoiceItemRepository;
        this.assignmentCodeRateService = assignmentCodeRateService;
        this.emailService = emailService;
        this.clientService = clientService;
        this.agencyService = agencyService;
        this.reportService = reportService;
        this.toInvoiceResult = toInvoiceResult;
        this.agencySettingsService = agencySettingsService;
        this.taxCalculationService = taxCalculationService;
        this.chargeRateService = chargeRateService;
    }

    @Override
    @Transactional
    public void createInvoice(Long clientId, Long agentId, List<Long> shiftIds, List<Long> workerTrainingSessionIds, InvoiceCreateDto invoiceCreateDto) throws Exception {

        if(workerTrainingSessionIds==null){
            workerTrainingSessionIds = new ArrayList<>();
        }
        if(shiftIds==null){
            shiftIds = new ArrayList<>();
        }
        if(invoiceCreateDto.getInvoiceType() == null)throw new BusinessValidationException("Please specify invoice type.");


        Invoice invoice = new Invoice();

        invoice.setInvoiceType(invoiceCreateDto.getInvoiceType());
        if(invoiceCreateDto.getNotes()!=null)invoice.setNotes(invoiceCreateDto.getNotes());
        if(invoiceCreateDto.getDueDate()!=null) {
            invoice.setDueDate(invoiceCreateDto.getDueDate());
        }else{
            invoice.setDueDate(LocalDate.now().plusDays(7));
        }


        if(agentId!=null)invoice.setAgency(agencyService.getOne(agentId));
        if(clientId!=null)invoice.setClient(clientService.getOne(clientId));
        invoice.setInvoiceDate(LocalDate.now());

        List<InvoiceItem> invoiceItems = createShiftInvoiceItems(clientId,agentId, invoiceCreateDto.getInvoiceType(), shiftIds);

        if(invoiceCreateDto.getInvoiceType().equals(InvoiceType.AGENCY) && workerTrainingSessionIds!=null)invoiceItems.addAll(createTrainingInvoiceItems(agentId, workerTrainingSessionIds));
        if (invoiceItems.isEmpty()) throw new InvalidRequestException("All selected items have already been billed.");
        invoice.addInvoiceItems(invoiceItems);


        BigDecimal subtotalAmount = invoice.getInvoiceItems().stream().map(InvoiceItem::getTotal).reduce(BigDecimal::add).get();
//        invoice.setSubTotalAmount(invoice.getInvoiceItems().stream().map(InvoiceItem::getTotal).reduce(BigDecimal::add).get());


        // Apply tax calculations to invoice items
        if(invoiceCreateDto.getInvoiceType()==InvoiceType.CLIENT) {
            AgencySettings agencySettings = agencySettingsService.findAgencySettings(agentId);
            if (nonNull(agencySettings)) {
                applyTaxCalculationsToShiftInvoice(invoice, agencySettings);
                log.info("Applied tax calculations to invoice ID: {} with {} items", invoice.getId(), invoice.getInvoiceItems().size());
            }
        }

        invoiceRepository.saveAndFlush(invoice);

        if(invoiceCreateDto.getInvoiceType() == InvoiceType.CLIENT) {
            //Update Shift Status
            List<Shift> shiftsToSave = new ArrayList<>();
//            CompletableFuture.runAsync(() -> {
                for (Long idShift : shiftIds
                ) {
                    Shift shift = shiftService.getOne(idShift);
                    if (shift.getStatus().name().equalsIgnoreCase("AUTHORIZED")) {
                        shift.setIsAgencyBilled(true);
                        shiftsToSave.add(shift);
                    }
                }
                shiftRepository.saveAll(shiftsToSave);
//            });
        }else if(invoiceCreateDto.getInvoiceType() == InvoiceType.AGENCY){

            List<Shift> shiftsToSave = new ArrayList<>();
            List<WorkerTrainingSession> trainingsToSave =  new ArrayList<>();

//            CompletableFuture.runAsync(() -> {
                for (Long shiftId : shiftIds
                ) {
                    Shift shift = shiftService.getOne(shiftId);
                    if (shift.getIsAdminBilled()==null || !shift.getIsAdminBilled()  ) {
                        shift.setIsAdminBilled(true);
                        shiftsToSave.add(shift);
                    }
                }
                shiftRepository.saveAll(shiftsToSave);
//            });

//            CompletableFuture.runAsync(() -> {
                for (Long id : workerTrainingSessionIds
                ) {
                    WorkerTrainingSession workerTrainingSession = workerTrainingSessionService.getOne(id);
                    if (workerTrainingSession.getIsAdminBilled()==null || !workerTrainingSession.getIsAdminBilled()  ) {
                        workerTrainingSession.setIsAdminBilled(true);
                        trainingsToSave.add(workerTrainingSession);
                    }
                }
                workerTrainingSessionRepository.saveAll(trainingsToSave);
//            });
        }

    }

    @Override
    public void invoiceAgencyForTrainings(Long agencyId, Long trainerId, InvoiceCreateDto invoiceCreateDto) throws Exception {
        if(invoiceCreateDto.getInvoiceType() == null)throw new BusinessValidationException("Please specify invoice type.");

        List<TrainingSession> trainingSessions = trainingSessionRepository.findAllById(invoiceCreateDto.getTrainingSessionIds());
        List<Long> workerTrainingSessionIds = new ArrayList<>();
        trainingSessions.forEach(t->{
//            if(t.getIsAgencyBilled()) throw new BusinessValidationException("One of the selected sessions is already billed. Please try again");
            t.getWorkerTrainingSessions().forEach(w->{
                if(
                        w.getTrainingStatus()== WorkerTrainingSessionStatus.NEW ||
                        w.getTrainingStatus()== WorkerTrainingSessionStatus.WAITING_AUTHORIZATION ||
                        w.getTrainingStatus()== WorkerTrainingSessionStatus.BOOKED
                ){
                    throw new BusinessValidationException("Please complete authorization and approval of all bookings on selected trainings.");
                }



                if(w.getTrainingStatus()== WorkerTrainingSessionStatus.CLOSED && !w.getSkippedTraining()){
                    workerTrainingSessionIds.add(w.getId());
                }
            });
        });



        Invoice invoice = new Invoice();

        invoice.setInvoiceType(invoiceCreateDto.getInvoiceType());
        if(invoiceCreateDto.getNotes()!=null)invoice.setNotes(invoiceCreateDto.getNotes());
        if(invoiceCreateDto.getDueDate()!=null) {
            invoice.setDueDate(invoiceCreateDto.getDueDate());
        }else{
            invoice.setDueDate(LocalDate.now().plusDays(7));
        }

        invoice.setAgency(agencyService.getOne(agencyId));
        invoice.setPayeeId(trainerId);

        invoice.setInvoiceDate(LocalDate.now());


        List<InvoiceItem> invoiceItems = billAgencyAsTrainer(trainerId, workerTrainingSessionIds);
        if (invoiceItems.isEmpty()) throw new InvalidRequestException("All selected items have already been billed.");
        invoice.addInvoiceItems(invoiceItems);


        BigDecimal subtotalAmount = invoice.getInvoiceItems().stream().map(InvoiceItem::getTotal).reduce(BigDecimal::add).get();
//        invoice.setSubTotalAmount(invoice.getInvoiceItems().stream().map(InvoiceItem::getTotal).reduce(BigDecimal::add).get());


        // Apply tax calculations to invoice items
        if(invoiceCreateDto.getInvoiceType()==InvoiceType.CLIENT) {
            AgencySettings agencySettings = agencySettingsService.findAgencySettings(agencyId);
            if (nonNull(agencySettings)) {
                applyTaxCalculationsToShiftInvoice(invoice, agencySettings);
                log.info("Applied tax calculations to invoice ID: {} with {} items", invoice.getId(), invoice.getInvoiceItems().size());
            }
        }

        invoiceRepository.saveAndFlush(invoice);



            List<WorkerTrainingSession> trainingsToSave =  new ArrayList<>();


                for (Long id : workerTrainingSessionIds
                ) {
                        WorkerTrainingSession trainingSession = workerTrainingSessionService.getOne(id);
                        trainingSession.setIsAgencyBilled(true);
                        trainingsToSave.add(trainingSession);
                }
                workerTrainingSessionRepository.saveAll(trainingsToSave);



    }

    @Override
    public InvoiceResult findInvoice(Long invoiceId) {
        Invoice invoice = findInvoiceById(invoiceId);

        InvoiceResult invoiceres = toInvoiceResult.convert(invoice);

        invoiceres.setPaymentRef(paymentRepository.findAllByInvoiceId(invoiceId));

        return invoiceres;
    }

    @Override
    public InvoiceResult forceGenerateUnbilledItems(Long agencyId) throws Exception {

        List<Shift> shifts = shiftRepository.findAllByAgencyIdAndStatusAndIsAdminBilled(agencyId, "AUTHORIZED", false);
        List<Long> workerTrainingSessionIds =workerTrainingSessionService.findByAgencyIdAndNotAdminBilled(agencyId);
        
        List<Long> shiftIds = new ArrayList<>();
        shifts.forEach((e)->{
            shiftIds.add(e.getId());
        });

        InvoiceCreateDto invoiceCreateDto = new InvoiceCreateDto();
        invoiceCreateDto.setInvoiceType(InvoiceType.AGENCY);
        invoiceCreateDto.setShiftIds(shiftIds);
        invoiceCreateDto.setWorkerTrainingSessionIds(workerTrainingSessionIds);

        createInvoice(null, agencyId, shiftIds, workerTrainingSessionIds,invoiceCreateDto);

        return null;
    }


    @Override
    public Page<InvoiceResult> getForClient(Integer page, Integer size) {
        return invoiceRepository.findAll(PageRequest.of(page, size))
                .map(toInvoiceResult::convert);
    }


    @Override
    public Page<InvoiceResult> getForClient(Long clientId, Integer page, Integer size) {
        log.info("Request to get client invoices granted");
        return invoiceRepository.findAllByClientIdAndInvoiceTypeAndPublishedAndInvoiceStatusNot(clientId, InvoiceType.CLIENT,true,InvoiceStatus.PAID,PageRequest.of(page, size))
                .map(toInvoiceResult::convert);
    }
    @Override
    public Page<InvoiceResult> getAllAgencyInvoicesByAdmin(Long agencyId, Integer page, Integer size) {
        log.info("Request to get agency invoices by admin");
        Page<InvoiceResult> invoices = invoiceRepository.findAllByAgencyIdAndPublishedAndInvoiceTypeAndInvoiceStatusNot(agencyId, true, InvoiceType.AGENCY, InvoiceStatus.PAID, PageRequest.of(page, size))
                .map(toInvoiceResult::convert);

        Page<InvoiceResult> invoices1 = invoiceRepository.findAllByAgencyIdAndPublishedAndInvoiceTypeAndInvoiceStatusNot(agencyId, true, InvoiceType.AGENCYTRAINING, InvoiceStatus.PAID, PageRequest.of(page, size))
                .map(toInvoiceResult::convert);

        List<InvoiceResult> invs = new ArrayList<>();
        List<InvoiceResult> invs0 = invoices.toList();
        List<InvoiceResult> invs1 = invoices1.toList();
        invs.addAll( invs0);
        invs.addAll( invs1);


        return new PageImpl<InvoiceResult>(invs);
    }
    @Override
    public Page<InvoiceResult> getWorkerInvoices(Long workerId, Integer page, Integer size) {
        log.info("Request to get worker invoices granted");
        return invoiceRepository.findAllByWorkerIdAndInvoiceTypeOrderByInvoiceStatusDesc(workerId,InvoiceType.WORKERTRAINING,PageRequest.of(page, size))
                .map(toInvoiceResult::convert);
    }

    @Override
    public Page<InvoiceResult> getTrainerWorkerInvoices(Long agencyId, Boolean onlyPaid, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of) {
        log.info("Request to get client invoices granted");
        List<Invoice> invoices = new ArrayList<>();
        if(nonNull(onlyPaid)&&onlyPaid) {
            invoices = invoiceRepository.findAllByAgencyIdAndInvoiceTypeAndInvoiceStatus(agencyId, InvoiceType.WORKERTRAINING, InvoiceStatus.PAID);
        }else{
            invoices = invoiceRepository.findAllByAgencyIdAndInvoiceTypeAndInvoiceStatusNot(agencyId, InvoiceType.WORKERTRAINING, InvoiceStatus.PAID);

        }

        if(startDate!=null) invoices = invoices.stream()
                .filter(p -> checkAfter(p.getInvoiceDate().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null) invoices = invoices.stream()
                .filter(p -> checkBefore(p.getInvoiceDate().toString(), endDate))
                .collect(Collectors.toList());

        if(trainingId!=null) invoices = invoices.stream()
                .filter(p -> Objects.equals(trainingSessionService.findById(p.getInvoiceItems().get(0).getTrainingId()).getTrainingId(), trainingId))
                .collect(Collectors.toList());

        List<InvoiceResult>invoiceResult = invoices.stream().map(toInvoiceResult::convert).collect(Collectors.toList());

        return paginateInvoice(of, invoiceResult);

    }

    @Override
    public Page<InvoiceResult> getTrainerAgencyInvoices(Long agencyId, Boolean onlyPaid, Long agencyIdFilter,  LocalDate startDate, LocalDate endDate, PageRequest of) {
        log.info("Request to get client invoices granted");
        List<Invoice> invoices =  new ArrayList<>();
        if(nonNull(onlyPaid) && onlyPaid) {
            invoices = invoiceRepository.findAllByPayeeIdAndInvoiceTypeAndInvoiceStatus(agencyId, InvoiceType.AGENCYTRAINING, InvoiceStatus.PAID);
        }else{
            invoices = invoiceRepository.findAllByPayeeIdAndInvoiceTypeAndInvoiceStatusNot(agencyId, InvoiceType.AGENCYTRAINING, InvoiceStatus.PAID);
        }


        if(startDate!=null) invoices = invoices.stream()
                .filter(p -> checkAfter(p.getInvoiceDate().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null) invoices = invoices.stream()
                .filter(p -> checkBefore(p.getInvoiceDate().toString(), endDate))
                .collect(Collectors.toList());

        if(agencyIdFilter!=null) invoices = invoices.stream()
                .filter(p -> Objects.equals(p.getAgency().getId(), agencyIdFilter))
                .collect(Collectors.toList());

        List<InvoiceResult>invoiceResult = invoices.stream().map(toInvoiceResult::convert).collect(Collectors.toList());

        return paginateInvoice(of, invoiceResult);
    }


    @Override
    public Page<InvoiceResult> getAllAgencyInvoiceReportsByAdmin(Long agencyId, Integer page, Integer size) {
        log.info("Request to get client invoices granted");
        return invoiceRepository.findAllByAgencyIdAndInvoiceTypeAndInvoiceStatus(agencyId, InvoiceType.AGENCY,InvoiceStatus.PAID,PageRequest.of(page, size))
                .map(toInvoiceResult::convert);
    }

    @Override
    public void acknowledgePayment(Long invoiceId, String paymentRef, BigDecimal amount) {
        Invoice invoice = findInvoiceById(invoiceId);
        Payment payment = new Payment();

//        log.info("Updating invoice payments {},",payStatus );

        payment.setRef(paymentRef);
        payment.setStatus(InvoiceStatus.PAID);
        payment.setTotal(amount);
        payment.setInvoice(invoice);

        invoice.payInvoice(payment);
        invoiceRepository.save(invoice);


        if(invoice.getInvoiceType() == InvoiceType.WORKERTRAINING){
            invoice.getInvoiceItems().forEach(e->{
                WorkerTrainingSession booking = workerTrainingSessionService.findByWorkerAndTrainingSession(
                        workerService.getOne(invoice.getWorkerId()), trainingSessionService.getOne(e.getTrainingId())
                );
                trainingSessionService.approveOrRejectWorkerBooking(  booking.getId(),  true, false);
            });

        }

    }


    @Override
    public void setDiscount(Long invoiceId, BigDecimal discount) {
        Invoice invoice = findInvoiceById(invoiceId);

            invoice.setDiscount(discount);

            if(invoice.getVatPercentage()!=null) {
                BigDecimal newVat = (invoice.getVatPercentage().divide(BigDecimal.valueOf(100.0)))
                        .multiply(invoice.getSubTotalAmount().subtract(discount));


//                invoice.setTotalAmount(
//                        invoice.getTotalAmount().subtract(invoice.getVatAmount()).add(newVat)
//                );

//                invoice.setVatAmount(newVat);

            }


            invoiceRepository.save(invoice);

    }


    @Override
    public void publish(Long invoiceId, Boolean sendEmail) {
        Invoice invoice = findInvoiceById(invoiceId);


        invoice.setPublished(true);
        invoiceRepository.save(invoice);


        if(sendEmail){
            String name = "";
            String senderName = "";
            String email;

            if(invoice.getInvoiceType()==InvoiceType.CLIENT){
                Client client =  invoice.getClient();
                name = client.getName();
                email = nonNull(client.getBillingEmail()) ? client.getBillingEmail() : client.getEmail();
                Agency agency = invoice.getAgency();
                senderName = agency.getName();
            }
            else if(invoice.getInvoiceType()==InvoiceType.AGENCY||invoice.getInvoiceType()==InvoiceType.AGENCYTRAINING){
                Agency agency = invoice.getAgency();
                name = agency.getName();
                email = agency.getBillingEmail();
                senderName = companyName+" Admin";
            }
            else{
                email = null;
            }

            String title = "Invoice created on "+companyName;
            String body =                "Good day, "+name+"\n" +
                    senderName+" has created an invoice for you on the " +companyName+" agency staffing platform.\n" +
                    "Please login to your account to view full details via:\n" +

                    "https://myworklink.uk/\n" ;
            if(nonNull(email))CompletableFuture.runAsync(() ->
                    emailService.sendSimpleMessage(email, title, body, invoice.getAgency().getId())
            );
        }

    }

    @Override
    public void delete(Long invoiceId) {
        Invoice invoice = findInvoiceById(invoiceId);

        if(invoice.getPublished()!=null && invoice.getPublished()) throw  new BusinessValidationException("Invoice has already been published");

        Long shiftId = 0L;

        List<Shift> shiftsToSave = new ArrayList<>();
        List<WorkerTrainingSession> trainingsToSave = new ArrayList<>();

        for(int i = 0; i < invoice.getInvoiceItems().size(); i++ ){
            if(nonNull(invoice.getInvoiceItems().get(i).getShiftId()) && shiftId != invoice.getInvoiceItems().get(i).getShiftId()) {
                Shift shift = shiftService.getOne(invoice.getInvoiceItems().get(i).getShiftId());
                shift.setStatus(ShiftStatus.AUTHORIZED);

                if(invoice.getInvoiceType().equals(InvoiceType.CLIENT)){
                    shift.setIsAgencyBilled(false);
                }
                if(invoice.getInvoiceType().equals(InvoiceType.AGENCY)){
                    shift.setIsAdminBilled(false);
                }

                shiftService.save(shift);
            }
            shiftId = invoice.getInvoiceItems().get(i).getShiftId();
        }



        for(int i = 0; i < invoice.getInvoiceItems().size(); i++ ){
            if(nonNull(invoice.getInvoiceItems().get(i).getTrainingId())) {
                WorkerTrainingSession workerTrainingSession = workerTrainingSessionService.getOne(invoice.getInvoiceItems().get(i).getTrainingId());
                workerTrainingSession.setIsAgencyBilled(false);
                trainingsToSave.add(workerTrainingSession);
            }
        }

        workerTrainingSessionRepository.saveAll(trainingsToSave);



        invoiceRepository.delete(invoice);



    }



    public void deleteAdminInvoice(Long invoiceId) {
        Invoice invoice = findInvoiceById(invoiceId);

        if(invoice.getPublished()!=null && invoice.getPublished()) throw  new BusinessValidationException("Invoice has already been published");

        Long shiftId = 0L;

        List<Shift> shiftsToSave = new ArrayList<>();
        List<WorkerTrainingSession> trainingsToSave = new ArrayList<>();

        for(int i = 0; i < invoice.getInvoiceItems().size(); i++ ){
            if(nonNull(invoice.getInvoiceItems().get(i).getShiftId()))
                if(shiftId != invoice.getInvoiceItems().get(i).getShiftId()) {
                    Shift shift = shiftService.getOne(invoice.getInvoiceItems().get(i).getShiftId());
                    shift.setIsAdminBilled(false);
                    shiftsToSave.add(shift);
                }
            shiftId = invoice.getInvoiceItems().get(i).getShiftId();
        }

        shiftRepository.saveAll(shiftsToSave);

        for(int i = 0; i < invoice.getInvoiceItems().size(); i++ ){
            if(nonNull(invoice.getInvoiceItems().get(i).getTrainingId())) {
                WorkerTrainingSession workerTrainingSession = workerTrainingSessionService.getOne(invoice.getInvoiceItems().get(i).getTrainingId());
                workerTrainingSession.setIsAdminBilled(false);
                trainingsToSave.add(workerTrainingSession);
            }
        }

        workerTrainingSessionRepository.saveAll(trainingsToSave);

        invoiceRepository.delete(invoice);

    }



    @Override
    public Invoice findInvoiceById(Long id) {
        return invoiceRepository.findById(id)
                .orElseThrow(() ->
                        new RecordNotFoundException("Invoice " + id + " does not exist"));
    }

    @Override
    public Invoice getOne(Long id) {
        return invoiceRepository.findById(id)
                .orElseThrow(() ->
                        new RecordNotFoundException("Invoice " + id + " does not exist"));
    }

    @Override
    public Page<InvoiceResult> getPaidInvoicesDueForSettlement(Long rentalId, String status, LocalDate startDate, LocalDate endDate, Long bookingId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return invoiceRepository.findPaidInvoicesDueForSettlement(rentalId, status, startDate.atStartOfDay().atZone(ZoneOffset.UTC), endDate.atStartOfDay().atZone(ZoneOffset.UTC), bookingId, pageable).map(toInvoiceResult::convert);
    }


    @Override
    public Page<InvoiceResult> getAllInvoicesForAgent(Long agencyId, Integer page, Integer size) {
        return invoiceRepository.findAllUnpaidByAgencyId(agencyId, PageRequest.of(page, size))
                .map(toInvoiceResult::convert);
    }

    @Override
    public Page<InvoiceResult> getAllInvoiceReportsForAgent(Long agencyId, Integer page, Integer size) {
        return invoiceRepository.findAllPaidByAgencyId(agencyId, PageRequest.of(page, size))
                .map(toInvoiceResult::convert);
    }
    @Override
    public Page<InvoiceResult> getAllInvoicesForAdmin( Integer page, Integer size) {
        return invoiceRepository.findAllUnpaid( PageRequest.of(page, size))
                .map(toInvoiceResult::convert);
    }

    @Override
    @SneakyThrows
    public ResponseEntity<Resource> downloadInvoice(Long invoiceId, ReportFormat format,
                                                    HttpServletRequest servletRequest) {
        return reportService.generateInvoice(servletRequest, format.name(), invoiceId);
    }


    private List<InvoiceItem> createShiftInvoiceItems(Long clientId, Long agencyId, InvoiceType invoiceType, List<Long> shiftIds) throws Exception {

        List<InvoiceItem> invoiceItems = new ArrayList<>();

        try {

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            for (Long shift : shiftIds) {
                boolean breakFactoredIn = false;


                Shift result = shiftService.getOne(shift);

                // Skip iteration if shift does not belong to client in client billing
                if(invoiceType == InvoiceType.CLIENT){
                    if(!Objects.equals(result.getClient().getId(), clientId)){
                        continue;
                    }
                }

                // Skip iteration if shift does not belong to agency in agency billing
                if(invoiceType == InvoiceType.AGENCY){
                    if(result.getAgency()!=null) {
                        if (!Objects.equals(result.getAgency().getId(), agencyId)) {
                            continue;
                        }
                    }else{
                        log.error("This shift did not have an agency assigned to it: {}", result);
                    }
                }


                log.info(" Shift total hours {}", result.getStart()
                        .until(result.getEnd(), ChronoUnit.HOURS));

                if (
                        (invoiceType == InvoiceType.CLIENT && result.getStatus().name().equalsIgnoreCase("AUTHORIZED"))
                        || ((result.getIsAdminBilled()==null || !result.getIsAdminBilled()) && invoiceType == InvoiceType.AGENCY )

                ) {

                    List<ShiftDayTime> shiftDayTimeList = splitShiftDurationToDays(result);
                    log.info("Shift day time list {}", shiftDayTimeList);

                    Set<ShiftRateItem> shiftRateItems = new HashSet<>();
                    for (int j = 0; j < shiftDayTimeList.size(); j++) {

                        ShiftDayTime daytime = shiftDayTimeList.get(j);

                        var shiftRates = getDayRate(result.getClient().getId(),result.getAgency().getId(), invoiceType , daytime.getDayOfWeek(), result);

                        log.info("############# Shift rates {}", shiftRates);

                        log.info("### J {}", j);
                        for (ShiftRateItem shiftRateItem : shiftRates) {

                            LocalTime rateStartTime = shiftRateItem.getStartTime();
                            LocalTime rateEndTime = shiftRateItem.getEndTime();
                            var rateItemRateEndTime = daytime.getShiftDate().atTime(rateEndTime);
                            var rateItemRateStartTime = daytime.getShiftDate().atTime(rateStartTime);

                            log.info("#################### rateItemRateStartTime {} and rateItemRateEndTime {}", rateItemRateStartTime, rateItemRateEndTime);
                            if (rateStartTime.isAfter(rateEndTime) || rateStartTime.equals(rateEndTime)) {

                                var rateItemRateEndTimePlusOneDay = rateItemRateEndTime.plusDays(1).toLocalDate();

                                log.info("###### rateItemRateEndTime {}", rateItemRateEndTime);

                                shiftRateItem.setRateStartTime(rateItemRateStartTime);
                                shiftRateItem.setRateEndTime(rateItemRateEndTime.plusDays(1));
                                shiftRateItems.add(shiftRateItem);
                            } else {

                                if ((daytime.getShiftEndTime().isBefore(rateItemRateStartTime) ||
                                        daytime.getShiftStartTime().isBefore(rateItemRateStartTime)) &&
                                        j == 0) {

                                    //Get yesterday rate

                                    val yesterday = daytime.getShiftDate().minusDays(1);

                                    val yesterdayDayOfWeek = yesterday.getDayOfWeek().name();

                                    val yesterdayRates =
                                            getDayRate(result.getClient().getId(),result.getAgency().getId() , invoiceType,yesterdayDayOfWeek, result);

                                    log.info("#### Yesterday rates {}", yesterdayRates);

                                    for (ShiftRateItem yesterdayRate : yesterdayRates) {

                                        LocalTime yesterdayRateStartTime = yesterdayRate.getStartTime();
                                        LocalTime yesterdayRateEndTime = yesterdayRate.getEndTime();

                                        var yesterdayRateItemRateEndTime = yesterday.atTime(yesterdayRateEndTime);
                                        var yesterdayRateItemRateStartTime = yesterday.atTime(yesterdayRateStartTime);

                                        log.info("############# yesterdayRateItemRateStartTime {} and yesterdayRateItemRateEndTime {} ",
                                                yesterdayRateItemRateStartTime, yesterdayRateItemRateEndTime);

                                        if (yesterdayRateStartTime.isAfter(yesterdayRateEndTime) ||
                                                yesterdayRateStartTime.equals(yesterdayRateEndTime)) { //Take only overlapping rate
                                            log.info("#### Taking only overlapping rates ");
                                            var yesterdayShiftRateItem = new ShiftRateItem(yesterdayDayOfWeek,
                                                    yesterdayRate.getStartTime(), yesterdayRate.getEndTime(), yesterdayRate.getRate());
                                            yesterdayShiftRateItem.setRateStartTime(yesterdayRateItemRateStartTime);
                                            yesterdayShiftRateItem.setRateEndTime(yesterdayRateItemRateEndTime.plusDays(1));
                                            shiftRateItems.add(yesterdayShiftRateItem);
                                        } else {
                                            log.info("##### Discarded  yesterday rate {}", yesterdayRate);
                                        }

                                    }
                                }

                                shiftRateItem.setRateStartTime(rateItemRateStartTime);
                                shiftRateItem.setRateEndTime(rateItemRateEndTime);
                                shiftRateItems.add(shiftRateItem);

                            }
                        }
                    }

                    shiftRateItems = shiftRateItems.stream()
                            .sorted(Comparator.comparing(ShiftRateItem::getRateStartTime))
                            .collect(Collectors.toCollection(LinkedHashSet::new));

                    log.info("-----> Shifts rates items {}", shiftRateItems);

                    for (ShiftRateItem rateItem : shiftRateItems) {

                        for (ShiftDayTime daytime : shiftDayTimeList) {

                            DailyShiftRate dailyShiftRate = getItemRatePerSlot(daytime.getShiftStartTime(),
                                    daytime.getShiftEndTime(),
                                    rateItem.getRateStartTime(),
                                    rateItem.getRateEndTime(),
                                    rateItem.getRate(),
                                    daytime.getDayOfWeek());
                            if (nonNull(dailyShiftRate)) {
                                InvoiceItem invoiceItem = new InvoiceItem();
                                invoiceItem.setDayOfTheWeek(daytime.getDayOfWeek());
                                invoiceItem.setRate(dailyShiftRate.getRate());
                                invoiceItem.setShiftId(result.getId());
                                invoiceItem.setAssignmentCode(result.getAssignmentCode().getCode());
                                invoiceItem.setClient(result.getClient().getName());
                                invoiceItem.setClientId(result.getClient().getId());

                                invoiceItem.setWorker(result.getWorker().getFirstname() + " " + result.getWorker().getLastname());


                                invoiceItem.setShiftType(result.getShiftType().getName());
                                invoiceItem.setDirectorate(result.getDirectorate().getName());
                                invoiceItem.setStartDate(dailyShiftRate.getBillStartDateTime().toLocalDate().format(formatter));
                                invoiceItem.setEndDate(dailyShiftRate.getBillEndDateTime().toLocalDate().format(formatter));
                                invoiceItem.setStartTime(dailyShiftRate.getBillStartDateTime().toLocalTime().toString());
                                invoiceItem.setEndTime(dailyShiftRate.getBillEndDateTime().toLocalTime().toString());

                                LocalDateTime localDateTime = daytime.getShiftDate().atStartOfDay();
                                LocalDateTime endOfDate = localDateTime
                                        .toLocalDate().atTime(LocalTime.MAX);

                                long minutesWorked = dailyShiftRate.getMinutesWorked();

                                if (dailyShiftRate.getBillEndDateTime().compareTo(endOfDate) == 0) {
                                    minutesWorked = minutesWorked + 1;
                                    log.info("### Minutes worked {}", minutesWorked);
                                }

                                double hoursWorked;

                                if (!breakFactoredIn) {

                                    long hours = Long.parseLong(daytime.getBreakTime().split(" ")[0].replace("hr", "")) * 60;
                                    long minutes = Long.parseLong(daytime.getBreakTime().split(" ")[1].replace("mins", ""));

                                    long minutesWorkedAfterRemoveBreak = (minutesWorked - (hours + minutes));

                                    hoursWorked = (double) minutesWorkedAfterRemoveBreak / 60;

                                    breakFactoredIn = true;
                                } else {
                                    hoursWorked = (double) minutesWorked / 60;
                                }

                                invoiceItem.setNumberOfHoursWorked(hoursWorked);

                                invoiceItem.setTotal(dailyShiftRate.getRate().multiply(BigDecimal.valueOf(invoiceItem.getNumberOfHoursWorked())));
                                invoiceItems.add(invoiceItem);
                            }
                        }
                    }
                }

            }
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new BusinessValidationException(exception.getMessage());
        }
        return invoiceItems;
    }
    private List<InvoiceItem> createTrainingInvoiceItems(Long agencyId, List<Long> workerTrainingSesionIds) throws Exception {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        try {
            for (Long workerTrainingSessionId : workerTrainingSesionIds) {
                WorkerTrainingSession trainingSession = workerTrainingSessionService.getOne(workerTrainingSessionId);
                if(nonNull(trainingSession.getTrainingSession().getTrainer())  && !Objects.equals(trainingSession.getTrainingSession().getTrainer().getId(), agencyId)) continue;
                else log.error("This booking did not have a trainer assigned to it: {}", trainingSession);

                if ((trainingSession.getTrainingStatus().name().equalsIgnoreCase("CLOSED")) || ((trainingSession.getIsAdminBilled()==null || !trainingSession.getIsAdminBilled()) ))
                  {

                      InvoiceItem invoiceItem = new InvoiceItem();
                      invoiceItem.setTrainingId(trainingSession.getId());
                      invoiceItem.setRate(BigDecimal.valueOf(1.0));
                      invoiceItem.setNumberOfHoursWorked(1);
                      invoiceItem.setStartDate(trainingSession.getTrainingSession().getStartDateTime().toLocalDate().format(formatter));
                      invoiceItem.setEndDate(trainingSession.getTrainingSession().getEndDateTime().toLocalDate().format(formatter));
                      invoiceItem.setStartTime(trainingSession.getTrainingSession().getStartDateTime().toLocalTime().toString());
                      invoiceItem.setEndTime(trainingSession.getTrainingSession().getEndDateTime().toLocalTime().toString());
                      invoiceItem.setTotal(BigDecimal.valueOf(1));
                      invoiceItem.setWorker(trainingSession.getWorker().getFirstname() + " " + trainingSession.getWorker().getLastname());


                      var training = workerTrainingSessionService.
                              getTraining(
                                      workerTrainingSessionId);


                      invoiceItem.setDescription(
                             training.getName()+" training id:"+training.getId()
                      );
                      invoiceItems.add(invoiceItem);
                }
            }
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new BusinessValidationException(exception.getMessage());
        }
        return invoiceItems;
    }


    private boolean checkAfter(String leftDate, LocalDate rightDate) {
        return convertFromString(leftDate).compareTo(rightDate) >= 0;
    }

    private boolean checkBefore(String leftDate, LocalDate rightDate) {
        return convertFromString(leftDate).compareTo(rightDate) <= 0;
    }

    private LocalDate convertFromString(String aDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        } catch (DateTimeParseException e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        }
    }


    private List<InvoiceItem> billAgencyAsTrainer(Long agencyId, List<Long> workerTrainingSesionIds) throws Exception {

        List<InvoiceItem> invoiceItems = new ArrayList<>();
        try {
            for (Long workerTrainingSessionId : workerTrainingSesionIds) {
                WorkerTrainingSession trainingSession = workerTrainingSessionService.getOne(workerTrainingSessionId);
                if(nonNull(trainingSession.getTrainingSession().getTrainer())  && !Objects.equals(trainingSession.getTrainingSession().getTrainer().getId(), agencyId)) continue;
                else log.error("This booking did not have a trainer assigned to it: {}", trainingSession);

                if ((trainingSession.getTrainingStatus().name().equalsIgnoreCase("CLOSED")) || ((trainingSession.getIsAdminBilled()==null || !trainingSession.getIsAdminBilled()) ))
                {
                    InvoiceItem invoiceItem = new InvoiceItem();
                    invoiceItem.setTrainingId(trainingSession.getId());
                    invoiceItem.setNumberOfHoursWorked(1);
                    invoiceItem.setRate(BigDecimal.valueOf(trainingSession.getTrainingSession().getTrainingCost()));
                    invoiceItem.setStartDate(trainingSession.getTrainingSession().getStartDateTime().toLocalDate().toString());
                    invoiceItem.setEndDate(trainingSession.getTrainingSession().getEndDateTime().toLocalDate().toString());
                    invoiceItem.setWorker(trainingSession.getWorker().getFirstname() + " " + trainingSession.getWorker().getLastname());

                    invoiceItem.setStartTime(trainingSession.getTrainingSession().getStartDateTime().toLocalTime().toString());
                    invoiceItem.setEndTime(trainingSession.getTrainingSession().getEndDateTime().toLocalTime().toString());
                    invoiceItem.setTotal(BigDecimal.valueOf(trainingSession.getTrainingSession().getTrainingCost()));

                    var training = workerTrainingSessionService.
                            getTraining(
                                    workerTrainingSessionId);


                    invoiceItem.setDescription(
                            training.getName()+" booking id: "+workerTrainingSessionId
                    );
                    invoiceItems.add(invoiceItem);
                }
            }
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new BusinessValidationException(exception.getMessage());
        }
        return invoiceItems;
    }


    private List<ShiftRateItem> getDayRate(Long clientId,Long agencyId, InvoiceType invoiceType,String dayOfWeek, Shift shift) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        List<ShiftRateItem> rates = new ArrayList<>();
        List<AssignmentRate> assignmentRate = new ArrayList<AssignmentRate>();
        if(invoiceType == InvoiceType.AGENCY) {
            assignmentRate = chargeRateService.findForAgencyBilling(
                    agencyId, shift.getShiftType(), shift.getAssignmentCode()
            );
        }else if(invoiceType == InvoiceType.CLIENT){
            assignmentRate = assignmentCodeRateService.findAssignmentRate(
                    clientId, shift.getDirectorate().getId(), shift.getShiftType().getId(), dayOfWeek, shift.getAssignmentCode().getId()
            );
        }

        for (AssignmentRate rate : assignmentRate
        ) {
            rates.add(new ShiftRateItem(dayOfWeek, rate.getStartTime(), rate.getEndTime(), rate.getClientRate()));
        }

        if (rates.size() == 0) {
            throw new BusinessValidationException("Rates not set");
        }
        return rates;
    }


    private Integer getNumberOfDays(LocalDateTime startDate, LocalDateTime endDate) {
        return (int) ChronoUnit.DAYS.between(startDate, endDate);
    }

    private String getDayOfTheWeekFromDate(LocalDate data) {
        return data.getDayOfWeek().name();
    }

    private long getNumberOfHoursWorked(LocalDate startDate, LocalDate endDate, String startTime, String endTime) {

        LocalDateTime time = startDate.atStartOfDay();
        log.info("Start time: {}", startTime);
        log.info("End time: {}", endTime);
        LocalDateTime startDateTime = startDate.atTime(LocalTime.parse(startTime));
        LocalDateTime endDateTime = endDate.atTime(LocalTime.parse(endTime));
        return startDateTime.until(endDateTime, ChronoUnit.HOURS);
    }


    void generateInvoiceItems(AssignmentRate rate) {

    }


    private List<ShiftDayTime> splitShiftDurationToDays(Shift shift) {

        List<ShiftDayTime> shiftDayTimeList = new ArrayList<>();
        int days = getNumberOfDays(shift.getStart(), shift.getEnd()) + 1;

        if (days == 1) {
            ShiftDayTime shiftDayTime = new ShiftDayTime(
                    getDayOfTheWeekFromDate(shift.getStart().toLocalDate()),
                    shift.getStart().toLocalDate(),
                    shift.getStart().toLocalDate().atTime(shift.getStart().toLocalTime()),
                    shift.getStart().toLocalDate().atTime(shift.getEnd().toLocalTime()),
                    shift.getBreakTime()
            );
            shiftDayTimeList.add(shiftDayTime);
        } else {
            int i;
            ShiftDayTime shiftDayTime;
            for (i = 1; i < days; i++) {
                LocalDateTime localDateTime = shift.getStart().toLocalDate().atStartOfDay();
                LocalDateTime endOfDate = localDateTime.toLocalDate().atTime(LocalTime.MAX);
                if (i == 1) {
                    shiftDayTime = new ShiftDayTime(
                            getDayOfTheWeekFromDate(shift.getStart().toLocalDate()),
                            shift.getStart().toLocalDate(),
                            shift.getStart().toLocalDate().atTime(shift.getStart().toLocalTime()),
                            endOfDate,
                            shift.getBreakTime()
                    );

                } else {
                    shiftDayTime = new ShiftDayTime(
                            getDayOfTheWeekFromDate(shift.getStart().toLocalDate().plusDays(i - 1)),
                            shift.getStart().toLocalDate().plusDays(i - 1),
                            shift.getStart().plusDays(i - 1).toLocalDate().atStartOfDay(),
                            endOfDate.plusDays(i - 1),
                            "0hr 0mins"
                    );
                }
                shiftDayTimeList.add(shiftDayTime);

            }
            shiftDayTime = new ShiftDayTime(
                    getDayOfTheWeekFromDate(shift.getStart().toLocalDate().plusDays(i - 1)),
                    shift.getStart().toLocalDate().plusDays(i - 1),
                    shift.getStart().plusDays(i - 1).toLocalDate().atStartOfDay(),
                    shift.getStart().plusDays(i - 1).toLocalDate().atTime(shift.getEnd().toLocalTime()),
                    "0hr 0mins"
            );
            shiftDayTimeList.add(shiftDayTime);
        }
        return shiftDayTimeList;


    }


    private DailyShiftRate getItemRatePerSlot(LocalDateTime shiftStartTime,
                                              LocalDateTime shiftEndTime,
                                              LocalDateTime rateStartTime,
                                              LocalDateTime rateEndTime,
                                              BigDecimal rate,
                                              String dayOfWeek) {
        log.info("### shift start time {} shift end time {} rate start time {} rate end time {}", shiftStartTime,
                shiftEndTime, rateStartTime, rateEndTime);

        if (shiftStartTime.isAfter(rateEndTime)) {
            return null;
        }

        if (shiftStartTime.isAfter(rateStartTime) || shiftStartTime.isEqual(rateStartTime)) {
            if (shiftEndTime.isBefore(rateEndTime) || shiftEndTime.isEqual(rateEndTime)) {
                log.info("====1==== Take shiftStartTime {} shiftEndTime {} minutes {} ", shiftStartTime,
                        shiftEndTime, Math.abs(shiftStartTime.until(shiftEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(shiftStartTime.until(shiftEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        shiftStartTime,
                        shiftEndTime

                );
            }
        }

        if (shiftStartTime.isAfter(rateStartTime) || shiftStartTime.isEqual(rateStartTime)) {
            if (shiftEndTime.isAfter(rateEndTime)) {
                log.info("===2=== Take shiftStartTime {} rateEndTime {} minutes {} ", shiftStartTime,
                        rateEndTime, Math.abs(shiftStartTime.until(rateEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(shiftStartTime.until(rateEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        shiftStartTime,
                        rateEndTime
                );
            }
        }
        if (shiftStartTime.isAfter(rateStartTime) || shiftStartTime.isEqual(rateStartTime)) {
            if (rateEndTime.isBefore(shiftEndTime)) {
                log.info("===3=== Take rateStartTime {} shiftEndTime {} minutes {} ", rateStartTime,
                        shiftEndTime, Math.abs(rateStartTime.until(shiftEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(rateStartTime.until(shiftEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        rateStartTime,
                        shiftEndTime
                );
            }
        }

        if (shiftStartTime.isBefore(rateStartTime)) {
            if (rateEndTime.isAfter(shiftEndTime) && rateStartTime.isBefore(shiftEndTime)) {
                log.info("===4=== Take rateStartTime {} shiftEndTime {} minutes {} ", rateStartTime,
                        shiftEndTime, Math.abs(rateStartTime.until(shiftEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(rateStartTime.until(shiftEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        rateStartTime,
                        shiftEndTime
                );
            }
        }
        if (shiftStartTime.isBefore(rateStartTime)) {
            if (rateEndTime.isBefore(shiftEndTime) || rateEndTime.isEqual(shiftEndTime)) {
                log.info("===5=== Take rateStartTime {} rateEndTime {} minutes {} ", rateStartTime,
                        rateEndTime, Math.abs(rateStartTime.until(rateEndTime, ChronoUnit.MINUTES)));
                return new DailyShiftRate(
                        Math.abs(rateStartTime.until(rateEndTime, ChronoUnit.MINUTES)),
                        dayOfWeek,
                        rate,
                        rateStartTime,
                        rateEndTime
                );
            }
        }

        return null;
    }







    @Override
    @Transactional
    public void settleInvoices(Long carRentalId, Set<Long> invoiceIds) {
        // Fetch paid invoices due for settlement
        List<Invoice> paidInvoices = invoiceRepository.findAllById(invoiceIds);
        Agency agency = agencyService.getOne(carRentalId);
        // Calculate total amount to settle
        Double totalAmount = paidInvoices.stream()
                .mapToDouble(i->i.getTotalAmount().doubleValue())
                .sum();

        // Create and save settlement
        SettlementStatement settlement = new SettlementStatement(
                agency,
                paidInvoices,
                totalAmount,
                LocalDate.now()
        );

        settlementStatementRepository.save(settlement);

        // Update invoice statuses if needed
        paidInvoices.forEach(invoice -> {
            invoice.setSettlementStatus(SettlementStatus.SUBMITTED); // Assuming Invoice has a settled field
            invoiceRepository.save(invoice);
        });
    }



    @Override
        public Page<SettlementStatement> getSettlementsByFilters(Long carRentalId,
                                                        LocalDate createdDateStart,
                                                        LocalDate createdDateEnd,
                                                        Long id,
                                                        Double amount,
                                                        String status,
                                                        int page,
                                                        int size) {
            Pageable pageable = PageRequest.of(page, size);
            return settlementStatementRepository.findByFilters(carRentalId, createdDateStart, createdDateEnd, id, amount, status, pageable);
        }


    /**
     * Apply tax calculations to shift-based invoice items
     * For shift invoices, we apply a default tax calculation since shifts don't have specific tax configuration
     */
    private void applyTaxCalculationsToShiftInvoice(Invoice invoice, AgencySettings agencySettings) {
        try {
            if (!agencySettings.isChargeVat()) {
                // No tax to apply
                return;
            }

            // Apply tax calculations to each invoice item
            for (InvoiceItem item : invoice.getInvoiceItems()) {
                // For shift items, we use the agency default tax rate and settings
                // Since shifts don't have specific tax exemption or custom rates
                BigDecimal taxRate = agencySettings.getVatPercentage();
                boolean taxInclusive = agencySettings.isDefaultTaxInclusive();

                // Apply tax calculation
                item.calculateTax(taxRate, taxInclusive);
            }

        } catch (Exception e) {
            log.error("Error applying tax calculations to shift invoice ID: {}", invoice.getId(), e);
            // Don't throw exception to avoid breaking the invoice creation process
        }
    }
}
