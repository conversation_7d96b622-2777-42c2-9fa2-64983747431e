package com.cap10mycap10.worklinkservice.search;


import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerToWorkerResultDto;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.AgencyWorkerPropertiesService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import org.apache.lucene.search.Query;
// TODO: Implement Hibernate Search 6.x
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.NoResultException;
import java.util.List;

@Service
public class WorkerSearchService {

    private final EntityManager entityManager;

    @Autowired
    private WorkerToWorkerResultDto toWorkerResultDto;
    @Autowired
    private WorkerService workerService;

    @Autowired
    private AgencyWorkerPropertiesService agencyWorkerPropertiesService;

    @Autowired
    public WorkerSearchService(EntityManagerFactory entityManagerFactory) {
        this.entityManager = entityManagerFactory.createEntityManager();
    }

    public void initializeHibernateSearch() {
        // TODO: Implement Hibernate Search 6.x initialization
        // Temporarily disabled for compilation
    }

    @Transactional
    public Page<WorkerResultDto> fuzzySearch(String searchTerm, int page, int size) {
        // TODO: Implement Hibernate Search 6.x fuzzy search
        // Temporarily returning empty page for compilation
        return new PageImpl<>(List.of(), PageRequest.of(page, size), 0);
    }



    @Transactional
    public Page<WorkerResultDto> fuzzyAgencySearch(String searchTerm, Long agencyId, int page, int size) {
        // TODO: Implement Hibernate Search 6.x fuzzy agency search
        // Temporarily returning empty page for compilation
        return new PageImpl<>(List.of(), PageRequest.of(page, size), 0);
    }
}
