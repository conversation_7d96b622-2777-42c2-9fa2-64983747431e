package com.cap10mycap10.worklinkservice.mapper.shiftdirectorate;

import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateCreateDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.model.Location;
import com.cap10mycap10.worklinkservice.model.ShiftDirectorate;
import com.cap10mycap10.worklinkservice.service.ClientService;
import com.cap10mycap10.worklinkservice.service.LocationService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class ShiftDirectorateDtoToShiftDirectorate implements Converter<ShiftDirectorateCreateDto, ShiftDirectorate> {

    private final LocationService locationService;
    private final AuthenticationFacade authenticationFacade;
    private final ClientService clientService;

    public ShiftDirectorateDtoToShiftDirectorate(final LocationService locationService, AuthenticationFacade authenticationFacade, ClientService clientService) {
        this.locationService = locationService;
        this.authenticationFacade = authenticationFacade;
        this.clientService = clientService;
    }

    @Override
    public ShiftDirectorate convert(ShiftDirectorateCreateDto shiftDirectorateCreateDto) {
        Location location = locationService.getOne(shiftDirectorateCreateDto.getShiftLocationId());
        Client client = clientService.getOne(shiftDirectorateCreateDto.getClientId());
        ShiftDirectorate shiftDirectorate = new ShiftDirectorate();
        shiftDirectorate.setName(shiftDirectorateCreateDto.getName());
        shiftDirectorate.setDeputyId(shiftDirectorateCreateDto.getDeputyId());
        shiftDirectorate.setPostCode(shiftDirectorateCreateDto.getPostCode());
        //Sometimes system creates this
//        shiftDirectorate.setCreatedBy(authenticationFacade.getAuthentication().getCity());
        shiftDirectorate.setPhoneNumber(shiftDirectorateCreateDto.getPhoneNumber());
        shiftDirectorate.setLocation(location);
        shiftDirectorate.setClient(client);

        if (shiftDirectorate.getClient() == null) {
            throw new BusinessValidationException("Please select a client.");
        }

        return shiftDirectorate;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
