package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.expenses.ExpenseRateDto;
import com.cap10mycap10.worklinkservice.dto.expenses.ExpenseRateUpdateDto;
import com.cap10mycap10.worklinkservice.model.ExpenseRate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface ExpenseRateService {

    void addExpenseRate(ExpenseRateDto expenseRateDto);

    void deleteExpenseRate(Long id);

    ExpenseRate findById(Long id);

    List<ExpenseRate> findAll();

    Page<ExpenseRate> findAllPaged(PageRequest of);


    ExpenseRate save(ExpenseRateUpdateDto expenseRateUpdateDto);

    ExpenseRate getOne(Long id);

}
