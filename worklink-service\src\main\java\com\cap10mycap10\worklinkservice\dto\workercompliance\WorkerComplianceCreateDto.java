package com.cap10mycap10.worklinkservice.dto.workercompliance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;

@Data
public class WorkerComplianceCreateDto {
    private Long complianceId;
    private String description;
    private String complianceDate;
    private MultipartFile file;
    private String complianceExpiry;
    private Long workerId;
    private Long agencyId;
}
