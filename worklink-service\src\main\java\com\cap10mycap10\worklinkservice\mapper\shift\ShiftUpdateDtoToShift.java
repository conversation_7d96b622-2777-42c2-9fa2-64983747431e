package com.cap10mycap10.worklinkservice.mapper.shift;

import com.cap10mycap10.worklinkservice.dto.shift.ShiftUpdateDto;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.service.*;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
@Slf4j
@Component
public class ShiftUpdateDtoToShift implements Converter<ShiftUpdateDto, Shift> {

    private final ClientService clientService;
    private final LocationService locationService;
    private final ShiftTypeService shiftTypeService;
    private final AssignmentCodeService assignmentCodeService;
    private final ShiftDirectorateService shiftDirectorateService;

    public ShiftUpdateDtoToShift(ClientService clientService, LocationService locationService, ShiftTypeService shiftTypeService, AssignmentCodeService assignmentCodeService, ShiftDirectorateService shiftDirectorateService) {
        this.clientService = clientService;
        this.locationService = locationService;
        this.shiftTypeService = shiftTypeService;
        this.assignmentCodeService = assignmentCodeService;
        this.shiftDirectorateService = shiftDirectorateService;
    }


    @Override
    public Shift convert(ShiftUpdateDto shiftUpdateDto) {
        Shift shift = new Shift();
//        shift.setGender(shiftUpdateDto.getGender());
        shift.setHoursBeforeBroadcasting(shiftUpdateDto.getHoursBeforeBroadcasting());
        shift.setNotes(shiftUpdateDto.getNotes());
        shift.setStart(shiftUpdateDto.getStart());
//        shift.setShiftStartTime(shiftUpdateDto.getShiftStartTime());
//        shift.setShiftEndTime(shiftUpdateDto.getShiftEndTime());


        if(shiftUpdateDto.getNoneAttendance()){
            log.info("Setting shift status cancelled");
            shift.setStatus(ShiftStatus.CANCELLED);
        }else{
            log.info("Setting shift status {}", shiftUpdateDto.getShiftStatus());
            shift.setStatus(shiftUpdateDto.getShiftStatus());
        }
//        shift.setShiftType(shiftTypeService.getOne(shiftUpdateDto.getShiftType()));
        shift.setShowNoteToAgency(shiftUpdateDto.getShowNoteToAgency());
        shift.setShowNoteToFw(shiftUpdateDto.getShowNoteToFw());
        shift.setDirectorate(shiftDirectorateService.getOne(shiftUpdateDto.getShiftDirectorateId()));
        return shift;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
