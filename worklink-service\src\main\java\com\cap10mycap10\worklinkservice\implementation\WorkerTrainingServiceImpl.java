package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AgencyWorkerComplianceRepository;
import com.cap10mycap10.worklinkservice.dao.AgencyWorkerTrainingRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerComplianceRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerTrainingRepository;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingCreateDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingResultDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingUpdateDto;
import com.cap10mycap10.worklinkservice.enums.TrainingStatus;
import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.mapper.workercompliance.WorkerComplianceToWorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.mapper.workertraining.WorkerTrainingToWorkerTrainingResultDto;
import com.cap10mycap10.worklinkservice.model.AgencyWorkerTraining;
import com.cap10mycap10.worklinkservice.model.Training;
import com.cap10mycap10.worklinkservice.model.WorkerTraining;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.TrainingService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import com.cap10mycap10.worklinkservice.service.WorkerTrainingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;

import static java.util.Objects.nonNull;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class WorkerTrainingServiceImpl implements WorkerTrainingService {
    @Value("${storage.volume.path}")
    private  String rootPath;
    private final AgencyService agencyService;
    private final WorkerService workerService;
    private final WorkerTrainingToWorkerTrainingResultDto toWorkerTrainingResultDto;
    private final WorkerComplianceToWorkerComplianceResultDto toWorkerComplianceResultDto;
    private final  DataBucketUtil dataBucketUtil;
    private final TrainingService trainingService;
    private final AgencyWorkerTrainingRepository agencyWorkerTrainingRepository;
    private final AgencyWorkerComplianceRepository agencyWorkerComplianceRepository;

    private final WorkerTrainingRepository workerTrainingRepository;
    private final WorkerComplianceRepository workerComplianceRepository;

    public WorkerTrainingServiceImpl(AgencyService agencyService, WorkerService workerService, WorkerTrainingToWorkerTrainingResultDto toWorkerTrainingResultDto, WorkerComplianceToWorkerComplianceResultDto toWorkerComplianceResultDto, DataBucketUtil dataBucketUtil, TrainingService trainingService,
                                     AgencyWorkerTrainingRepository agencyWorkerTrainingRepository, AgencyWorkerComplianceRepository agencyWorkerComplianceRepository, WorkerTrainingRepository workerTrainingRepository, WorkerComplianceRepository workerComplianceRepository) {
        this.agencyService = agencyService;
        this.workerService = workerService;
        this.toWorkerTrainingResultDto = toWorkerTrainingResultDto;
        this.toWorkerComplianceResultDto = toWorkerComplianceResultDto;
        this.dataBucketUtil = dataBucketUtil;
        this.trainingService = trainingService;
        this.agencyWorkerTrainingRepository = agencyWorkerTrainingRepository;
        this.agencyWorkerComplianceRepository = agencyWorkerComplianceRepository;
        this.workerTrainingRepository = workerTrainingRepository;
        this.workerComplianceRepository = workerComplianceRepository;
    }

    @Override
    public WorkerTrainingResultDto addWorkerTraining(WorkerTrainingCreateDto createDto) {

        WorkerTraining workerTraining = new WorkerTraining();
        Training training = trainingService.getOne(createDto.getTrainingId());

        workerTraining.setDescription(createDto.getDescription());
        workerTraining.setName(training.getName());
        workerTraining.setTrainingDate(LocalDate.parse(createDto.getTrainingDate()));
        workerTraining.setTrainingExpiry(LocalDate.parse(createDto.getTrainingExpiry()));
        if(nonNull(createDto.getFile())) {
            String fileUrl = dataBucketUtil.uploadFile(createDto.getFile(), "ComplianceDoc", String.valueOf(createDto.getWorkerId()), WorklinkUserType.WORKER);
            workerTraining.setDocument(fileUrl);
        }
        workerTraining.setAgency(agencyService.getOne(createDto.getAgencyId()));
        workerTraining.setWorker(workerService.getOne(createDto.getWorkerId()));
        workerTraining.setTraining(training);
        WorkerTraining result = workerTrainingRepository.saveAndFlush(workerTraining);

        return toWorkerTrainingResultDto.convert(result);
    }

    @Override
    public void updateWorkerTraining(WorkerTrainingUpdateDto workerTrainingUpdateDto) {
        WorkerTraining workerTraining = getOne(workerTrainingUpdateDto.getId());
        AgencyWorkerTraining agencyWorkerTraining;
        try {
            agencyWorkerTraining = agencyWorkerTrainingRepository
                    .findAllByWorkerIdAndAgencyIdAndTrainingId(
                            workerTraining.getWorker().getId(),
                            workerTrainingUpdateDto.getAgencyId(),
                            workerTraining.getId()
                    ).get(0);
        }
        catch (IndexOutOfBoundsException e){

            AgencyWorkerTraining trainer = new AgencyWorkerTraining();
            trainer.setAgency(agencyService.getOne(workerTrainingUpdateDto.getAgencyId()));
            trainer.setWorker(workerService.getOne(workerTrainingUpdateDto.getWorkerId() ));
                trainer.setTraining(getOne(workerTraining.getId()));

                agencyWorkerTraining = trainer;
        }


            log.info("Updated worker-training{}", workerTraining);
            if (workerTrainingUpdateDto.getDescription() != null) {
                workerTraining.setDescription(workerTrainingUpdateDto.getDescription());
            }
            if (workerTrainingUpdateDto.getTrainingDate() != null) {
                workerTraining.setTrainingDate(workerTrainingUpdateDto.getTrainingDate());
            }
            if (workerTrainingUpdateDto.getTrainingExpiry() != null) {
                workerTraining.setTrainingExpiry(workerTrainingUpdateDto.getTrainingExpiry());
            }
            if (workerTrainingUpdateDto.getComment() != null) {
                workerTraining
                        .setComment(
                                workerTrainingUpdateDto
                                        .getComment()
                        );
            }



            if (workerTrainingUpdateDto.getStatus() != null) {
                if (workerTrainingUpdateDto.getStatus().equalsIgnoreCase(TrainingStatus.APPROVED.toString())) {
                    workerTraining.setStatus(TrainingStatus.APPROVED);
                    agencyWorkerTraining.setStatus(TrainingStatus.APPROVED);
                }
                if (workerTrainingUpdateDto.getStatus().equalsIgnoreCase(TrainingStatus.REJECTED.toString())) {
                    workerTraining.setStatus(TrainingStatus.REJECTED);
                    agencyWorkerTraining.setStatus(TrainingStatus.REJECTED);
                }
            }

            agencyWorkerTrainingRepository.save(agencyWorkerTraining);
            workerTrainingRepository.save(workerTraining);

    }



    @Override
    public void addTrainingDoc(Long workerId,Long compliance, MultipartFile files) {
        WorkerTraining wc = getOne(compliance);
        String url = dataBucketUtil.uploadFile(files,"WorkerTraining",wc.getWorker().getId().toString(), WorklinkUserType.WORKER );
        wc.setDocument(url);
        workerTrainingRepository.save(wc);
    }



    @Override
    public void deleteWorkerTraining(Long id) {
        WorkerTraining training = workerTrainingRepository.findById(id).get();
        training.setDeleted(true);
        workerTrainingRepository.save(training);
    }

    @Override
    public WorkerTrainingResultDto findById(Long id) {


        return toWorkerTrainingResultDto.convert(getOne(id));
    }

    @Override
    @Transactional
    public Page<WorkerTrainingResultDto> findWorkerTrainings(Long workerId, PageRequest of) {


        Page<WorkerTrainingResultDto> iWorkerTrainingResultDto = workerTrainingRepository.findAllByWorkerAndDeletedAndTrainingExpiryGreaterThan(workerService.getOne(workerId), false,LocalDate.now().minusDays(30),of).map(toWorkerTrainingResultDto::convert);

        return iWorkerTrainingResultDto;
    }

    @Override
    public List<WorkerTrainingResultDto> findAgencyWorkerTrainings(Long workerId, Long agencyId, PageRequest of) {

        Page<WorkerTrainingResultDto> iWorkerTrainingResultDto = workerTrainingRepository.findAllByWorkerAndDeletedAndTrainingExpiryGreaterThan(workerService.getOne(workerId), false,LocalDate.now().minusDays(30),of) .map(toWorkerTrainingResultDto::convert);;

        List<WorkerTrainingResultDto> list = iWorkerTrainingResultDto.getContent();


        list.forEach(
                (train) -> {
                    try{
                    train.setStatus(agencyWorkerTrainingRepository.findAgencyWorkerTraining(workerId, agencyId, train.getId()).getStatus().toString());

                    }catch (NullPointerException e){

                        AgencyWorkerTraining trainer = new AgencyWorkerTraining();
                        trainer.setAgency(agencyService.getOne(agencyId));
                        trainer.setWorker(workerService.getOne(workerId));
                        trainer.setTraining(getOne(train.getId()));


                        train.setStatus(TrainingStatus.NEW.toString());

                    }

                }
        );

        return list;
    }



    @Override
    public Page<WorkerTraining> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public WorkerTraining save(WorkerTrainingUpdateDto workerTrainingUpdateDto) {
        return null;
    }

    @Override
    public WorkerTraining getOne(Long id) {
        return workerTrainingRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker training not found"));
    }

//    @Override
//    public WorkerTraining findByIHascoId(Long id) {
//
//        WorkerTraining res = workerTrainingRepository.findByHascoId(id);
//        return res;
//    }
}
