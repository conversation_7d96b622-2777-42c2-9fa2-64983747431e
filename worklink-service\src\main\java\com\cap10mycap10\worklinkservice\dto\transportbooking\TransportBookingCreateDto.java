package com.cap10mycap10.worklinkservice.dto.transportbooking;

import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TransportBookingCreateDto {

    private Long transportId;

    private Long agencyId;

    private TransportStatus transportStatus;

    private Set<Long> workerIds;
}
