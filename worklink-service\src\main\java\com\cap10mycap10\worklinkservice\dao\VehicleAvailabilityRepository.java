package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.model.VehicleAvailability;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;

public interface VehicleAvailabilityRepository extends JpaRepository<VehicleAvailability, Long> {

    List<VehicleAvailability> findByVehicleId(Long vehicleId);

    @Query("SELECT va.date FROM VehicleAvailability va WHERE va.vehicle.id = :vehicleId AND va.date >= :currentDate")
    List<LocalDate> findUnavailableDatesByVehicleId(@Param("vehicleId") Long vehicleId, @Param("currentDate") LocalDate currentDate);

    List<VehicleAvailability> findByVehicleIdAndDateIn(Long vehicleId, List<LocalDate> dates);

    void deleteByVehicleIdAndDateIn(Long vehicleId, List<LocalDate> dates);

    void deleteByVehicleId(Long vehicleId);
}
