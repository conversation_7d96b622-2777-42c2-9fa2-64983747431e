package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.compliance.ComplianceCreateDto;
import com.cap10mycap10.worklinkservice.dto.compliance.ComplianceUpdateDto;
import com.cap10mycap10.worklinkservice.model.Compliance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface ComplianceService {

    void addCompliance(ComplianceCreateDto complianceCreateDto);

    void deleteCompliance(Long id);

    Compliance findById(Long id);

    List<Compliance> findAll();

    Page<Compliance> findAllPaged(PageRequest of);

    Compliance save(ComplianceUpdateDto complianceUpdateDto);

    Compliance getOne(Long id);
}
