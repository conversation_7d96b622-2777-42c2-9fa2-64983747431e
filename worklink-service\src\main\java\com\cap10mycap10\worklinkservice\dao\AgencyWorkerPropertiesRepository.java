package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.agencyworkerproperties.IAgencyWorkerProperties;
import com.cap10mycap10.worklinkservice.model.AgencyWorkerProperties;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface AgencyWorkerPropertiesRepository extends JpaRepository<AgencyWorkerProperties, Long> {

    @Query(value = "select id ,\n" +
            "agency_id                 as agencyId,\n" +
            "contract_end_date         as contractEndDate,\n" +
            "dbs_expiry         as dbsExpiry,\n" +
            "dbs_number                     as dbsNumber,\n" +
            "employment_start_date as employmentStartDate,\n" +
            "expiry               as expiry,\n" +
            "next_check_date            as nextCheckDate,\n" +
            "payment_method          as paymentMethod,\n" +
            "restriction_expiry              as restrictionExpiry,\n" +
            "restrictions                  as restrictions,\n" +
            "right_to_work                 as rightToWork,\n" +
            "worker_id                 as workerId,\n" +

            "eligible, \n" +
            "proof, \n" +
            "sign_date as signDate, \n" +
            "visa, \n" +
            "visa_expiry as visaExpiry, \n" +
            "paperwork, \n" +
            "approver, \n" +
            "position, \n" +
            "comment, \n" +
            "status, \n" +
            "signed \n" +

            "from agency_worker_properties\n" +
            "where worker_id = ?1\n" +
            "   and agency_id = ?2\n"
            , nativeQuery = true)
    IAgencyWorkerProperties findAgencyWorkerProperties(Long workerId, Long agencyId);

    AgencyWorkerProperties findByWorkerIdAndAgencyId(Long workerId, Long agencyId);
}