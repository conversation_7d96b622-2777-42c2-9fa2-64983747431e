package com.cap10mycap10.worklinkservice.dto.users;

import com.cap10mycap10.worklinkservice.model.Agency;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserRequest {

    private String firstName;

    private String lastName;

    private String username;

    private String email;

    private Long agentId;

    private Long clientId;

    private Long roleId;

    public static UserRequest buildRequest(Agency agency, AdministratorCreateDto request) {
        return UserRequest.builder()
                .email(request.getEmail())
                .firstName(request.getFirstname())
                .lastName(request.getLastname())
                .username(request.getEmail())
                .roleId(3L)
                .agentId(agency.getId())
                .build();
    }


}