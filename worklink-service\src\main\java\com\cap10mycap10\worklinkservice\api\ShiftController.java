package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.shift.*;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.permissions.shift.UpdateShift;
import com.cap10mycap10.worklinkservice.service.ShiftService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;

import jakarta.validation.Valid;
import java.net.URI;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/")
public class ShiftController {


    private final ShiftService shiftService;
    private final WorkerService workerService;

    public ShiftController(ShiftService shiftService, WorkerService workerService) {
        this.shiftService = shiftService;
        this.workerService = workerService;
    }



    /*@CreateShift*/
    @PostMapping(value = "shift")
    public ResponseEntity<List<BookingResultDto>> create(@RequestBody @Valid List<ShiftCreateDto> shiftCreateDto) {
        log.info("Request to add shift with : {}", shiftCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(shiftService.save(shiftCreateDto));
    }

    /*@BookShift*/
    @PutMapping(value = "book-shift/{shiftId}/{workerId}/{agencyId}")
    public ResponseEntity book(@PathVariable("shiftId") Long shiftId,
                               @PathVariable("workerId") Long workerId,
                               @PathVariable("agencyId") Long agencyId) throws BusinessValidationException, JsonProcessingException {
        log.info("Request to book shift with : shiftId: {}, workerID: {}, agencyID: {}", shiftId, workerId, agencyId);
        shiftService.book(shiftId, agencyId, workerId);
        return ResponseEntity.ok().build();
    }

    /*@BookShift*/
    @PutMapping(value = "approve-applied-shift/{shiftId}/{workerId}/")
    public ResponseEntity book(@PathVariable("shiftId") Long shiftId,
                               @PathVariable("workerId") Long workerId) throws BusinessValidationException, JsonProcessingException {
        log.info("Request to book shift with : shiftId: {}, workerID: {}, agencyID: {}", shiftId, workerId, null);
        shiftService.approve(shiftId, null, workerId);
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "direct-book-shift/{shiftId}/{workerId}")
    public ResponseEntity bookWorkerDirectly(@PathVariable("shiftId") Long shiftId,
                                             @PathVariable("workerId") Long workerId) throws BusinessValidationException, JsonProcessingException {
        log.info("Request to directly book a worker on a shift with : shiftId: {}, workerID: {}", shiftId, workerId);
        shiftService.bookWorkerDirectly(shiftId, workerId);
        return ResponseEntity.ok().build();
    }


    @PutMapping(value = "apply-shift/{shiftId}/{workerId}/{agencyId}")
    public ResponseEntity apply(@PathVariable("shiftId") Long shiftId,
                                @PathVariable("workerId") Long workerId,
                                @PathVariable("agencyId") Long agencyId) throws BusinessValidationException {
        log.info("Request to apply shift with : shiftId: {}, workerID: {}, agencyID: {}", shiftId, workerId, agencyId);
        shiftService.apply(shiftId, agencyId, workerId);
        return ResponseEntity.ok().build();
    }

    /*@QueryShift*/
    @PutMapping(value = "query-shift/{shiftId}/{workerId}/{reason}")
    public ResponseEntity query(@PathVariable("shiftId") Long shiftId,
                                @PathVariable("workerId") Long workerId,
                                @PathVariable("reason") String reason) throws BusinessValidationException {
        log.info("Request to book shift with : shiftId: {}, workerID: {}, agencyID: {}", shiftId, workerId, reason);
        shiftService.query(shiftId, workerId, reason);
        return ResponseEntity.ok().build();
    }

    /*@CancelShift*/
    @PutMapping(value = "cancel-shift/{shiftId}/{payerId}/{reason}")
    public ResponseEntity cancel(@PathVariable("shiftId") Long shiftId,
                                 @PathVariable("payerId") Long clientId,
                                 @PathVariable("reason") String reason) throws BusinessValidationException {
        log.info("Request to cancel shift with : shiftId: {}, payerId: {}, reason: {}", shiftId, clientId, reason);
        shiftService.cancel(shiftId, clientId, reason);
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "cancel-shift-by-worker/{shiftId}/{workerId}/{reason}")
    public ResponseEntity cancelWorkerShift(@PathVariable("shiftId") Long shiftId,
                                            @PathVariable("workerId") Long workerId,
                                            @PathVariable("reason") String reason) throws BusinessValidationException {
        log.info("Request to cancel shift with : shiftId: {}, payerId: {}, reason: {}", shiftId, workerId, reason);
        shiftService.cancelWorkerShift(shiftId, workerId, reason);
        return ResponseEntity.ok().build();
    }

    /*@AuthorizeShift*/
    @PutMapping(value = "authorize-queried-shift/{shiftId}/{payerId}/{endTime}")
    public ResponseEntity authorizeQueried(
            @PathVariable("shiftId") Long shiftId,
            @PathVariable("payerId") Long clientId,
            @PathVariable("endTime") LocalTime endTime) throws BusinessValidationException {
        log.info("Request to authorize shift with : shiftID: {}, payerId {}, endtime: {}", shiftId, clientId, endTime);
        shiftService.authorizeQueried(shiftId, clientId, endTime);
        return ResponseEntity.ok().build();
    }


    /*@AuthorizeShift*/
    @PutMapping(value = "authorize-shift/{shiftId}")
    public ResponseEntity authorize(
            @PathVariable("shiftId") Long shiftId) throws BusinessValidationException {
        log.info("Request to authorize shift with : shiftID: {}", shiftId);
        shiftService.authorize(shiftId);
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "release-shift/{shiftId}/{workerId}")
    public ResponseEntity releaseShift(@PathVariable("shiftId") Long shiftId,
                               @PathVariable("workerId") Long workerId) throws BusinessValidationException, JsonProcessingException {
        log.info("Request to release shift with : shiftId: {}, workerID: {}, agencyID: {}", shiftId, workerId);
        shiftService.release(shiftId, workerId);
        return ResponseEntity.ok().build();
    }

    /*@ViewShift*/
    @GetMapping(value = "shift/{id}")
    public ResponseEntity<BookingResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request to view shift with id : {}", id);
        return ResponseEntity.ok(shiftService.findById(id));
    }

    @PutMapping(value = "shift/worker/remind-authorisation/{id}")
    public ResponseEntity<Object> authorisationReminder(@PathVariable("id") Long id) {
        log.info("Request to send authorisation reminder for shift : {}", id);
        shiftService.authorisationReminder(id);
        return ResponseEntity.ok().build();
    }

    /*@ViewShift*/
    @GetMapping(value = "shifts")
    public ResponseEntity<List<BookingResultDto>> findAll() throws FirebaseMessagingException {
        log.info("Request to view all shifts ");
        return ResponseEntity.ok(shiftService.findAll());
    }

    /* @ViewShift*/
    @GetMapping(value = "shifts-status/{page}/{size}/{status}")
    public ResponseEntity<Page<BookingResultDto>> findById(@PathVariable("page") int page,
                                                           @PathVariable("size") int size,
                                                           @PathVariable("status") String status) {
        log.info("Request to view pages shifts with : {}, {}, {}", page
                , size, status);
        return ResponseEntity.ok(shiftService.findAllPagedByStatus(PageRequest.of(page, size), status));
    }

    /*@ViewShift*/
    @GetMapping(value = "shifts/{page}/{size}")
    public ResponseEntity<Page<BookingResultDto>> findById(@PathVariable("page") int page,
                                                           @PathVariable("size") int size,
                                                           @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                           @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to view pages shifts with : {}, {}", page
                , size);
        return ResponseEntity.ok(shiftService.findAllPaged(PageRequest.of(page, size), startDate, endDate));
    }

    /*@ViewShift*/
    @GetMapping(value = "shifts/{page}/{size}/{status}")
    public ResponseEntity<Page<BookingResultDto>> findByShiftStatus(@PathVariable("page") int page,
                                                                    @PathVariable("size") int size,
                                                                    @PathVariable("status") String status,
                                                                    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to view paged  shifts by status and client : {}, {}, {}", page, size, status);
        return ResponseEntity.ok(shiftService.findAllStatusPaged(status, PageRequest.of(page, size), startDate, endDate));

    }

    /*@ViewShift*/
    @GetMapping(value = "shifts/{page}/{size}/{payerId}/{status}")
    public ResponseEntity<Page<BookingResultDto>> findByShiftStatusByClient(@PathVariable("page") int page,
                                                                            @PathVariable("size") int size,
                                                                            @PathVariable("payerId") Long clientId,
                                                                            @PathVariable("status") String status,
                                                                            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to view paged  shifts by status : {}, {}, {}, {}", page, size, clientId, status);
        return ResponseEntity.ok(shiftService.findAllStatusByClientPaged(clientId, status, PageRequest.of(page, size), startDate, endDate));
    }

//    /*@ViewShift*/
//    @GetMapping(value = "shifts/worker/{page}/{size}/{payeeId}/{workerId}")
//    public ResponseEntity<Page<BookingResultDto>> findByShiftStatusByClient(@PathVariable("page") int page,
//                                                                          @PathVariable("size") int size,
//                                                                          @PathVariable("workerId") Long workerId,
//                                                                          @PathVariable("payeeId") Long payeeId,
//                                                                          @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
//                                                                          @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
//        log.info("Request to view paged  shifts by status : {}, {}, {}, {}", page, size, payeeId, workerId);
//        return ResponseEntity.ok(shiftService.findJobCount(payeeId, workerId, PageRequest.of(page, size), startDate, endDate));
//    }


    /* @ViewShift*/
    @GetMapping(value = "shifts-clients-status/{page}/{size}/{payerId}/{status}")
    public ResponseEntity<Page<BookingResultDto>> findByShiftStatusByClientId(@PathVariable("page") int page,
                                                                              @PathVariable("size") int size,
                                                                              @PathVariable("payerId") Long clientId,
                                                                              @PathVariable("status") String status
    ) {
        log.info("Request to view paged  shifts by status : {}, {}, {}, {}", page, size, clientId, status);
        return ResponseEntity.ok(shiftService.findAllStatusByClientIdPaged(clientId, status, PageRequest.of(page, size)));
    }
    /* @ViewShift*/
//    @GetMapping(value = "shifts-clients-status/{agencyId}/{clientId}/{status}/{page}/{size}")
//    public ResponseEntity<Page<BookingResultDto>> findAgencyClientShiftsForBilling(@PathVariable("page") int page,
//                                                                              @PathVariable("size") int size,
//                                                                              @PathVariable("payerId") Long clientId,
//                                                                              @PathVariable("status") String status
//    ) {
//        log.info("Request to view paged  shifts by status : {}, {}, {}, {}", page, size, clientId, status);
//        return ResponseEntity.ok(shiftService.findAllStatusByClientIdPaged(clientId, status, PageRequest.of(page, size)));
//    }

    /* @ViewShift*/
    @GetMapping(value = "shifts/job-count/{workerId}")
    public ResponseEntity<Page<BookingResultDto>> findJobCount(@PathVariable("workerId") Long clientId) {
        log.info("Request to view paged  shifts by status : {}", clientId);
        return ResponseEntity.ok(shiftService.findJobCount(clientId));
    }

    /*@ViewShiftDashboard*/
    @GetMapping(value = "shifts-dashboard")
    public ResponseEntity<ShiftReportStatus> findAllShiftsByStatus() {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(shiftService.findAllShiftsByStatus());
    }


    /*@ViewShiftDashboard*/
    @GetMapping(value = "client-shifts-dashboard/{clientId}")
    public ResponseEntity<ShiftReportStatus> findAllClientShiftsByStatus(@PathVariable("clientId") Long clientId) {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(shiftService.findAllClientShiftsByStatus(clientId));
    }

    /*@ViewShiftDashboard*/
    @GetMapping(value = "agency-shifts-dashboard/{agencyId}")
    public ResponseEntity<ShiftReportStatus> findAllAgencyShiftsByStatus(@PathVariable("agencyId") Long agencyId) {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(shiftService.findAllAgencyShiftsByStatus(agencyId));
    }


    @GetMapping(value = "applicants/{shiftId}/{agencyId}")
    public ResponseEntity<List<WorkerResultDto>> getAgencyApplicants(@PathVariable("shiftId") Long shiftId, @PathVariable("agencyId") Long agencyId) {
        log.info("Request to get workers who applied for specific shift Id: {}", shiftId);
        return ResponseEntity.ok(workerService.getAgencyApplicantsByShiftId(shiftId, agencyId));
    }

    @GetMapping(value = "shift-applicants/{shiftId}")
    public ResponseEntity<List<WorkerResultDto>> getApplicants(@PathVariable("shiftId") Long shiftId) {
        log.info("Request to get workers who applied for specific shift Id: {}", shiftId);
        return ResponseEntity.ok(workerService.getApplicantsByShiftId(shiftId));
    }

    /*@ViewShiftDashboard*/
    @GetMapping(value = "worker-shifts-dashboard/{workerId}")
    public ResponseEntity<List<IShiftReportStatus>> findAllWorkerShiftsByStatus(@PathVariable("workerID") Long workerId) {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(shiftService.findAllWorkerShiftsByStatus(workerId));
    }


    /* @ViewShift*/
    @PostMapping(value = "client-shifts")
    public ResponseEntity<Page<BookingResultDto>> findShiftsByClientId(@RequestBody ShiftRequest shiftRequest
    ) {
        log.info("Request to get shifts by client paged with : {}, {}, {}", shiftRequest.getClientId(), shiftRequest.getPage(), shiftRequest.getSize());
        return ResponseEntity.ok(shiftService.findAllClientPaged(shiftRequest.getClientId(), PageRequest.of(shiftRequest.getPage(), shiftRequest.getSize())));
    }

//    @UpdateShift
    @PutMapping(value = "shift", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity update(@RequestBody ShiftUpdateDto shiftUpdateDto,
                                 @RequestParam(value = "message", required = false) String message) {
        log.info("Request to edit shift with : {}", shiftUpdateDto);

        if (message != null) {
            shiftService.save(shiftUpdateDto, message);
        } else {
            shiftService.save(shiftUpdateDto);
        }
        return ResponseEntity.ok().build();
    }

    /*@DeleteShift*/
    @DeleteMapping(value = "shift/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete shift with id : {}", id);


        shiftService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("shift-filter/{workerId}/{page}/{size}")
    public ResponseEntity<List<BookingResultDto>> searchShift(@RequestParam(value = "payeeId", required = false) Long agentId,
                                                              @RequestParam(value = "payerId", required = false) Long clientId,
                                                              @RequestParam(value = "location", required = false) String location,
                                                              @RequestParam(value = "startDate", required = true) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                              @RequestParam(value = "endDate", required = true) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
                                                              @RequestParam(value = "status", required = true) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) String status,
                                                              @PathVariable("workerId") Long workerId,
                                                              @PathVariable int page, @PathVariable int size) {

        if (agentId != null && clientId != null) {
            if (location == null) {
                return ResponseEntity.ok(shiftService.filterByClientAndAgent(workerId, agentId, clientId, startDate, endDate, status, PageRequest.of(page, size)));
            } else {
                return ResponseEntity.ok(shiftService.filterByClientAndAgent(workerId, agentId, clientId, location, startDate, endDate, status, PageRequest.of(page, size)));

            }
        }
        if (agentId == null && clientId != null) {
            if (location == null) {
                return ResponseEntity.ok(shiftService.filterByClient(workerId, clientId, startDate, endDate, status, PageRequest.of(page, size)));
            } else {
                return ResponseEntity.ok(shiftService.filterByClient(workerId, clientId, location, startDate, endDate, status, PageRequest.of(page, size)));

            }
        }

        if (agentId != null && clientId == null) {
            if (location == null) {
                return ResponseEntity.ok(shiftService.filterByAgent(workerId, agentId, startDate, endDate, status, PageRequest.of(page, size)));
            } else {
                return ResponseEntity.ok(shiftService.filterByAgent(workerId, agentId, location, startDate, endDate, status, PageRequest.of(page, size)));

            }
        }

        if (location == null) {
            return ResponseEntity.ok(shiftService.filter(workerId, startDate, endDate, status, PageRequest.of(page, size)));
        } else {
            return ResponseEntity.ok(shiftService.filter(workerId, location, startDate, endDate, status, PageRequest.of(page, size)));
        }
    }

    @GetMapping(value = "shifts/compliance/issues/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<IShiftCompliance>> findShiftComplianceIssues(@PathVariable("page") int page,
                                                           @PathVariable("size") int size,
                                                           @PathVariable("agencyId") Long agencyId) {
        return ResponseEntity.ok(shiftService.findShiftComplianceIssues(agencyId,PageRequest.of(page, size)));
    }

    @GetMapping(value = "shift/pooling/workers/{shiftId}")
    public ResponseEntity<List<ShiftCarPoolingDto>> carPoolingWorkerList(@PathVariable("shiftId") Long shiftId){

        log.info("Request to get a list of workers for car pooling for shift with id: {}", shiftId);
        return ResponseEntity.ok(shiftService.findAllWorkersForShiftCarPooling(shiftId));
    }
}
