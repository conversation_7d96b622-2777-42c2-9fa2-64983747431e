package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AssignmentCodeRateRepository;
import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateCreateDto;
import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateResultDto;
import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateUpdateDto;

import com.cap10mycap10.worklinkservice.enums.BookingType;
import com.cap10mycap10.worklinkservice.mapper.assignmentcoderate.AssignmentCodeRateDtoToAssignmentCodeRate;
import com.cap10mycap10.worklinkservice.mapper.assignmentcoderate.AssignmentCodeRateToAssignmentCodeRateResultDto;
import com.cap10mycap10.worklinkservice.model.AssignmentRate;
import com.cap10mycap10.worklinkservice.model.Location;
import com.cap10mycap10.worklinkservice.model.ShiftDirectorate;
import com.cap10mycap10.worklinkservice.model.ShiftType;
import com.cap10mycap10.worklinkservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class AssignmentCodeRateServiceImpl implements AssignmentCodeRateService {

    private final AssignmentCodeRateRepository assignmentCodeRateRepository;
    private final AssignmentCodeRateToAssignmentCodeRateResultDto toAssignmentCodeRateResultDto;
    private final AssignmentCodeRateDtoToAssignmentCodeRate toAssignmentCodeRate;
    private final ClientService clientService;
    private final AssignmentCodeService assignmentCodeService;
    private final AgencyService agencyService;
    private final ShiftTypeService shiftTypeService;
    private final LocationService locationService;
    private final ShiftDirectorateService shiftDirectorateService;


    public AssignmentCodeRateServiceImpl(AssignmentCodeRateRepository assignmentCodeRateRepository, AssignmentCodeRateToAssignmentCodeRateResultDto toAssignmentCodeRateResultDto, AssignmentCodeRateDtoToAssignmentCodeRate toAssignmentCodeRate, ClientService clientService, AssignmentCodeService assignmentCodeService, AgencyService agencyService, ShiftTypeService shiftTypeService, LocationService locationService, ShiftDirectorateService shiftDirectorateService) {
        this.assignmentCodeRateRepository = assignmentCodeRateRepository;
        this.toAssignmentCodeRateResultDto = toAssignmentCodeRateResultDto;
        this.toAssignmentCodeRate = toAssignmentCodeRate;
        this.clientService = clientService;
        this.assignmentCodeService = assignmentCodeService;
        this.agencyService = agencyService;
        this.shiftTypeService = shiftTypeService;
        this.locationService = locationService;
        this.shiftDirectorateService = shiftDirectorateService;
    }

    @Override
    public AssignmentCodeRateResultDto save(AssignmentCodeRateCreateDto assignmentCodeRateCreateDto) {
        AssignmentRate assignmentRate = new AssignmentRate();

        ShiftType shiftType = shiftTypeService.getOne(assignmentCodeRateCreateDto.getShiftTypeId());

        if(shiftType.getBookingType()== BookingType.SHIFT) {
            ShiftDirectorate shiftDirectorate = shiftDirectorateService.getOne(assignmentCodeRateCreateDto.getDirectorateId());
            Location location = shiftDirectorate.getLocation();
            assignmentRate.setLocation(location);
            assignmentRate.setShiftDirectorate(shiftDirectorate);
        }
        assignmentRate.setShiftType(shiftType);
        assignmentRate.setAssignmentCode(assignmentCodeService.getOne(assignmentCodeRateCreateDto.getAssignmentCodeId()));
        assignmentRate.setClient(clientService.getOne(assignmentCodeRateCreateDto.getClientId()));
        assignmentRate.setAgent(agencyService.getOne(assignmentCodeRateCreateDto.getAgencyId()));
        assignmentRate.setDayOfTheWeek(assignmentCodeRateCreateDto.getDayOfWeek());
        assignmentRate.setEndTime(assignmentCodeRateCreateDto.getEndTime());
        assignmentRate.setStartTime(assignmentCodeRateCreateDto.getStartTime());
        assignmentRate.setClientRate(assignmentCodeRateCreateDto.getClientRate());
        assignmentRate.setPayeRate(assignmentCodeRateCreateDto.getPayeRate());
        assignmentRate.setPrivateRate(assignmentCodeRateCreateDto.getPrivateRate());
        assignmentRate.setUmbrellaRate(assignmentCodeRateCreateDto.getUmbrellaRate());

        log.info("Assignment rate {}", assignmentRate.toString());

        return toAssignmentCodeRateResultDto.convert(assignmentCodeRateRepository.save(assignmentRate));
    }

    @Override
    public AssignmentCodeRateResultDto save(AssignmentCodeRateUpdateDto assignmentCodeRateUpdateDto) {

        AssignmentRate assignmentRate = getOne(assignmentCodeRateUpdateDto.getId());
        assignmentRate.setAgent(agencyService.getOne(assignmentCodeRateUpdateDto.getAgencyId()));
        assignmentRate.setClient(clientService.getOne(assignmentCodeRateUpdateDto.getClientId()));
        assignmentRate.setAssignmentCode(assignmentCodeService.getOne(assignmentCodeRateUpdateDto.getAssignmentCodeId()));
        assignmentRate.setShiftType(shiftTypeService.getOne(assignmentCodeRateUpdateDto.getShiftTypeId()));
        assignmentRate.setDayOfTheWeek(assignmentCodeRateUpdateDto.getDayOfWeek());
        assignmentRate.setEndTime(assignmentCodeRateUpdateDto.getEndTime());
        assignmentRate.setStartTime(assignmentCodeRateUpdateDto.getStartTime());
        assignmentRate.setClientRate(assignmentCodeRateUpdateDto.getClientRate());
        assignmentRate.setPayeRate(assignmentCodeRateUpdateDto.getPayeRate());
        assignmentRate.setPrivateRate(assignmentCodeRateUpdateDto.getPrivateRate());
        assignmentRate.setUmbrellaRate(assignmentCodeRateUpdateDto.getUmbrellaRate());

        return toAssignmentCodeRateResultDto.convert(assignmentCodeRateRepository.save(assignmentRate));
    }

    @Override
    public AssignmentCodeRateResultDto findById(Long id) {

        return toAssignmentCodeRateResultDto.convert(getOne(id));
    }

    @Override
    public List<AssignmentCodeRateResultDto> findAll() {
        return assignmentCodeRateRepository.findAll()
                .stream()
                .map(toAssignmentCodeRateResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Page<AssignmentCodeRateResultDto> findAllPaged(PageRequest of) {

        return assignmentCodeRateRepository.findAll(of)
                .map(toAssignmentCodeRateResultDto::convert);
    }

    @Override
    public void deleteById(Long id) {
        AssignmentRate assignmentRate = assignmentCodeRateRepository.findById(id).get();
        try {
            assignmentCodeRateRepository.delete(getOne(id));
            assignmentCodeRateRepository.flush();
        } catch (Exception ex) {
            throw new BusinessValidationException("Rate cannot be deleted because it references an existing record");
        }
    }

    @Override
    public AssignmentRate getOne(Long id) {
        return assignmentCodeRateRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Assignment Rate does not exist"));
    }

    @Override
    public List<AssignmentRate> findAssignmentRate(Long clientId, Long directorateId, Long shiftTypeId, String dayOfWeek, Long assignmentCode) {
        return assignmentCodeRateRepository.findByClientIdAndShiftDirectorateIdAndShiftTypeIdAndDayOfTheWeekAndAssignmentCodeId(
                 clientId,  directorateId,  shiftTypeId,  dayOfWeek,  assignmentCode
        );
    }

    @Override
    public List<AssignmentCodeRateResultDto> findAllByAgentId(Long agentId) {
        return assignmentCodeRateRepository.findByAgent_Id(agentId)
                .stream()
                .map(toAssignmentCodeRateResultDto::convert)
                .collect(Collectors.toList());
    }
    @Override
    public List<AssignmentCodeRateResultDto> findAllByClientId(Long clientId) {
        return assignmentCodeRateRepository.findByClient_Id(clientId)
                .stream()
                .map(toAssignmentCodeRateResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public List<AssignmentCodeRateResultDto> findAllByAgentId(Long agentId, String day) {
        return assignmentCodeRateRepository.findByAgent_IdAndDayOfTheWeek(agentId, day)
                .stream()
                .map(toAssignmentCodeRateResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void saveAll(List<AssignmentCodeRateCreateDto> assignmentCodeCreateDtos) {
        List<AssignmentRate> assignmentRates = new ArrayList<>();
        for (AssignmentCodeRateCreateDto assCode:assignmentCodeCreateDtos
             ) {
            AssignmentRate assignmentRate = new AssignmentRate();
            Location location = locationService.getOne(assCode.getLocationId());
            ShiftDirectorate shiftDirectorate = shiftDirectorateService.getOne(assCode.getDirectorateId());
            assignmentRate.setAssignmentCode(assignmentCodeService.getOne(assCode.getAssignmentCodeId()));
            assignmentRate.setClient(clientService.getOne(assCode.getClientId()));
            assignmentRate.setAgent(agencyService.getOne(assCode.getAgencyId()));
            assignmentRate.setShiftType(shiftTypeService.getOne(assCode.getShiftTypeId()));
            assignmentRate.setDayOfTheWeek(assCode.getDayOfWeek());
            assignmentRate.setEndTime(assCode.getEndTime());
            assignmentRate.setStartTime(assCode.getStartTime());
            assignmentRate.setClientRate(assCode.getClientRate());
            assignmentRate.setPayeRate(assCode.getPayeRate());
            assignmentRate.setPrivateRate(assCode.getPrivateRate());
            assignmentRate.setUmbrellaRate(assCode.getUmbrellaRate());
            assignmentRate.setLocation(location);
            assignmentRate.setShiftDirectorate(shiftDirectorate);

            log.info("Assignment rate {}", assignmentRate.toString());
            assignmentRates.add(assignmentRate);
        }
        assignmentCodeRateRepository.saveAll(assignmentRates);
    }
}
