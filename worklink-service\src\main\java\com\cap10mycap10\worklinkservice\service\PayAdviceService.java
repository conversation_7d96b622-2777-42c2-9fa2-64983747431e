package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.payadvice.PayAdviceResult;
import com.cap10mycap10.worklinkservice.model.PayAdvice;
import com.cap10mycap10.worklinkservice.reports.ReportFormat;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;

public interface PayAdviceService {

    void  createPayAdvice(HttpServletRequest servletRequest, Long id,  Long agentId,String payDate, List<Long> shiftId) throws Exception;

    PayAdviceResult findPayAdvice(Long payAdviceId);

    Page<PayAdviceResult> getAllPayAdvices(Integer page, Integer size);

    Page<PayAdviceResult> getAllPayAdvices(Long workerId, Integer page, Integer size);
    Page<PayAdviceResult> findAllByWorkerIdAndAgencyId(Long workerId,Long agenyId, Integer page, Integer size);


    void acknowledgePayment(Long payAdviceId, String paymentRef);

    void sendPOP(Long payAdviceId, String paymentRef);

    PayAdvice findPayAdviceById(Long id);
    Page<PayAdviceResult> findWorkerPayAdvices(Long workerId, PageRequest of);


    Page<PayAdviceResult> getAllPayAdvicesForAgent(Long agencyId, Integer page, Integer size);
    Page<PayAdviceResult> getAllFilteredPayAdvicesForAgent(Long agencyId, Long workerId, LocalDate startDate, LocalDate endStart, Integer page, Integer size);

    ResponseEntity<Resource> downloadPayAdvice(Long payAdviceId, ReportFormat format, HttpServletRequest servletRequest);
    ResponseEntity<Resource> downloadBacsPaymentCsv(LocalDate date, Long agencyId, HttpServletRequest servletRequest);
    void uploadBacsPaymentCsv(MultipartFile file);

    PayAdvice getOne(Long id);

    void delete(Long adviceId);
}
