package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.billing.VatRateCreateDto;
import com.cap10mycap10.worklinkservice.dto.billing.VatRateDto;
import com.cap10mycap10.worklinkservice.model.VatRate;
import com.cap10mycap10.worklinkservice.service.VatRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class VatRateController {

    private final VatRateService vatRateService;

    public VatRateController(VatRateService vatRateService) {
        this.vatRateService = vatRateService;
    }

    /* @CreateTaxRate*/
    @PostMapping(value = "vat-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity create(@RequestBody VatRateCreateDto vatRateDto) {
        log.info("Request to add vat rate with : {}", vatRateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        vatRateService.save(vatRateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    /*@ViewVatRateById*/
    @GetMapping(value = "vat-rate/{id}")
    public ResponseEntity<VatRate> findById(@PathVariable("id") Long id) {
        log.info("Request to get vat rate with id : {}", id);
        return ResponseEntity.ok(vatRateService.findById(id));
    }

    /*@ViewAllVatRates*/
    @GetMapping(value = "vat-rate")
    public ResponseEntity<List<VatRate>> findAll() {
        log.info("Request to get all charge rate");
        return ResponseEntity.ok(vatRateService.findAll());
    }

    /* @ViewPagenatedVatRate*/
    @GetMapping(value = "vat-rate/{page}/{size}")
    public ResponseEntity<Page<VatRate>> findById(@PathVariable("page") int page,
                                                     @PathVariable("size") int size) {
        log.info("Request to get paged charge rates with : {}, {}", page, size);
        return ResponseEntity.ok(vatRateService.findAllPaged(PageRequest.of(page, size)));
    }

    /* @UpdateVatRate*/
    @PutMapping(value = "vat-rate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity update(@RequestBody VatRateDto vatRateDto) {
        log.info("Request to update vat rate with : {}", vatRateDto);
        vatRateService.update(vatRateDto);
        return ResponseEntity.ok(HttpStatus.CREATED);
    }

    /*@DeleteVatRate*/
    @DeleteMapping(value = "vat-rate/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete vat rate with id  : {}", id);
        vatRateService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
