package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.transportbooking.TransportApplicantsResultDto;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Set;

public interface TransportBookingService {
    BookingResultDto workerApply(Long workerId, Long transportId);
    TransportApplicantsResultDto approveBooking(Long transportBookingId);
    void rejectBooking(Long transportBookingId);
    Set<BookingResultDto> findWokerBookingByStatus(ShiftStatus transportStatus, Long workerId);
    Set<BookingResultDto> findNewBookingsForWorker(Long workerId);
    Page<TransportApplicantsResultDto> agencyTransportBookings(Long agencyId, Long transportId, TransportStatus transportStatus, PageRequest of);
    BookingResultDto bookWorkerDirectly( Long workerId, Long agencyId);
    String authorizeTransportBooking(Long transportBookingId);
    TransportApplicantsResultDto cancelTransportBooking(Long transportBookingId, String cancelReason);

    String authorizationReminder(Long transportBookingId, boolean bool);
}
