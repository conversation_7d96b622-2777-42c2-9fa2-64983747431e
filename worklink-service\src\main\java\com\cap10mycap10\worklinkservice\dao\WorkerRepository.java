package com.cap10mycap10.worklinkservice.dao;


import com.cap10mycap10.worklinkservice.dto.workerappliedshift.WorkerAppliedShiftRawResultDto;
import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.WorkerStatus;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Worker;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;
import java.util.Set;

public interface WorkerRepository extends JpaRepository<Worker, Long> {

    @Query(value = "insert into worker(email, firstname, phone_number, status, lastname, assignment_code_id, created_by,version, postcode, address)" +
            "values(?1, ?2,?3,?4,?5,?6,?7,?8, ?9, ?10)", nativeQuery = true)
    void save(String email, String firstname, String phoneNumber, String status, String lastname, Long assignmentCode, String createdBy
            , Long version, String postcode, String address);

    @Query(value = "select email from agency", nativeQuery = true)
    List<String> findWorkerEmail();

    @Query(value = "select count(*) from worker", nativeQuery = true)
    int findNumberOfWorkers();


    @Query(value = "select  worker.* \n" +
            "            from worker\n" +
            "            INNER join assignment_code s on s.id = worker.assignment_code_id\n" +
            "            inner join agency_worker on worker.id = agency_worker.worker_id\n" +
            "            INNER join agency_worker_properties p on worker.id = p.worker_id and p.agency_id = ?1 and p.status <> 'APPLICANT'\n" +
            "            where agency_worker.agency_id = ?1  order by firstname asc", nativeQuery = true)
    List<Worker> findWorkerForAnAgency(Long id);

    @Query(value = "select  *\n" +
            "            from worker\n" +
            "            INNER join assignment_code s on s.id = worker.assignment_code_id\n" +
            "            inner join agency_worker on worker.id = agency_worker.worker_id\n" +
            "            INNER join agency_worker_properties p on worker.id = p.worker_id and p.agency_id = ?1 and p.status <> 'APPLICANT'\n" +
            "            where agency_worker.agency_id = ?1  order by firstname asc", nativeQuery = true)
    Page<Worker> findWorkersByAgency(Long id, PageRequest of);





    @Query(value = "select  worker.id as id, worker.created_by as createdBy, email, firstname, lastname, gender, phone_number as phoneNumber, p.status as status,p.status as agencyStatus, s.city as assignmentName, s.code as assignmentCode, postcode, address\n" +
            "            from worker\n" +
            "            INNER join assignment_code s on s.id = worker.assignment_code_id\n" +
            "            inner join agency_worker on worker.id = agency_worker.worker_id\n" +
            "            inner join agency_worker_properties p on worker.id = p.worker_id and p.agency_id = ?1 and p.status <> 'APPLICANT'\n" +
            "            where agency_worker.agency_id = ?1 and (worker.firstname LIKE %?2% OR worker.email LIKE %?2%) order by firstname asc", nativeQuery = true)
    Page<Worker> searchWorkerForAnAgency(Long id, String searchCriteria, PageRequest of);


    @Query(value = "select  worker.* \n" +
            "            from worker\n" +
            "            INNER join assignment_code s on s.id = worker.assignment_code_id\n" +
            "            inner join agency_worker on worker.id = agency_worker.worker_id\n" +
            "            left OUTER join agency_worker_properties p on worker.id = p.worker_id and p.agency_id = ?1 \n" +
            "            where agency_worker.agency_id = ?1 order by firstname asc", nativeQuery = true)
    Page<Worker> findApplicantForAnAgency(Long id, PageRequest of);

    @Query(value = "select  worker.*\n" +
            "            from worker\n" +
            "            INNER join assignment_code s on s.id = worker.assignment_code_id\n" +
            "            inner join agency_worker on worker.id = agency_worker.worker_id\n" +
            "            left OUTER join agency_worker_properties p on worker.id = p.worker_id and p.agency_id = ?1 \n" +
            "            where agency_worker.agency_id = ?1 and (worker.firstname LIKE %?2% OR worker.email LIKE %?2%) order by firstname asc", nativeQuery = true)
    Page<Worker> searchApplicantForAnAgency(Long id, String searchCriteria, PageRequest of);



    @Query(value = "select  worker.id as id, worker.created_by as createdBy, email, firstname, lastname, worker.gender, phone_number as phoneNumber, p.status as status, s.city as assignmentName, s.code as assignmentCode, postcode, address\n" +
            "  from worker\n" +
            "  INNER join assignment_code s on s.id = worker.assignment_code_id\n" +
            "  inner join agency_worker on worker.id = agency_worker.worker_id\n" +
            "  inner join agency_worker_properties p on worker.id = p.worker_id and p.agency_id = ?1 and p.status <> 'APPLICANT'\n" +
            "  where agency_worker.agency_id = ?1  and worker.id IN " +
            "(" +
            "    SELECT worker_id\n" +
            "    FROM shift\n" +
            "    where worker.id = shift.worker_id and shift.released = true\n" +
            "    and shift_worker_status IS NULL  \n" +
            "    and shift_status in ('AUTHORIZED','BILLED')  \n" +
            ") " +
            "order by firstname asc", nativeQuery = true)

    Page<Worker> findWorkerForAnAgencyPendingShifts(Long agencyId, ShiftStatus status, PageRequest of);














    @Query(value = "select * from worker where id  in (\n" +
            "select worker_id from agency_worker\n" +
            "inner join agency_client ac on agency_worker.agency_id = ac.agency_id\n" +
            "where client_id = ?1)\n" +
            "and assignment_code_id =?2\n" +
            "and gender =?3 order by lastname asc", nativeQuery = true)
    Page<Worker> findMyWorkerUnderClientAgency(Long id, Long clientId, String gender, PageRequest of);


    @Query(value = "select * from worker where id  in (\n" +
            "select worker_id from agency_worker\n" +
            "inner join agency_client ac on agency_worker.agency_id = ac.agency_id\n" +
            "where client_id = ?1)\n" +
            "and assignment_code_id =?2 order by lastname asc\n", nativeQuery = true)
    Page<Worker> findMyWorkerUnderClientAgency(Long clientId, Long assignmentCodeId, PageRequest of);



    @Query(value = "select worker.* from worker " +
            "INNER join assignment_code s on s.id = worker.assignment_code_id where worker.id  in (\n" +
            "    select worker_id from agency_worker_properties\n" +
            "  inner join agency_client ac on agency_worker_properties.agency_id = ac.agency_id\n" +
            "    where client_id = ?1 and agency_worker_properties.status = 'APPROVED') order by firstname asc", nativeQuery = true)
    Page<Worker> findClientWorkers(Long clientID, PageRequest of);

    @Query(value = "select worker.* from worker " +
            "INNER join assignment_code s on s.id = worker.assignment_code_id where worker.id  in (\n" +
            "    select worker_id from agency_worker_properties\n" +
            "  inner join agency_client ac on agency_worker_properties.agency_id = ac.agency_id\n" +
            "    where client_id = ?1 and agency_worker_properties.status = 'APPROVED') and (worker.firstname LIKE %?2% OR worker.email LIKE %?2%) order by firstname asc", nativeQuery = true)
    Page<Worker> searchClientWorkers(Long clientID, String searchCriteria, PageRequest of);

    @Query(value = "select worker.* from worker\n" +
            " INNER join assignment_code s on s.id = ?2 where worker.id in (\n" +
            "              select worker_id from agency_worker\n" +
            "                where agency_id = ?1\n" +
            "               )\n" +
            "            and assignment_code_id = ?2 order by firstname asc", nativeQuery = true)
    List<Worker> findByAgencyIdAndAssignmentCode(Long agencyId, Long assignmentCodeId);


    @Query(value = "select worker.* from worker\n" +
            " INNER join assignment_code s on s.id = ?2  where worker.id in (\n" +
            "              select worker_id from agency_worker\n" +
            "                where agency_id = ?1\n" +
            "               )\n" +
            "            and assignment_code_id = ?2 and gender =?3 order by firstname asc", nativeQuery = true)
    List<Worker> findByAgencyIdAndAssignmentCode(Long agencyId, Long assignmentCodeId, String gender);


    @Query(value = "select * from worker where id in (select worker_id from worker_applied_shift where shift_id = ?1 and agency_id = ?2)",
            nativeQuery = true)
    List<Worker> findWorkerOnShiftAndAgency(Long shiftId, Long agencyId);

    @Query(value = "select * from worker where id in (select worker_id from worker_applied_shift where shift_id = ?1)",
            nativeQuery = true)
    List<Worker> findWorkersOnShift(Long shiftId);

    @Query(value = "select id, worker_id as workerId, agency_id as agencyId, shift_id as shiftId from worker_applied_shift where shift_id = ?1",
            nativeQuery = true)
    List<WorkerAppliedShiftRawResultDto> findApplicants(Long shiftId);


    Worker findByHascoId(Long id);
    Worker findByEmail(String email);


    Worker findByDeputyId(Long workerId);

    List<Worker> findAllByAgencySetAndGender(Agency agency, Gender gender);

    Set<Worker> findAllByAgencySetIdAndPropertiesStatus(Long agencyId, WorkerStatus workerStatus);
}
