package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.Client;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ClientRepository extends JpaRepository<Client, Long> {

    @Query(value = "select count(*) from client", nativeQuery = true)
    int findNumberOfClients();


    @Query(value = "select distinct client.*" +
            "from client\n" +
            "         right join agency_client ac on client.id = ac.client_id\n" +
            "         right join services s on s.id = client.service_id\n" +
            "where ac.agency_id in (\n" +
            "    select agency_id from agency_worker where worker_id = ?1\n" +
            "    )\n" +
            "order by city asc", nativeQuery = true)
    Page<Client> findAllClientsPagedByWorkerId(Long workerId, Pageable of);


    @Query(value = "select  *\n" +
            "            from client\n" +
            "            inner join agency_client on client.id = agency_client.client_id\n" +
            "            where agency_client.agency_id = ?1 and (client.name LIKE %?2% OR client.email LIKE %?2%) order by name asc", nativeQuery = true)
    Page<Client> searchClientForAgency(Long agencyId, String searchCriteria, Pageable of);

    @Query(value = "select * from client where (?1 is null or name like %?1% or email like %?1% or telephone like %?1%)", nativeQuery = true)
    Page<Client> findClientsWithFilters(String searchQuery, Pageable of);

    Client findByEmail(String bemail);

    @Query(value = "select c.* from client c inner join agency_client ac on c.id = ac.client_id where ac.agency_id = ?1 order by c.name asc", nativeQuery = true)
    Page<Client> findClientsByAgencyId(Long agencyId, Pageable pageable);

    @Query(
            value = "SELECT DISTINCT c.* FROM client c " +
                    "INNER JOIN agency_client ac ON c.id = ac.client_id " +
                    "WHERE (?1 IS NULL OR ac.agency_id = ?1) " +
                    "AND (?2 IS NULL OR c.name LIKE %?2% OR c.email LIKE %?2% OR c.telephone LIKE %?2%) " +
                    "ORDER BY c.name ASC",

            countQuery = "SELECT COUNT(DISTINCT c.id) FROM client c " +
                    "INNER JOIN agency_client ac ON c.id = ac.client_id " +
                    "WHERE (?1 IS NULL OR ac.agency_id = ?1) " +
                    "AND (?2 IS NULL OR c.name LIKE %?2% OR c.email LIKE %?2% OR c.telephone LIKE %?2%)",
            nativeQuery = true
    )
    Page<Client> findClientsByAgencyIdAndSearchQuery(Long agencyId, String searchQuery, Pageable pageable);

}
