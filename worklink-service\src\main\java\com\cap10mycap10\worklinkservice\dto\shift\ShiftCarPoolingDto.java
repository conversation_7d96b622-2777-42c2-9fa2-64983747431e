package com.cap10mycap10.worklinkservice.dto.shift;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShiftCarPoolingDto {

    private String firstName;

    private String lastName;

    private Long workerId;

    private String postCode;

    private String gender;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime carPoolingTime;

    private String carPoolingLocation;
}
