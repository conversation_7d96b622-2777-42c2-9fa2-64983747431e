package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.DeviceRepository;
import com.cap10mycap10.worklinkservice.model.Device;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.dto.device.DeviceWorkerUpdateDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.service.DeviceService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

import static java.util.Objects.nonNull;


@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class DeviceServiceImpl implements DeviceService {
    private final WorkerServiceImpl workerService;
    private final DeviceRepository    deviceRepository;

    public DeviceServiceImpl(WorkerServiceImpl workerService, DeviceRepository deviceRepository) {
        this.workerService = workerService;
        this.deviceRepository = deviceRepository;
    }


    @Override
    @Transactional
    public void save(DeviceWorkerUpdateDto deviceWorkerUpdateDto) throws JsonProcessingException {
        
        Device device = new Device();

        device.setWorker(workerService.getOne(deviceWorkerUpdateDto.getWorkerId()));
        device.setFcmToken(deviceWorkerUpdateDto.getFcmToken());
        Device exists = deviceRepository.findByFcmToken(deviceWorkerUpdateDto.getFcmToken());

        if(!nonNull(exists)) {
            try {
                deviceRepository.save(device);
            } catch (Exception e) {
                throw new BusinessValidationException("Device already saved.");
            }
        }


    }

    @Override
    public Set<Device> getWorkerDevices(Long workerId) {
        Worker worker = workerService.getOne(workerId);
        return worker.getDevices();
        
    }

    public Device getOne(Long id) {
        return deviceRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Shift not found"));
    }

    @Override
    public void delete(Device device) {
        deviceRepository.delete(device);
    }
    
}
