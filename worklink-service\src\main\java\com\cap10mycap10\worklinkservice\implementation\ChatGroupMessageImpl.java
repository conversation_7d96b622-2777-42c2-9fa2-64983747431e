package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.dao.ChatGroupMessageRepository;
import com.cap10mycap10.worklinkservice.dao.ChatGroupRepository;
import com.cap10mycap10.worklinkservice.dto.chatgroupmessage.ChatGroupMessageRequest;
import com.cap10mycap10.worklinkservice.enums.MessageType;
import com.cap10mycap10.worklinkservice.model.ChatGroup;
import com.cap10mycap10.worklinkservice.model.ChatGroupMessage;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.ChatGroupMessageService;
import com.cap10mycap10.worklinkservice.service.ChatGroupService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ChatGroupMessageImpl implements ChatGroupMessageService {

    private final SimpMessagingTemplate messagingTemplate;
    private final ChatGroupMessageRepository chatGroupMessageRepository;
    private final ChatGroupService chatGroupService;
    private final WorkerService workerService;

    @Override
    public ChatGroupMessage sendChatGroupMessage(ChatGroupMessageRequest chatGroupMessageRequest) {
//        Worker worker = workerService.getOne(chatGroupMessageRequest.getSenderId());
//        ChatGroup chatGroup = chatGroupService.findById(chatGroupMessageRequest.getGroupId()).orElseThrow(
//                () -> new RuntimeException("Chat group not found")
//        );
//        ChatGroupMessage chatGroupMessage = ChatGroupMessage.builder()
//                .chatGroup(chatGroup)
//                .messageSentAt(LocalDateTime.now())
//                .messageType(MessageType.valueOf(chatGroupMessageRequest.getMessageType()))
//                .sender(worker)
//                .content(chatGroupMessageRequest.getContent())
//                .build();
//        chatGroupMessageRepository.save(chatGroupMessage);
//        chatGroup.getMessages().add(chatGroupMessage);
//
//        messagingTemplate.convertAndSend("/topic/group/" + chatGroupMessage.getChatGroup().getId(), chatGroupMessage);
//        log.info("Message successfully sent by :: {} with content :: {}", worker.getFirstname(), chatGroupMessage.getContent() + "to group " + chatGroup.getGroupName());

//        return chatGroupMessage;

        return null;
    }

    @Override
    public List<ChatGroupMessage> getChatGroupMessageByGroup_Id(Long groupId) {
        return chatGroupMessageRepository.findByChatGroupId(groupId);
    }

}



