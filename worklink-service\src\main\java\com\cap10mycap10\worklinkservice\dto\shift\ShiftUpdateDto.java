package com.cap10mycap10.worklinkservice.dto.shift;

import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data

public class ShiftUpdateDto {

    private Long id;

    private Long shiftLocationId;

    private Long shiftDirectorateId;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime start;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime end;

    private LocalTime shiftStartTime;

    private LocalTime shiftEndTime;

    private String breakTime;

//    private Gender gender;

//    private Long shiftType;

    private Long assignmentCode;

    private String notes;
    private String queryResponse;

    private Boolean showNoteToFw;
    private Boolean noneAttendance;

    private Boolean showNoteToAgency;

    private int hoursBeforeBroadcasting;

    private ShiftStatus shiftStatus;
//
//    private Client payerId;
//
//    private List<Long> agencyList;
}
