//package com.cap10mycap10.worklinkservice.mapper.worker;
//
//
//import com.cap10mycap10.worklinkservice.dto.worker.IWorker;
//import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
//import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
//import com.fasterxml.jackson.databind.JavaType;
//import com.fasterxml.jackson.databind.type.TypeFactory;
//import com.fasterxml.jackson.databind.util.Converter;
//import org.springframework.stereotype.Component;
//
//@Component
//public class IWorkerToWorkerResultDto implements Converter<IWorker, WorkerResultDto> {
//
//    private final AgencyToAgencyResultDto agencyToAgencyResultDto;
//
//    public IWorkerToWorkerResultDto(AgencyToAgencyResultDto agencyToAgencyResultDto) {
//        this.agencyToAgencyResultDto = agencyToAgencyResultDto;
//    }
//
//    @Override
//    public WorkerResultDto convert(IWorker worker) {
//        WorkerResultDto resultDto = new WorkerResultDto();
//        resultDto.setId(worker.getId());
//        resultDto.setFirstname(worker.getFirstname());
//        resultDto.setLastname(worker.getLastname());
//        resultDto.setEmail(worker.getEmail());
////        resultDto.setAssignmentCodeName(worker.getAssignmentCodeName().getCity());
//        resultDto.setGender(worker.getGender());
//        resultDto.setPhoneNumber(worker.getPhoneNumber());
//        resultDto.setUsername(worker.getFirstname());
////        resultDto.setEmploymentNumber(worker.getEmploymentNumber());
////        resultDto.setNationality(worker.getNationality());
////        resultDto.setCv(worker.getCv());
////        resultDto.setDob(worker.getDob());
//        resultDto.setPostcode(worker.getPostcode());
//        resultDto.setAddress(worker.getAddress());
////        resultDto.setProfilePic(worker.getProfilePic());
////        resultDto.setCreatedBy(worker.getCreatedBy());
//        //resultDto.setAgencyName(worker.getAgency().getCity());
////        resultDto.setAssignmentCodeId(worker.getAssignmentCodeName().getId());
////        resultDto.setAssignmentName(worker.getAssignmentCodeName().getCity());
////        resultDto.setAssignmentCodeName(worker.getAssignmentCodeName().getCode());
//
//        return resultDto;
//    }
//
//    @Override
//    public JavaType getInputType(TypeFactory typeFactory) {
//        return null;
//    }
//
//    @Override
//    public JavaType getOutputType(TypeFactory typeFactory) {
//        return null;
//    }
//}
