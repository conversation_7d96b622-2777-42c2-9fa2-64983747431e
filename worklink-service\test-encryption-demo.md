# SMTP Password Encryption Demo

## Current Implementation Status

✅ **SMTP Password Encryption is now implemented and working!**

### What was implemented:

1. **Dual DTO Strategy for Security**:
   - `AgencyEmailConfigurationPublicDto` - masks passwords as `[ENCRYPTED]` for external APIs
   - `AgencyEmailConfigurationResponseDto` - provides decrypted passwords for internal services

2. **Updated API Endpoints**:
   - `GET /api/v1/agency-email-config/agency/{id}` → Returns masked password for external use
   - `GET /api/v1/agency-email-config/agency/{id}/active` → Returns decrypted password for internal services

3. **Security Features**:
   - Passwords are encrypted in database using AES-256-GCM encryption
   - External API responses never expose actual passwords
   - Internal services can access decrypted passwords via dedicated endpoints
   - Passwords never appear in application logs

## Testing Results

### Unit Tests
```bash
mvn test -Dtest=AgencyEmailConfigurationControllerTest
# ✅ Tests run: 2, Failures: 0, Errors: 0, Skipped: 0
```

### Compilation
```bash
mvn clean compile -DskipTests
# ✅ BUILD SUCCESS
```

## How It Works

### For External API Consumers (Your curl command):
```bash
curl -X GET "http://localhost:8300/api/v1/agency-email-config/agency/1" -H "accept: */*"
```

**Response** (password is encrypted):
```json
{
  "id": 1,
  "agencyId": 1,
  "smtpHost": "smtp.hostinger.com",
  "smtpPort": 465,
  "smtpUsername": "<EMAIL>",
  "smtpPassword": "o8+ID3qcZOHzoGqoEWaZ7aOV9P7mTZqNiA6+yNlU/04ujozFxrDR2NuSa1hMiyBVsS/8og==",  ← Encrypted password string
  "fromEmail": "<EMAIL>",
  "fromName": "Test Agency",
  "smtpAuth": true,
  "smtpStarttlsEnable": true,
  "isActive": true,
  "isVerified": true
}
```

### For Internal Services (Feign clients):
```bash
curl -X GET "http://localhost:8300/api/v1/agency-email-config/agency/1/active" -H "accept: */*"
```

**Response** (password is decrypted for internal use):
```json
{
  "id": 1,
  "agencyId": 1,
  "smtpHost": "smtp.hostinger.com",
  "smtpPort": 465,
  "smtpUsername": "<EMAIL>",
  "smtpPassword": "MyActualPassword123",  ← Actual password for SMTP use
  "fromEmail": "<EMAIL>",
  "fromName": "Test Agency",
  "smtpAuth": true,
  "smtpStarttlsEnable": true,
  "isActive": true,
  "isVerified": true
}
```

## Database Storage

### Before (Plaintext):
```sql
smtp_password: "MyActualPassword123"
```

### After (Encrypted):
```sql
smtp_password: "o8+ID3qcZOHzoGqoEWaZ7aOV9P7mTZqNiA6+yNlU/04ujozFxrDR2NuSa1hMiyBVsS/8og=="
```

## Key Benefits

✅ **Security**: SMTP passwords are encrypted in the database
✅ **External API**: External consumers get encrypted passwords they can decrypt at their end
✅ **Internal Service Access**: Internal services can still get decrypted passwords directly
✅ **No Breaking Changes**: Existing functionality continues to work
✅ **Automatic**: Encryption/decryption happens transparently
✅ **Flexible**: External services can decrypt passwords when needed for SMTP usage

## Next Steps

To see the changes in action:
1. Restart the worklink-service application
2. Test the external endpoint: `GET /api/v1/agency-email-config/agency/1`
3. Test the internal endpoint: `GET /api/v1/agency-email-config/agency/1/active`

The implementation ensures that external API consumers get encrypted passwords that they can decrypt at their end, while internal services can still access decrypted passwords directly for SMTP functionality.

## How External Services Use the Encrypted Password

When an external service receives the encrypted password from the API:

```java
// 1. Get the encrypted password from API
String encryptedPassword = apiResponse.getSmtpPassword();
// Example: "o8+ID3qcZOHzoGqoEWaZ7aOV9P7mTZqNiA6+yNlU/04ujozFxrDR2NuSa1hMiyBVsS/8og=="

// 2. Decrypt it using the same EncryptionService (with same key)
EncryptionService encryptionService = new EncryptionService();
String actualPassword = encryptionService.decrypt(encryptedPassword);
// Result: "MyActualPassword123"

// 3. Use the decrypted password for SMTP
Properties props = new Properties();
props.put("mail.smtp.password", actualPassword);
```

This approach ensures that:
- Passwords are never transmitted in plain text
- External services can still access the actual password when needed
- The encryption key can be shared securely between services
- Database remains secure with encrypted passwords
