package com.cap10mycap10.worklinkservice.feign;


import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.request.UserDto;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.response.UserResponse;
// Ribbon is deprecated in Spring Cloud 2020.0.0+, using Spring Cloud LoadBalancer instead
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;


@FeignClient(name = "zuul-gateway")
// @RibbonClient is deprecated, Spring Cloud LoadBalancer is used automatically
public interface RegisterAgentAdminFeignClient {

    @PostMapping("user-service/api/v1/user-management/user/feignregistration")
    UserResponse registerUserAccountFeign(@Valid @RequestBody final UserDto accountDto);

   /* @PostMapping("/remit-user-manager/user/feignregistration")
    UserResponse registerUserAccountFeign(@Valid @RequestBody final UserDto accountDto);*/

    @PutMapping("user-service/api/v1/user-management/user/activate/{id}")
    UserResponse activateUser(@PathVariable Integer id);

    @PutMapping("user-service/api/v1/user-management/user/deactivate/{id}")
    UserResponse deactivateUser(@PathVariable Integer id);

    @GetMapping("user-service/api/v1/user-management/users/activate-agency-users/{agentId}")
    void activateAgentUsers(@PathVariable("agentId") Long agentId, @RequestParam("active") boolean active);

    @GetMapping("user-service/api/v1/user-management/users/activate-client-users/{payerId}")
    void activateClientUsers(@PathVariable("payerId") Long clientId, @RequestParam("active") boolean active);


    @GetMapping("user-service/api/v1/user-management/users-client/{payerId}")
    UserResponse getUserByClientId(@PathVariable("payerId") Long clientId);

    @GetMapping("user-service/api/v1/user-management/users/agency/{page}/{size}/{payeeId}")
    List<UserResponse> getAgencyUsers(@PathVariable("page") int page,
                                      @PathVariable("size") int size,
                                      @PathVariable("payeeId") Long agentId);


}
