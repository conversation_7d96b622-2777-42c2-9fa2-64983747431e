package com.cap10mycap10.worklinkservice.dto.workersecuretransportassignment;


import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WorkerTransportAssignmentResultDto {

    private String workerName;

    private Long workerTransportAssignmentId;

    private Long transportId;

    private Long transportBookingId;

    private TransportStatus transportStatus;

    private Long assignmentCodeId;

    private String assignmentCodeName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
    private LocalDate dateRequired;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
    private LocalDate dateBooked;



}
