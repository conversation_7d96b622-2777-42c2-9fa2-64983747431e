package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Objects.nonNull;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonSerialize
public class InvoiceItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long shiftId;

    private String dayOfTheWeek;

    private String startTime;

    private String endTime;

    private String startDate;

    private String endDate;

    @Column(nullable = false)
    private double numberOfHoursWorked;

    private BigDecimal rate;

    private Long trainingId;

    private String worker;

    private String client;
    private String description;
    private Long clientId;
    private String assignmentCode;

    private String shiftType;

    private BigDecimal total;

    // Tax-related fields
    private Boolean taxExempt = false; // Whether this item is exempt from tax
    private BigDecimal taxRate = BigDecimal.ZERO; // Tax rate applied to this item
    private BigDecimal taxAmount = BigDecimal.ZERO; // Tax amount for this item
    private BigDecimal netAmount = BigDecimal.ZERO; // Amount before tax (for tax-inclusive items)
    private Boolean taxInclusive = false; // Whether the total includes tax

    private String directorate;

    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
//    @JoinColumn(nullable = false)
    private Invoice invoice;

    public InvoiceItem(VehicleBooking vehicleBooking, LocalDate d) {
        VehicleRate rat = vehicleBooking.getVehicle().getRate(d);
        this.description = "Car rental for "+d.getDayOfWeek()+ " "+vehicleBooking.getVehicle().getModel();
        this.total = BigDecimal.valueOf(rat.getRate());
        this.rate = BigDecimal.valueOf(rat.getRate());
    }
    public InvoiceItem(Transport transport) {
        if(!nonNull(transport.getVehicleLog() ) || !nonNull(transport.getVehicleLog().getEndMileage() ) || !nonNull(transport.getVehicleLog().getStartMileage()))
            throw new BusinessValidationException("Enter job start and end mileage in driver log");
        if(!nonNull(transport.getVehicle().getMileageRate()))
            throw new BusinessValidationException("Set mileage rate for vehicle "+transport.getVehicle().getId()+ " "+ transport.getVehicle().getName());
        float rate = transport.getVehicle().getMileageRate();
        float mileage = transport.getVehicleLog().getEndMileage() - transport.getVehicleLog().getStartMileage();
        if(mileage<0)
            throw new BusinessValidationException("Job vehicle mileage cannot be less than zero. Please enter correct mileage log.");

        this.description = "Secure transport for "+mileage+ " miles, "+transport.getVehicle().getModel();
        this.total = BigDecimal.valueOf(rate*mileage);
        this.rate = BigDecimal.valueOf(rate);
        this.numberOfHoursWorked = mileage;
    }
    public InvoiceItem(Transport transport, LocalDate hourly) {
        Float hrs = transport.getHoursSpent();
        float rate = transport.getVehicle().getHourlyRate();
        this.description = "Secure transport for "+rate + ", hrs"+transport.getVehicle().getModel();
        this.total = BigDecimal.valueOf(rate*hrs);
        this.rate = BigDecimal.valueOf(rate);
        this.numberOfHoursWorked = hrs;
    }

    public InvoiceItem(Integer days, VehicleInventory d) {
        this.description = d.getName()+" ";
        this.total = BigDecimal.valueOf(d.getPrice()*days);
        this.rate = BigDecimal.valueOf(d.getPrice());
    }

    public BigDecimal getRate() {
        return rate;
    }

    public BigDecimal getTotal() {
        if (isNull(total)) {
            return null;
        }
        return total.setScale(2, RoundingMode.HALF_EVEN);
    }

    // Tax calculation helper methods
    public void calculateTax(BigDecimal taxRatePercent, boolean isInclusive) {
        if (taxExempt || taxRatePercent == null || taxRatePercent.compareTo(BigDecimal.ZERO) == 0) {
            this.taxRate = BigDecimal.ZERO;
            this.taxAmount = BigDecimal.ZERO;
            this.netAmount = this.total;
            this.taxInclusive = false;
            return;
        }

        this.taxRate = taxRatePercent;
        this.taxInclusive = isInclusive;

        if (isInclusive) {
            // Price includes tax - calculate net amount and tax amount
            BigDecimal divisor = BigDecimal.ONE.add(taxRatePercent.divide(BigDecimal.valueOf(100)));
            this.netAmount = this.total.divide(divisor, 2, RoundingMode.HALF_EVEN);
            this.taxAmount = this.total.subtract(this.netAmount);
        } else {
            // Price excludes tax - calculate tax amount and keep total as net
            this.netAmount = this.total;
            this.taxAmount = this.total.multiply(taxRatePercent).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_EVEN);
            this.total = this.netAmount.add(this.taxAmount);
        }
    }

    public BigDecimal getNetAmount() {
//        return nonNull(netAmount) ? netAmount.setScale(2, RoundingMode.HALF_EVEN) : BigDecimal.ZERO;

        return total.subtract(getTaxAmount()).setScale(2, RoundingMode.HALF_EVEN);
    }

    public BigDecimal getTaxAmount() {
        return nonNull(taxAmount) ? taxAmount.setScale(2, RoundingMode.HALF_EVEN) : BigDecimal.ZERO;
    }
}
