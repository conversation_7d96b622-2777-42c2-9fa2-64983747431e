<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.17.0.final using JasperReports Library version 6.17.0-6d93193241dd8cc42629e188b94f9e0bc5722efd  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Invoice" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4eedbb89-b4f6-4469-9ab6-f642a1688cf7">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="MyJDBCDataAdaptor"/>
	<style name="Title" forecolor="#FFFFFF" fontSize="50" isBold="false" pdfFontName="Times-Bold"/>
	<style name="SubTitle" forecolor="#CCCCCC" fontSize="18" isBold="false" pdfFontName="Times-Roman"/>
	<style name="Column header" forecolor="#666666" fontSize="14" isBold="true"/>
	<style name="Detail" mode="Transparent"/>
	<style name="Row" mode="Transparent" pdfFontName="Times-Roman">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#EEEFF0"/>
		</conditionalStyle>
	</style>
	<style name="Table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#CACED0">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style backcolor="#D8D8D8"/>
		</conditionalStyle>
	</style>
	<subDataset name="tableDataset" uuid="f13e6d36-5148-4ecc-bbe3-3035def80980">
		<queryString>
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="INVOICE_ID" class="java.lang.Long">
		<parameterDescription><![CDATA[Invoice]]></parameterDescription>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select d.name          as agencyName,
       d.billing_email,
       d.first_line,
       d.second_line,
       d.county,
       d.town,
       d.telephone,
       c.name          as clientName,
       c.first_line    as clientStreet,
       c.second_line   as clientSurbub,
       c.county        as clientCounty,
       c.town          as clientTown,
       c.billing_email as clientEmail,
       c.telephone     as clientTelephone,
       total_amount,
       invoice_status,
       invoice_id,
       day_of_the_week,
       end_date,
       end_time,
       number_of_hours_worked,
       rate,
       shift_id,
       start_date,
       start_time,
       total           as lineItemTotal
from invoice_item
         inner join invoice on invoice_item.invoice_id = invoice.id
         inner join client c on invoice.client_id = c.id
         inner join agency d on invoice.agent_id = d.id
         where invoice_id = $P{INVOICE_ID}]]>
	</queryString>
	<field name="agencyName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="agencyName"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="billing_email" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="billing_email"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="first_line" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="first_line"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="second_line" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="second_line"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="county" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="county"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="town" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="town"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="telephone" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="telephone"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="clientName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="clientName"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientStreet" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="clientStreet"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientSurbub" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="clientSurbub"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientCounty" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="clientCounty"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientTown" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="clientTown"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientEmail" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="clientEmail"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientTelephone" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="clientTelephone"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="total_amount" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="total_amount"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice"/>
	</field>
	<field name="invoice_status" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="invoice_status"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice"/>
	</field>
	<field name="invoice_id" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="invoice_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="day_of_the_week" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="day_of_the_week"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="end_date" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="end_date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="end_time" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="end_time"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="number_of_hours_worked" class="java.lang.Float">
		<property name="com.jaspersoft.studio.field.label" value="number_of_hours_worked"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="rate" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="rate"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="shift_id" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.label" value="shift_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="start_date" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="start_date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="start_time" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="start_time"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<field name="lineItemTotal" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.label" value="lineItemTotal"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
	</field>
	<title>
		<band height="230" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="90" width="84" height="20" uuid="57aed2b1-4f4e-40a3-a6ad-54dae8dd4c5a">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[Bill to:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="80" width="556" height="1" uuid="806ce5df-1219-4876-ae0c-ca7405b1f246">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="415" y="10" width="50" height="20" uuid="0f86baff-6386-4f3f-b3fe-2388707babe8"/>
				<box rightPadding="4"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Date:]]></text>
			</staticText>
			<textField pattern="EEEEE dd MMMMM yyyy">
				<reportElement x="465" y="10" width="84" height="20" uuid="bb10dbe1-0a4f-4722-9953-c163b63cf979"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<image hAlign="Center">
				<reportElement x="0" y="0" width="65" height="60" uuid="94883631-a913-43e2-b182-ab8d77d0181e"/>
				<imageExpression><![CDATA[]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="415" y="30" width="50" height="20" uuid="0b3f9342-da78-4cfa-9fc5-2301c4749678"/>
				<box rightPadding="4"/>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Invoice #]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="95" y="0" width="205" height="15" uuid="835c3783-038a-4d14-9f83-43c887612b22">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4044bdac-e154-45e3-a416-c73280830bc9"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{agencyName}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="95" y="16" width="205" height="15" uuid="ba002b6f-1211-465d-9e0e-5115ed864efb">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7afc8e99-6953-452a-8348-4a42c89cad4e"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{first_line}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="95" y="36" width="205" height="15" uuid="341c03ac-a9bd-45a0-87cc-d2fea58cf7a0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="fc365f83-816a-4b64-b16f-e321a8a1df69"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{second_line} + $F{county} + $F{town}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="95" y="51" width="205" height="15" uuid="0fd3aa03-866b-4f0c-a5cd-cbf111314e0f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6e5bedec-b89d-45e0-859a-a7e51ba03a65"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{billing_email} +  $F{telephone}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="SubTitle" positionType="Float" x="0" y="110" width="180" height="15" uuid="09376368-3078-468b-bcc1-f46b9922ed3d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="09b0547d-7662-458b-bf50-89f9e2f36bb9"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{clientName}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="0" y="124" width="120" height="15" uuid="a05b2fab-672a-48c0-8165-3e5d8ba41b46">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="7c36f9fc-8235-4ff5-9701-d8e66c9ca7f9"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{clientStreet}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="0" y="140" width="310" height="15" uuid="50069c26-a037-4fb1-9210-59b97c5259b1">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="e9ee7b63-d9e2-459d-ba78-2540e821b604"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{clientSurbub} + $F{county} + $F{town}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="0" y="155" width="310" height="15" uuid="faa463fc-4483-46a5-ab3f-4acee63848e5">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="85106221-4fd4-45e3-8c2d-27322ebb5a9d"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{clientEmail} + $F{clientTelephone}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="465" y="30" width="70" height="20" uuid="1c52d027-64d0-4cb0-88e9-ee5a29424d7c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="236438cd-3206-4cbd-a436-ee40967a0351"/>
				</reportElement>
				<textElement>
					<font size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{invoice_id}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="22">
			<staticText>
				<reportElement style="Column header" positionType="Float" x="59" y="0" width="60" height="15" forecolor="#000000" uuid="f2d3e28f-cb51-4c77-b198-2aa06bd9314f">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4e7c6b1f-8bda-4df1-9f83-78ae5157d539"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Day]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="265" y="0" width="60" height="15" forecolor="#000000" uuid="099cc808-12ce-4aef-aea6-48ea79f85420">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="8fd9e00e-986e-4073-8762-47f11d4716f7"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[end_date]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="330" y="2" width="50" height="14" forecolor="#000000" uuid="8fd99b8c-f80b-45ce-b2f1-2455a04d3d5c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="15321913-4a01-4405-a1ae-93b83621e146"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[end_time]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="380" y="0" width="38" height="15" forecolor="#000000" uuid="1e4d2322-38a3-4848-a176-84bd75738156">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="45332543-c5ab-4585-add0-94244b535f5e"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[hrs]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="423" y="2" width="57" height="15" forecolor="#000000" uuid="a23489a2-a026-4a3b-b799-31bb81ba9de7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="24fdf05b-50a8-45f8-9912-3560a0f33b75"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[rate]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="7" y="1" width="53" height="15" forecolor="#000000" uuid="28efad89-8b94-4937-b4d3-ba4849acd846">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="edf42034-4773-40e6-a3a1-ea2a36cbf1a0"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Shift]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="119" y="0" width="61" height="15" forecolor="#000000" uuid="c116de53-8ece-4d65-bdfe-2a58145ed41a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6d2c8f83-2e38-4cfd-a6af-d11c66e4cfcb"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[start date]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="496" y="0" width="60" height="15" forecolor="#000000" uuid="96f68aa0-9372-4ab7-812b-dbeb7cb05158">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dcc00a74-8fb2-446f-88e1-0496dff69faf"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="180" y="1" width="85" height="15" forecolor="#000000" uuid="7a5ad911-253c-4cef-8103-fc79ab9c0086">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ae91b5e2-b37b-4406-a6cd-fcb09c1f2305"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[start time]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="30">
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="59" y="-1" width="60" height="15" uuid="59f98a95-7c69-4e3d-af80-83321c173906">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4e7c6b1f-8bda-4df1-9f83-78ae5157d539"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{day_of_the_week}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="265" y="-1" width="60" height="15" uuid="23d7d18c-dd2f-40a3-8452-91dfaf29637c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="8fd9e00e-986e-4073-8762-47f11d4716f7"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{end_date}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="330" y="1" width="50" height="14" uuid="8e26ec7a-0893-447b-878a-67e6dd643cf5">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="15321913-4a01-4405-a1ae-93b83621e146"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{end_time}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="380" y="1" width="38" height="15" uuid="e20ce9b4-3e03-46f4-a389-049c1b900cd8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="45332543-c5ab-4585-add0-94244b535f5e"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{number_of_hours_worked}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="423" y="3" width="57" height="15" uuid="df8b56e9-ca81-43eb-b964-357808fcf323">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="24fdf05b-50a8-45f8-9912-3560a0f33b75"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rate}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="7" y="1" width="52" height="15" uuid="919b4d1b-84e3-4880-9e59-c08a43109f52">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="edf42034-4773-40e6-a3a1-ea2a36cbf1a0"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shift_id}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="120" y="-1" width="60" height="15" uuid="449fb31f-2794-40bc-8185-05cf5d61a31e">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="6d2c8f83-2e38-4cfd-a6af-d11c66e4cfcb"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{start_date}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="180" y="-1" width="85" height="21" uuid="21240d22-ca32-43a5-8049-0c6c3d8bc29d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="ae91b5e2-b37b-4406-a6cd-fcb09c1f2305"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{start_time}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="496" y="0" width="60" height="15" uuid="3e7fcf77-ec49-445f-a604-b94ebff4f595">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="dcc00a74-8fb2-446f-88e1-0496dff69faf"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lineItemTotal}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band height="177">
			<staticText>
				<reportElement positionType="Float" x="-1" y="30" width="261" height="101" uuid="1a0d7088-5af7-4865-8be1-41ec5f51fb36"/>
				<box>
					<topPen lineWidth="1.1" lineStyle="Dashed"/>
					<leftPen lineWidth="1.1" lineStyle="Dashed"/>
					<bottomPen lineWidth="1.1" lineStyle="Dashed"/>
					<rightPen lineWidth="1.1" lineStyle="Dashed"/>
				</box>
				<text><![CDATA[Your notes here]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Opaque" x="0" y="147" width="556" height="30" backcolor="#E6E8E9" uuid="36aa233d-4305-48e6-974a-1bbf89bb3c8f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Serif" size="9" isItalic="true"/>
				</textElement>
				<text><![CDATA[THANK YOU FOR YOUR BUSINESS]]></text>
			</staticText>
			<staticText>
				<reportElement style="Column header" positionType="Float" x="380" y="8" width="100" height="20" forecolor="#000000" uuid="2bd34f01-2051-4ff2-8a33-d250f2c3342c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="27006fa4-5fae-4710-9934-5b855af96847"/>
				</reportElement>
				<textElement>
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[total_amount]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement style="Detail" positionType="Float" x="480" y="10" width="76" height="20" uuid="c777ab42-e840-4cdb-879a-30f6b4be8c13">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="27006fa4-5fae-4710-9934-5b855af96847"/>
				</reportElement>
				<textElement>
					<font size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total_amount}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
</jasperReport>
