package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.model.ShiftType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface ShiftTypeService {

    ShiftType save(ShiftType shiftTypeCreateDto);

    ShiftType findById(Long id);

    List<ShiftType> findAll();

    Page<ShiftType> findAllPaged(PageRequest of);

    void deleteById(Long id);

    ShiftType update(ShiftType shiftTypeUpdateDto);

    ShiftType getOne(Long id);

    void deactivate(Long id);

    void activate(Long id);
}
