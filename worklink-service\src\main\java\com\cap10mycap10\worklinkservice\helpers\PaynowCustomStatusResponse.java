package com.cap10mycap10.worklinkservice.helpers;//

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Map;
import zw.co.paynow.constants.TransactionStatus;
import zw.co.paynow.exceptions.InvalidIntegrationException;
import zw.co.paynow.responses.PaynowResponse;

public class PaynowCustomStatusResponse extends PaynowResponse {
    private  String merchantReference;
    private  String paynowReference;
    private  BigDecimal amount;
    private  boolean paid;

    public PaynowCustomStatusResponse(Map<String, String> response) throws InvalidIntegrationException {
        this.rawResponseContent = response;
        if (!this.rawResponseContent.containsKey("error")) {
            this.requestSuccess = true;
        } else {
            this.requestSuccess = false;
        }

        if (this.rawResponseContent.containsKey("status")) {
            String rawStatus = (String)this.rawResponseContent.get("status");
            this.status = TransactionStatus.getTransactionStatus(rawStatus);
            this.paid = ((String)this.rawResponseContent.get("status")).equalsIgnoreCase(TransactionStatus.PAID.getResponseString());
        } else {
            this.paid = false;
            this.status = TransactionStatus.UNDEFINED;
        }

        if (this.rawResponseContent.containsKey("amount")) {
            this.amount = new BigDecimal((String)this.rawResponseContent.get("amount"));
        } else {
            this.amount = new BigDecimal(0);
        }

        if (this.rawResponseContent.containsKey("reference")) {
            this.merchantReference = (String)this.rawResponseContent.get("reference");
        } else {
            this.merchantReference = "";
        }

        if (this.rawResponseContent.containsKey("paynowreference")) {
            this.paynowReference = (String)this.rawResponseContent.get("paynowreference");
        } else {
            this.paynowReference = "";
        }

        if (!this.requestSuccess) {
            if (this.rawResponseContent.containsKey("error")) {
                this.fail((String)this.rawResponseContent.get("error"));
            }

        }
    }

    public final String pollUrl() {
        return this.rawResponseContent.containsKey("pollurl") ? (String)this.rawResponseContent.get("pollurl") : "";
    }

    public final String hash() {
        return this.rawResponseContent.containsKey("hash") ? (String)this.rawResponseContent.get("hash") : "";
    }

    public String getMerchantReference() {
        return this.merchantReference;
    }

    public String getPaynowReference() {
        return this.paynowReference;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public boolean paid() {
        return this.isPaid();
    }

    public boolean isPaid() {
        return this.paid;
    }
}
