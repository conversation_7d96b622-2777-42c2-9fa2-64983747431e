package com.cap10mycap10.worklinkservice.mapper.shiftexpenseclaim;

import com.cap10mycap10.worklinkservice.dto.shiftexpenseclaim.ShiftExpenseClaimResultDto;
import com.cap10mycap10.worklinkservice.model.ShiftExpenseClaim;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ShiftExpenseClaimToShiftExpenseClaimResultDto implements Converter<List<ShiftExpenseClaim>, List<ShiftExpenseClaimResultDto>> {
    @Override
    public List<ShiftExpenseClaimResultDto> convert(List<ShiftExpenseClaim> ShiftExpenseClaim) {
        List<ShiftExpenseClaimResultDto> ShiftExpenseClaimResults = new ArrayList<>();
        for (ShiftExpenseClaim item:ShiftExpenseClaim
        ) {
            ShiftExpenseClaimResultDto shiftExpenseClaimResult = new ShiftExpenseClaimResultDto();
            shiftExpenseClaimResult.setId(item.getId());
            shiftExpenseClaimResult.setShiftId(item.getShift().getId());
            shiftExpenseClaimResult.setRate(item.getRate());
            shiftExpenseClaimResult.setAmount(item.getAmount());
            shiftExpenseClaimResult.setDescription(item.getDescription());
            shiftExpenseClaimResult.setStatus(item.getStatus());
            shiftExpenseClaimResult.setAgencyExpenseRate(item.getAgencyExpenseRate());

            ShiftExpenseClaimResults.add(shiftExpenseClaimResult);
        }
        return ShiftExpenseClaimResults;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
