package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AvailabilityRepository;
import com.cap10mycap10.worklinkservice.dto.availability.AvailabilityCreateDto;
import com.cap10mycap10.worklinkservice.dto.availability.AvailabilityUpdateDto;
import com.cap10mycap10.worklinkservice.dto.availability.IAvailabilityResultDto;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.mapper.availability.AvailabilityToAvailabilityResultDto;
import com.cap10mycap10.worklinkservice.model.Availability;
import com.cap10mycap10.worklinkservice.service.AvailabilityService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class AvailabilityServiceImpl implements AvailabilityService {

    private final WorkerService workerService;

    private final AvailabilityRepository availabilityRepository;

    private final AvailabilityToAvailabilityResultDto toAavailabilityToAvailabilityResultDto;

    public AvailabilityServiceImpl(WorkerService workerService, AvailabilityRepository availabilityRepository, AvailabilityToAvailabilityResultDto toAavailabilityToAvailabilityResultDto) {
        this.workerService = workerService;
        this.availabilityRepository = availabilityRepository;
        this.toAavailabilityToAvailabilityResultDto = toAavailabilityToAvailabilityResultDto;
    }

    @Override
    public void updateAvailability(AvailabilityCreateDto availabilityCreateDto) {
        if(availabilityCreateDto.getIsAvailable()) {
            Availability availability = new Availability();

            availability.setDate(availabilityCreateDto.getDate());
            availability.setStartTime(availabilityCreateDto.getStartTime());
            availability.setEndTime(availabilityCreateDto.getEndTime());
            availability.setReason(availability.getReason());
            availability.setWorker(workerService.getOne(availabilityCreateDto.getWorkerId()));
            availabilityRepository.save(availability);
        } else {
            List<Availability> av = availabilityRepository.findAllByDateAndWorker(availabilityCreateDto.getDate(), workerService.getOne(availabilityCreateDto.getWorkerId()));
            av.forEach(e -> {availabilityRepository.delete(e);});
        }
    }

    @Override
    public void deleteAvailability(Long id) {

        Availability availability = availabilityRepository.findById(id).orElseThrow(() -> new RecordNotFoundException("Availability not found"));


            availabilityRepository.delete(availability);
            availabilityRepository.flush();


    }

    @Override
    public Availability findById(Long id) {
        return null;
    }

    @Override
    public Page<IAvailabilityResultDto> findWorkerAvailability(Long workerId, PageRequest of) {
        return availabilityRepository.findAllWorkerAvailability(workerId, of);
//                .stream()
//                .map(toAavailabilityToAvailabilityResultDto::convert)
//                .collect(Collectors.toList());


    }

    @Override
    public Page<Availability> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public Availability save(AvailabilityUpdateDto availabilityUpdateDto) {
        return null;
    }

    @Override
    public Availability getOne(Long id) {
        return null;
    }
}
