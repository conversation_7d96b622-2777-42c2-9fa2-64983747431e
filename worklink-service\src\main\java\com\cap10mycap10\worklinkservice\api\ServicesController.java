package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.service.ServiceCreateDto;
import com.cap10mycap10.worklinkservice.dto.service.ServiceResultDto;
import com.cap10mycap10.worklinkservice.dto.service.ServiceUpdateDto;
import com.cap10mycap10.worklinkservice.permissions.services.CreateServices;
import com.cap10mycap10.worklinkservice.permissions.services.DeleteServices;
import com.cap10mycap10.worklinkservice.permissions.services.UpdateServices;
import com.cap10mycap10.worklinkservice.permissions.services.ViewServices;
import com.cap10mycap10.worklinkservice.service.ServicesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;


@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ServicesController {

    private final ServicesService servicesService;

    public ServicesController(final ServicesService servicesService) {
        this.servicesService = servicesService;
    }

    /*@CreateServices*/
    @PostMapping(value = "service", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> create(@RequestBody ServiceCreateDto serviceCreateDto) {
        log.info("Request to add service with : {}", serviceCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(servicesService.save(serviceCreateDto));
    }

   /* @ViewServices*/
    @GetMapping(value = "service/{id}")
    public ResponseEntity<ServiceResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get service with id : {}", id);
        return ResponseEntity.ok(servicesService.findById(id));
    }

    /*@ViewServices*/
    @GetMapping(value = "services")
    public ResponseEntity<List<ServiceResultDto>> findById() {
        log.info("Request to get all services");
        return ResponseEntity.ok(servicesService.findAll());
    }

   /* @ViewServices*/
    @GetMapping(value = "services/{page}/{size}")
    public ResponseEntity<Page<ServiceResultDto>> findById(@PathVariable("page") int page,
                                                           @PathVariable("size") int size) {
        log.info("Request to get paged services : {}, {}", page, size);
        return ResponseEntity.ok(servicesService.findAllPaged(PageRequest.of(page, size)));
    }

   /* @UpdateServices*/
    @PutMapping(value = "service", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ServiceResultDto> update(@RequestBody ServiceUpdateDto serviceUpdateDto) {
        log.info("Request to update service with : {}", serviceUpdateDto);
        return ResponseEntity.ok(servicesService.save(serviceUpdateDto));
    }

    /*@DeleteServices*/
    @DeleteMapping(value = "service/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        servicesService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

}
