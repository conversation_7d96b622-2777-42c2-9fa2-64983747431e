package com.cap10mycap10.worklinkservice.dto.asset.admin;

import com.cap10mycap10.worklinkservice.enums.PromotionType;
import com.cap10mycap10.worklinkservice.enums.TransmissionType;
import com.cap10mycap10.worklinkservice.enums.VehicleType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class VehicleSearchDto {

    private int size;
    private Long agencyId;
    private String searchCriteria;
    private LocalDateTime start;
    private LocalDateTime end;
    private Long location;
    private VehicleType vehicleType;


    private TransmissionType transmission;
    private Float minPrice;
    private Float maxPrice;
    private Float maxDeposit;
    private Float lowMileageLimit;

    // Promotion filters
    private Boolean hasPromotion;
    private PromotionType promotionType;

    // Sorting parameters
    private String sortBy;
    private String sortDirection;

}
