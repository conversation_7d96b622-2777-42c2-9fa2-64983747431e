package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateCreateDto;
import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateResultDto;
import com.cap10mycap10.worklinkservice.dto.assignmentcoderate.AssignmentCodeRateUpdateDto;
import com.cap10mycap10.worklinkservice.model.AssignmentRate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface AssignmentCodeRateService {

    AssignmentCodeRateResultDto save(AssignmentCodeRateCreateDto assignmentCodeRateCreateDto);

    AssignmentCodeRateResultDto save(AssignmentCodeRateUpdateDto assignmentCodeRateUpdateDto);

    AssignmentCodeRateResultDto findById(Long id);

    List<AssignmentCodeRateResultDto> findAll();

    Page<AssignmentCodeRateResultDto> findAllPaged(PageRequest of);

    void deleteById(Long id);

    AssignmentRate getOne(Long id);

    List<AssignmentRate> findAssignmentRate(Long clientId, Long directorateId, Long shiftTypeId, String dayOfWeek, Long assignmentCodeId);

    List<AssignmentCodeRateResultDto> findAllByAgentId(Long agentId);
    List<AssignmentCodeRateResultDto> findAllByClientId(Long agentId);

    List<AssignmentCodeRateResultDto> findAllByAgentId(Long agentId, String day);

    void saveAll(List<AssignmentCodeRateCreateDto> assignmentCodeCreateDto);
}
