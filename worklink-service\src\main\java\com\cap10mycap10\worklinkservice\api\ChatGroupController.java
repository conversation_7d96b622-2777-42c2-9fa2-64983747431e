package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.dto.chatgroupmessage.ChatGroupMessageResponseDto;
import com.cap10mycap10.worklinkservice.model.ChatGroup;
import com.cap10mycap10.worklinkservice.model.ChatGroupMessage;
import com.cap10mycap10.worklinkservice.service.ChatGroupMessageService;
import com.cap10mycap10.worklinkservice.service.ChatGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/chat-groups")
@RequiredArgsConstructor
@Slf4j
public class ChatGroupController {

    private final ChatGroupService chatGroupService;

    private final ChatGroupMessageService chatGroupMessageService;

    @GetMapping("/{shiftId}")
    public ResponseEntity<ChatGroup> findChatGroupByShiftId(@PathVariable("shiftId") Long shiftId){
        log.info("Request to get a group chat for shift id: {}", shiftId);
        return ResponseEntity.ok(chatGroupService.findChatGroupByShiftID(shiftId));
    }

    @GetMapping("/{shiftId}/{workerId}")
    public ResponseEntity<ChatGroup> findChatGroupByShiftIdAndWorkerId(@PathVariable("shiftId") Long shiftId,@PathVariable("workerId") Long workerId,
                                                                       @PathVariable("workerId") Long workerId1){
        log.info("Request to get chat group for shift id: {} and worker id: {}", shiftId, workerId);
        return ResponseEntity.ok(chatGroupService.findChatGroupByShiftIdAndWorkerId(shiftId, workerId));
    }

    @GetMapping("group-messages/{shiftId}/{workerId}")
    public ResponseEntity<List<ChatGroupMessageResponseDto>> findChatGroupMessagesByShiftIdAndWorkerId(@PathVariable("shiftId") Long shiftId, @PathVariable("workerId") Long workerId){
        log.info("Request to get chat group messages for shift id: {} and worker id: {}", shiftId, workerId);
        return ResponseEntity.ok(chatGroupService.findChatGroupMessagesByShiftIdAndWorkerId(shiftId, workerId));
    }

    @GetMapping("groupId/{groupId}")
    public ResponseEntity<List<ChatGroupMessageResponseDto>> findChatGroupMessagesByChatGroupId(@PathVariable("groupId") Long groupId){
        log.info("Request to get chat group messages for group id: {}", groupId);
        return ResponseEntity.ok(chatGroupService.findChatGroupMessagesByChatGroupId(groupId));
    }
}
