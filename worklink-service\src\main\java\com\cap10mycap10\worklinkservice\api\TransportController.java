package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.shift.ShiftReportStatus;
import com.cap10mycap10.worklinkservice.dto.transport.*;
import com.cap10mycap10.worklinkservice.dto.transportbooking.TransportApplicantsResultDto;
import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import com.cap10mycap10.worklinkservice.model.Transport;
import com.cap10mycap10.worklinkservice.service.TransportBookingService;
import com.cap10mycap10.worklinkservice.service.TransportService; 
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jettison.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class TransportController {

    @Autowired
    private TransportService transportService;
    @Autowired
    private TransportBookingService transportBookingService;

    @PostMapping(value = "transport", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TransportDto> create(@RequestBody TransportDto transportDto) throws JSONException {
        log.info("Request to create a new transport request : {}", transportDto);
        return ResponseEntity.ok(transportService.create(transportDto));
    }

    @PutMapping(value = "transport", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TransportDto> update(@RequestBody TransportDto transportDto) throws JSONException {
        log.info("Request to update a new transport request : {}", transportDto);
        return ResponseEntity.ok(transportService.update(transportDto));
    }


    @PutMapping(value = "transport/mileage", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleLogDto> updateMileage(@RequestBody VehicleLogDto transportDto){
        log.info("Request to update vehicle mileage : {}", transportDto);
        return ResponseEntity.ok(transportService.updateMileage(transportDto));
    }

    @PutMapping(value = "transport/vehicle-check", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleLogDto> updateVehicleCheck(@RequestBody VehicleLogDto transportDto){
        log.info("Request to update vehicle check : {}", transportDto);
        return ResponseEntity.ok(transportService.updateVehicleCheck(transportDto));
    }

    @PutMapping(value = "transport/team-leader/entry", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TransportDto> teamLeaderUpdate(@RequestBody TransportTeamLeaderUpdateDto transportDto){
        log.info("Request to update team leader data : {}", transportDto);
        return ResponseEntity.ok(transportService.teamLeaderUpdate(transportDto));
    }


    @PutMapping(value = "transport/worker-times", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TransportDto> teamLeaderUpdate(@RequestBody TransportWorkerTimesDto transportDto){
        log.info("Request to update team leader data : {}", transportDto);
        return ResponseEntity.ok(transportService.teamLeaderWorkerTimesUpdate(transportDto));
    }

    @PutMapping(value = "transport/cleaning", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleLogDto> updateCleaningCheck(@RequestBody VehicleLogDto transportDto){
        log.info("Request to update cleanliness check : {}", transportDto);
        return ResponseEntity.ok(transportService.updateCleaningCheck(transportDto));
    }

    @GetMapping(value = "transport/worker/driver/{driverId}/{status}/{page}/{size}")
    public ResponseEntity<Page<TransportDto>> findByDriver(@PathVariable("size") int size,
                                                             @PathVariable("page") int page,
                                                             @PathVariable("driverId") Long driverId,
                                                             @PathVariable("status") TransportStatus status){
        log.info("Request to update cleanliness check : {}", driverId);
        return ResponseEntity.ok(transportService.findByDriver(driverId,status,PageRequest.of(page, size)));
    }


    @GetMapping(value = "vehicle-logs/agency/{agencyId}/{status}/{page}/{size}")
    public ResponseEntity<Page<VehicleLogDto>> findByAgencyAndStatus(@PathVariable("size") int size,
                                                             @PathVariable("page") int page,
                                                             @PathVariable("agencyId") Long agencyId,
                                                             @PathVariable("status") LogStatus status){
        log.info("Request to update cleanliness check : {}", agencyId);
        return ResponseEntity.ok(transportService.findLogsByAgencyAndStatus(agencyId,status,PageRequest.of(page, size)));
    }


    @GetMapping(value = "vehicle-logs/worker/{workerId}/{status}/{page}/{size}")
    public ResponseEntity<Page<VehicleLogDto>> findByWorkerAndStatus(@PathVariable("size") int size,
                                                             @PathVariable("page") int page,
                                                             @PathVariable("workerId") Long workerId,
                                                             @PathVariable("status") LogStatus status){
        log.info("Request to update cleanliness check : {}", workerId);
        return ResponseEntity.ok(transportService.findByWorkerAndStatus(workerId,status,PageRequest.of(page, size)));
    }

    @GetMapping(value = "transport/worker/team-leader/{leaderId}/{status}/{page}/{size}")
    public ResponseEntity<Page<TransportDto>> findByTeamLeader(@PathVariable("size") int size,
                                                             @PathVariable("page") int page,
                                                             @PathVariable("driverId") Long leaderId,
                                                             @PathVariable("status") TransportStatus status){
        log.info("Request to update cleanliness check : {}", leaderId);
        return ResponseEntity.ok(transportService.findByDriver(leaderId,status,PageRequest.of(page, size)));
    }



    @GetMapping(value = "transport/{status}/{size}/{page}")
    public ResponseEntity<Page<TransportDto>> findAll(@PathVariable("size") int size,
                                                            @PathVariable("page") int page,
                                                            @PathVariable("status") TransportStatus status){
        log.info("Request to get transport requests with page: {} of size {}",page, size);
        return ResponseEntity.ok(transportService.findPaged(PageRequest.of(page, size), status));
    }

    @GetMapping(value = "transport/{id}")
    public ResponseEntity<TransportDto> findById(@PathVariable("id") Long id){
        log.info("Request to get transport request with id: {}",id);
        return ResponseEntity.ok(transportService.findById(id));
    }

    @DeleteMapping(value = "transport/{id}")
    public ResponseEntity<Object>cancelJob(@PathVariable("id") Long id){
        log.info("Request to delete transport request with id: {}",id);
        transportService.cancelJob(id);
        return ResponseEntity.status(201).build();
    }

    @GetMapping(value = "transport/agency/{agencyId}/{status}/{page}/{size}")
    public ResponseEntity<Page<TransportDto>> findByAgencyId(@PathVariable("agencyId") Long agencyId,
                                                                   @PathVariable("page") int page,
                                                                   @PathVariable("size") int size,
                                                                   @PathVariable("status") TransportStatus status,
                                                                   @RequestParam("searchCriteria") String searchCriteria,
                                                                   @RequestParam(required = false, value = "fullyBookedOrNOt") boolean isFullyBooked){
        log.info("Request to get transport request with agencyId: {}",agencyId);
        return ResponseEntity.ok(transportService.findAgencyJobs(agencyId, PageRequest.of(page, size), status, isFullyBooked, searchCriteria));
    }


    @GetMapping(value = "transport/agency/dashboard/{agencyId}")
    public ResponseEntity<ShiftReportStatus> findAllAgencyShiftsByStatus(@PathVariable("agencyId") Long agencyId) {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(transportService.findAllAgencyShiftsByStatus(agencyId));
    }

    @GetMapping(value = "transport/logs/agency/dashboard/{agencyId}")
    public ResponseEntity<ShiftReportStatus> findAllAgencyLogsShiftsByStatus(@PathVariable("agencyId") Long agencyId) {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(transportService.findAllAgencyLogsShiftsByStatus(agencyId));
    }

    @GetMapping(value = "transport/client/dashboard/{clientId}")
    public ResponseEntity<ShiftReportStatus> findAllClientShiftsByStatus(@PathVariable("clientId") Long clientId) {
        log.info("Request to get  shift dashboard ");
        return ResponseEntity.ok(transportService.findAllClientShiftsByStatus(clientId));
    }

    @GetMapping(value = "transport/client/{clientId}/{status}/{page}/{size}")
    public ResponseEntity<Page<TransportDto>> findByClientId(@PathVariable("clientId") Long clientId,
                                                                   @PathVariable("page") int page,
                                                                   @PathVariable("size") int size,
                                                                   @PathVariable("status") TransportStatus status){
        log.info("Request to get transport request with clientId: {}",clientId);
        return ResponseEntity.ok(transportService.findByClientId(clientId, PageRequest.of(page, size), status));
    }

    @PostMapping(value = "transport-booking/book/{workerId}/{specId}")
    public ResponseEntity<BookingResultDto> bookWorkersDirectly(@PathVariable("workerId") Long workerId,
                                                                @PathVariable("specId") Long specId){
        log.info("Request to book worker with id: {}  and workerSpec id: {} ", workerId, specId);
        return ResponseEntity.ok(transportBookingService.bookWorkerDirectly( workerId, specId));
    }


    @PutMapping(value = "transport-booking/approve-booking/{workerTransportBookingId}")
    public ResponseEntity<TransportApplicantsResultDto> approveBooking(@PathVariable("workerTransportBookingId") Long workerTransportBookingId){
        log.info("Request to approve worker Transport booking of id: {} ", workerTransportBookingId);
        return ResponseEntity.ok(transportBookingService.approveBooking(workerTransportBookingId));
    }

    @DeleteMapping(value = "transport-booking/cancel/{transportBookingId}")
    public ResponseEntity<TransportApplicantsResultDto> cancelTransportBooking(@PathVariable("transportBookingId") Long transportBookingId,
                                                                               @RequestParam("reason") String reason){
        log.info("Request to cancel worker transport booking of id: {} with optional reason of {} ", transportBookingId, reason);
        return  ResponseEntity.ok(transportBookingService.cancelTransportBooking(transportBookingId, reason));
    }

    @PutMapping(value = "transport-booking/reject-booking/{transportBookingId}")
    public ResponseEntity<Object> rejectBooking(@PathVariable("transportBookingId") Long transportBookingId){
        log.info("Request to remove worker transport assignment with id: {}", transportBookingId);
        transportBookingService.rejectBooking(transportBookingId);
        return ResponseEntity.noContent().build();
    }

    @PutMapping(value = "transport-booking/{workerId}/{transportId}")
    public ResponseEntity<BookingResultDto> apply(@PathVariable("workerId") Long workerId,
                                                  @PathVariable("transportId") Long transportId){
        log.info("Request to add a transport worker of id: {} to transport booking: {} ", workerId, transportId);
        return ResponseEntity.ok(transportBookingService.workerApply(workerId, transportId));
    }

    @PutMapping(value = "transport/accept/{transportId}/{agencyId}")
    public ResponseEntity<TransportDto> accept(@PathVariable("transportId") Long transportId,
                                                     @PathVariable("agencyId") Long agencyId){
        log.info("BookingId : {} accepted by agency ", transportId);
        return ResponseEntity.ok(transportService.acceptJob(transportId, agencyId));
    }

    @GetMapping(value = "transport-booking/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<TransportApplicantsResultDto>> agencyTransportBookings(@PathVariable("agencyId") Long agencyId,
                                                                          @PathVariable("page") int page,
                                                                          @PathVariable("size") int size,
                                                                          @RequestParam(required = false) Long transportId,
                                                                          @RequestParam(required = false) TransportStatus transportStatus){
        log.info("Request to get transport request with agencyId: {}, transportId: {} and status: {}", agencyId, transportId, transportStatus);
        return ResponseEntity.ok(transportBookingService.agencyTransportBookings(agencyId, transportId, transportStatus, PageRequest.of(page, size)));
    }


    @PutMapping(value = "transport/vehicle/{id}/{transportId}")
    public ResponseEntity< TransportDto> transportVehicle(@PathVariable("id") Long id, @PathVariable("transportId") Long transportId){
        log.info("Request to get transport applicants for a job of id: {}", id);
        return ResponseEntity.ok(transportService.pickVehicle(id, transportId));
    }

    @PutMapping(value = "transport/pick-team-leader/{id}/{transportId}")
    public ResponseEntity< TransportDto> pickTeamLeader(@PathVariable("id") Long id, @PathVariable("transportId") Long transportId){
        log.info("Request to get transport applicants for a job of id: {}", id);
        return ResponseEntity.ok(transportService.pickTeamLeader(id, transportId));
    }

    @PutMapping(value = "transport/pick-driver/{id}/{transportId}")
    public ResponseEntity< TransportDto> pickDriver(@PathVariable("id") Long id, @PathVariable("transportId") Long transportId){
        log.info("Request to get transport applicants for a job of id: {}", id);
        return ResponseEntity.ok(transportService.pickDriver(id, transportId));
    }


    @GetMapping(value = "transport/legible-workers/{transportId}")
    public ResponseEntity< List<LegibleWorkersDto>> getTransportLegibleWorkers(@PathVariable("transportId") Long transportId){
        log.info("Request to get transport applicants for a job of id: {}", transportId);
        return ResponseEntity.ok(transportService.getEligibleWorkers(transportId));
    }


    @PutMapping(value = "transport/commit/{transportId}")
    public ResponseEntity<TransportDto> commitJob(@PathVariable("transportId") Long transportId){
        log.info("Request to commit transport job of id: {}", transportId);
        return ResponseEntity.ok(transportService.commitJob( transportId));
    }


    @PostMapping("/transport/upload-risk-document/{transportId}")
    public ResponseEntity<Object> uploadRiskDocument(@RequestParam("file") MultipartFile file,
                                                           @PathVariable("transportId") Long transportId){
        log.info("Request to upload patient risk document");
        Transport transport = transportService.getOne(transportId);
        transportService.uploadDocument(file, transport, "damageDoc");
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/vehicle-log/upload-damage-report/{logId}")
    public ResponseEntity<Object> uploadRiskDamageReport(@RequestParam("file") MultipartFile file,
                                                           @PathVariable("logId") Long logId){
        log.info("Request to upload patient risk document");
        transportService.uploadDamageReport(file, logId);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/transport/upload-patient-document-one/{transportId}")
    public ResponseEntity<Object> uploadPatientDocumentOne(@RequestParam("file") MultipartFile file,
                                                     @PathVariable("transportId") Long transportId){
        log.info("Request to upload patient  document one");
        Transport transport = transportService.getOne(transportId);
        transportService.uploadDocument(file, transport, "patientDocOne");
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/transport/upload-patient-document-two/{transportId}")
    public ResponseEntity<Object> uploadPatientDocumentTwo(@RequestParam("file") MultipartFile file,
                                                           @PathVariable("transportId") Long transportId){
        log.info("Request to upload patient document two");
        Transport transport = transportService.getOne(transportId);
        transportService.uploadDocument(file, transport, "patientDocTwo");
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/transport/upload-patient-document-three/{transportId}")
    public ResponseEntity<Object> uploadPatientDocumentThree(@RequestParam("file") MultipartFile file,
                                                           @PathVariable("transportId") Long transportId){
        log.info("Request to upload patient document three");
        Transport transport = transportService.getOne(transportId);
        transportService.uploadDocument(file, transport, "patientDocThree");
        return ResponseEntity.noContent().build();
    }

    @PutMapping(value = "transport/rate-provider/{transportId}/{rating}")
    public ResponseEntity<TransportDto> rateProvider(@PathVariable("transportId") Long transportId,
                                                              @PathVariable("rating") Byte rating){
        log.info("Request to rate a transport job of id: {} and rating of: {}", transportId, rating);
        return ResponseEntity.ok(transportService.transportRating(transportId, rating));
    }

    @PutMapping(value = "transport/authorize")
    public ResponseEntity<TransportDto> authorizeTransport(@RequestBody AuthorizeTransportDto authorizeTransportDto){
        log.info("Request to authorize a transport job of id: {} ", authorizeTransportDto);
        transportService.authorize(authorizeTransportDto);
        return ResponseEntity.noContent().build();
    }


    @PutMapping(value = "transport-booking/authorize/{transportBookingId}")
    public ResponseEntity<String> authorizeTransportBooking(@PathVariable("transportBookingId") Long transportBookingId){
        log.info("Request to authorize transport booking with id: {}", transportBookingId);
        return  ResponseEntity.ok(transportBookingService.authorizeTransportBooking(transportBookingId));
    }


    @PutMapping(value = "vehicle-log/authorize/{vehicleLogId}/{authorizer}")
    public ResponseEntity<VehicleLogDto> authorizeVehicleLog(@PathVariable("vehicleLogId") Long vehicleLogId, @PathVariable("authorizer") String authorizer){
        log.info("Request to authorize transport booking with id: {}", vehicleLogId);
        return  ResponseEntity.ok(transportService.authorizeVehicleLog(vehicleLogId, authorizer));
    }

    @PutMapping(value = "vehicle-log/comment/{vehicleLogId}/{comment}")
    public ResponseEntity<VehicleLogDto> commentVehicleLog(@PathVariable("vehicleLogId") Long vehicleLogId, @PathVariable("comment") String comment){
        log.info("Request to authorize transport booking with id: {}", vehicleLogId);
        return  ResponseEntity.ok(transportService.commentVehicleLog(vehicleLogId, comment));
    }

    @PutMapping(value = "transport-booking/authorization-reminder/{transportBookingId}")
    public ResponseEntity<String> authorizationReminder(@PathVariable("transportBookingId") Long transportBookingId, boolean bool){
        log.info("Request to send an authorization reminder for transport booking with id: {} and boolean value of {}", transportBookingId, bool);
        return ResponseEntity.ok(transportBookingService.authorizationReminder(transportBookingId, bool));
    }

}
