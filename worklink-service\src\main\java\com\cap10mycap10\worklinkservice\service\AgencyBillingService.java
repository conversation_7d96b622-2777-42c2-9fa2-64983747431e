package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.billing.AgencyBillDto;
import com.cap10mycap10.worklinkservice.dto.billing.AgencyBillStatusDto;
import com.cap10mycap10.worklinkservice.dto.billing.ShiftBillDto;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;

import java.time.LocalDate;
import java.util.List;


public interface AgencyBillingService {

    List<AgencyBillDto> findAllPaged(PageRequest of, Long agentId, LocalDate startDate, LocalDate endDate);
    List<AgencyBillDto> findAllPendingPaged(PageRequest of, Long agentId, LocalDate startDate, LocalDate endDate);
    AgencyBillDto findById(Long id);

    void createDebitOrders(ShiftBillDto shiftBillDto);

    ResponseEntity SetDebitNotePaidStatus(AgencyBillStatusDto agencyBillStatusDto);
}
