//package com.cap10mycap10.worklinkservice.dto.transport;
//
//
//import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
//import com.cap10mycap10.worklinkservice.enums.*;
//import com.cap10mycap10.worklinkservice.model.Vehicle;
//import com.fasterxml.jackson.annotation.JsonFormat;
//import lombok.EqualsAndHashCode;
//import lombok.Getter;
//import lombok.Setter;
//
//import jakarta.persistence.*;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.util.List;
//import java.util.Set;
//
//@Getter
//@Setter
//@EqualsAndHashCode
//public class TransportResultDto {
//
//    private Long id;
//    private Long clientId;
//    private String clientName;
//    private String agencyName;
//    private Set<Long> transportLegibleAgencyIDs;
//    private Set<String> transportLegibleAgencyNames;
//    private Long pickupLocationDirectorateId;
//    private String pickupLocationName;
//    private String pickupLocationPostCode;
//    private String pickupLocationContactNumber;
//    private String destination;
//    private String pickupPostCode;
//    private String destinationPostCode;
//    private String destinationContactNumber;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm")
//    private LocalDateTime dateTimeRequired;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm")
//    private LocalDateTime dateTimeBooked;
//    @Enumerated(EnumType.STRING)
//    private Gender passengerGender;
//    private String reasonForTransport;
//    //    Current Risks
//    private String pmeds;
//    private Level assaultStaff;
//    private Level physicalAggression;
//    private Level verballyAggressive;
//    private Level selfHarm;
//    private Level absconsionRisk;
//    private Level sexuallyInappropriate;
//    private String otherRisks;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
//    private LocalDateTime end;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
//    private LocalDateTime start;
//    private int passengerAge;
//    private String damageDoc;
//
//    private String assaultStaffDesc;
//    private String selfHarmDesc;
//    private String physicalAggressionDesc;
//    private String verballyAggressiveDesc;
//    private String absconsionRiskDesc;
//    private String genderIssuesDesc;
//    private String racialIssuesDesc;
//    private String mentalHealthStatus;
//    private String sexuallyInappropriateDesc;
//
//
//    private String mobilityIssues;
//    private String patientName;
//    @Enumerated(EnumType.STRING)
//    private TransportStatus transportStatus;
//    private String passengerAdditionalRisks;
//    private String passengerWalkInfo;
//    private String bpostCode;
//    private Integer breakTime;
//    private String escortServiceRisk;
//    private Boolean isPassengerAwareOfTransport;
//    private Integer wardEscort;
//    private Boolean passengerRequiresRestraints;
//    private boolean fullyBooked;
//    private WorkerResultDto teamLeader;
//    private WorkerResultDto driver;
//    private Vehicle vehicle;
//    private String reasonsForRestrains;
//    private String specialRequests;
//    List<TransportWorkerSpecDto> workerSpec;
//    private String createdBy;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm")
//    private LocalDateTime lastModifiedDate;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
//    private LocalDateTime finishTime;
//    private Long mileage;
//    private String patientDocumentOne;
//    private String patientDocumentTwo;
//    private String patientDocumentThree;
//    private Integer transportRating;
//
//
//    private String mha;
//    private String pcaddress;
//    private String pcemail;
//    private String pcbusiness;
//    private String pward;
//    private String pname;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
//    private LocalDate pdob;
//    private String nhs;
//    private String diagnosis;
//
//    private String dname;
//    private String dbusiness;
//    private String dward;
//    private String dcontact;
//    private String demail;
//
//    private Level genderIssues;
//    private Level racialIssues;
//    private String medication;
//    private String physicalHealth;
//    private String rapidTranq;
//    private String infectionControl;
//    private Boolean covid;
//    private String offerFood;
//    private String allergies;
//
//    private String submittedBy;
//    private String semail;
//    private String sphone;
//    private String pOrderNum;
//    private String sbsCode;
//    private String bname;
//    private String baddress;
//    private String binvoice;
//    private String bphone;
//    private String bemail;
//    private String authority;
//    private Boolean walk;
//    private String walkInfo;
//}
