package com.cap10mycap10.worklinkservice.dto.shift;

import com.cap10mycap10.worklinkservice.enums.BookingType;
import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.*;

@Data

public class BookingResultDto {

    private Long id;
    private Long workerSpecId;
    private String shiftLocation;
    private String postCode;
    private String phoneNumber;
    private Boolean released;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime releaseDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime actualStart;
    private String directorate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime start;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime end;
    private String applicantCount;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
//    private LocalTime shiftStartTime;
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm")
//    private LocalTime shiftEndTime;
    private String bpostCode;
    private String cost;
    private String trainer;
    private String trainingName;
    private String payer;
    private String breakTime;
    private Gender gender;
    private Long lastAuthorisationReminder;
    private Boolean isAgencyBilled;
    private String shiftType;
    private String assignmentCode;
    private String notes;
    private Boolean showNoteToFw;
    private Boolean showNoteToAgency;
    private Boolean requireApplicationByWorkers;
    private int hoursBeforeBroadcasting;
    private String shiftStatus;
    private ShiftStatus appliedStatus;
    private String agency;
    private Long agencyId;
    private String worker;
    private Long workerId;
    private String client;
    private Long clientId;
    private String cancelledReason;
    private String queriedReason;
    private String createdBy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime appliedDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime bookedDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime authorizedDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime queriedDate;
    private BookingType bookingType;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime cancelledDate;

    private int numberOfStaff;
    private String pAddress;
    private String pPostCode;
    private String pWard;
    private String dAddress;
    private String dPostCode;
    private String dWard;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime lastModifiedDate;
    private List<Long> agencies = new ArrayList<>();

    private Boolean carPooling;
    private Set<Long> carPoolingShiftSet = new HashSet<>();

    private String carPoolingChatGroupName;

    private Long carPoolingChatGroupId;


    // Prevent duplicate bookings in sets
    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        BookingResultDto that = (BookingResultDto) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }
}
