package com.cap10mycap10.worklinkservice.enums;

import java.time.DayOfWeek;

public enum WeekDay {

    MON(DayOfWeek.MONDAY),
    TUE(DayOfWeek.TUESDAY),
    WED(DayOfWeek.WEDNESDAY),
    THU(DayOfWeek.THURSDAY),
    FRI(DayOfWeek.FRIDAY),
    SAT(DayOfWeek.SATURDAY),
    SUN(DayOfWeek.SUNDAY),
    MTF(DayOfWeek.MONDAY), // Monday to Friday
    SP; // Special or other like Holiday

    private final DayOfWeek dayOfWeek;

    private WeekDay(DayOfWeek dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }
    private WeekDay( ) {
        this((DayOfWeek) null);
    }
    private WeekDay(String day ) {
        this(DayOfWeek.MONDAY);
    }
   public DayOfWeek getDayOfWeek() {
               return dayOfWeek;
           }

}
