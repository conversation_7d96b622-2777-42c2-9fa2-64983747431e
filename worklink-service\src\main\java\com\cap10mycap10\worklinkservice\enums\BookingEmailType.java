package com.cap10mycap10.worklinkservice.enums;

/**
 * Enum representing different types of emails that can be sent for vehicle bookings.
 */
public enum BookingEmailType {
    BOOKING_PAID,           // Email sent when booking is paid
    BOOKING_RESERVED,       // Email sent when booking is reserved
    AGENCY_BOOKING_RECEIVED, // Email sent to agency when booking is received
    BOOKING_PAYMENT_FAILED, // Email sent when payment fails
    BOOKING_CANCELLED,      // Email sent when booking is cancelled
    BOOKING_COMPLETED,      // Email sent when booking is completed (with feedback prompt)
    BOOKING_REMINDER        // Email sent as reminder for upcoming bookings
}
