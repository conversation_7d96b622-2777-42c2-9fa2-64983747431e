package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.UploadFileResponse;

import com.cap10mycap10.worklinkservice.dto.file.FileDto;
import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.FileNotFoundException;
import com.cap10mycap10.worklinkservice.exception.FileStorageException;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.model.WorkerTraining;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;


import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

import static java.util.stream.Collectors.toSet;

/**
 * <AUTHOR>
 * @created 28/06/2020 - 2:13 PM
 */

@Slf4j
@Component
public class FileStorageService {

    @Autowired
    private DataBucketUtil dataBucketUtil;



    public FileDto uploadFile(MultipartFile file) {
       if (file.isEmpty())
            throw new BusinessValidationException("Uploaded file is cannot be empty.");

       String originalFileName = file.getOriginalFilename();
        if(originalFileName == null)
            throw new BusinessValidationException("Original file name is null");

        String extension = getFileExtension(file);
        String name = generateRandomString(10);


        originalFileName=name+"."+extension;

        Path path = new File(originalFileName).toPath();
        String contentType;
        try {
            contentType = Files.probeContentType(path);
        } catch (IOException e) {
            contentType = ".jpg";
        }

        return dataBucketUtil.uploadFile(file, "/kd/"+originalFileName, contentType);
    }


    public static String getFileExtension(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName != null) {
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
                return fileName.substring(dotIndex + 1);
            }
        }
        return "";
    }

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";


    public static String generateRandomString(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }


    private final Path fileStorageLocation;
    public FileStorageService(FileStorageProperties fileStorageProperties) {
        this.fileStorageLocation = Paths.get(fileStorageProperties.getUploadDir())
                .toAbsolutePath().normalize();
        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new FileStorageException("Could not create the directory where the uploaded files will be stored.");
        }
    }

    public UploadFileResponse saveFile(MultipartFile file) {

        val fileName = storeFile(file);
        return new UploadFileResponse(fileName);
    }

    private String storeFile(MultipartFile file) {
        String fileName = StringUtils.cleanPath(file.getOriginalFilename());
        try {
            validateFile(fileName);
            fileName = generateUniqueFileName(fileName);
            Path targetLocation = this.fileStorageLocation.resolve(fileName);
            log.info("Target location {}", targetLocation);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
            return fileName;
        } catch (IOException ex) {
            ex.printStackTrace();
            throw new FileStorageException("Could not store file " + fileName + ". Please try again!");
        }
    }

    Set<String> storeMultiple(Set<MultipartFile> files) {
        return files.stream()
                .filter(file -> file != null && file.getSize() != 0)
                .map(this::storeFile)
                .collect(toSet());
    }

    private void validateFile(String fileName) {
        if (fileName.contains("..")) {
            throw new FileStorageException("Sorry! Filename contains invalid path sequence " + fileName);
        }
    }

    public Resource loadFileAsResource(String fileName) {
        log.info("### file city {}", fileName);
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (resource.exists()) {
                return resource;
            } else {
                throw new FileNotFoundException("File not found " + fileName);
            }
        } catch (MalformedURLException ex) {
            throw new FileNotFoundException("File not found " + fileName);
        }
    }

    public String getFileName(String fileName) {
        log.info("### file city {}", fileName);
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).
                    toAbsolutePath().normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (resource.exists()) {
                return filePath.toString();
            } else {
                throw new FileNotFoundException("File not found " + fileName);
            }
        } catch (MalformedURLException ex) {
            throw new FileNotFoundException("File not found " + fileName);
        }
    }

    private String generateUniqueFileName(String fileName) {
        String fileExtension = "";
        try {
            fileExtension = fileName.substring(fileName.lastIndexOf("."));
        } catch (Exception ignored) {

        }
        return UUID.randomUUID().toString() + fileExtension;
    }

    public UploadFileResponse updateFile(MultipartFile file, String fileName) {
        try {
            Path targetLocation = this.fileStorageLocation.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
            return new UploadFileResponse(fileName);
        } catch (IOException ex) {
            ex.printStackTrace();
            throw new FileStorageException("Could not store file " + fileName + ". Please try again!");
        }
    }

}
