package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.note.NoteCreateDto;
import com.cap10mycap10.worklinkservice.dto.note.NoteUpdateDto;
//import com.cap10mycap10.worklinkservice.dto.note.INoteResultDto;
import com.cap10mycap10.worklinkservice.model.Note;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Set;

public interface NoteService {

    void addNote(NoteCreateDto noteCreateDto);

    void deleteNote(Long id);

    Note findById(Long id);

    Set<Note> findWorkerNotes(Long workerId, PageRequest of);

    Page<Note> findAllPaged(PageRequest of);

    Note save(NoteUpdateDto noteUpdateDto);

    Note getOne(Long id);
}
