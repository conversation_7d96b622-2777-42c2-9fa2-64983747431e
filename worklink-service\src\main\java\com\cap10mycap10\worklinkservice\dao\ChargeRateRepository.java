package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.cap10mycap10.worklinkservice.model.ChargeRate;
import com.cap10mycap10.worklinkservice.model.ShiftType;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChargeRateRepository extends JpaRepository<ChargeRate, Long> {
    List<ChargeRate> findByShiftTypeAndAssignmentCode(ShiftType shiftType, AssignmentCode assignmentCode);

}
