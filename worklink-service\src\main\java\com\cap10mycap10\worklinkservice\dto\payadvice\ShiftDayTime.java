package com.cap10mycap10.worklinkservice.dto.payadvice;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShiftDayTime {

    private String dayOfWeek;


    private LocalDateTime start;

    private LocalDateTime shiftStartTime;

    private LocalDateTime shiftEndTime;

    private String breakTime;

}
