package com.cap10mycap10.worklinkservice.mapper.agency;

import com.cap10mycap10.worklinkservice.auth.AuthenticationFacade;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyCreateDto;
import com.cap10mycap10.worklinkservice.enums.AgencyType;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.model.Address;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.service.ServicesService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

import static java.util.Objects.nonNull;

@Component
public class AgencyDtoToAgency implements Converter<AgencyCreateDto, Agency> {

    private final AuthenticationFacade authenticationFacade;
    private final ServicesService service;

    public AgencyDtoToAgency(AuthenticationFacade authenticationFacade, ServicesService service) {
        this.authenticationFacade = authenticationFacade;
        this.service = service;
    }

    @Override
    public Agency convert(AgencyCreateDto agencyCreateDto) {
        Agency agency = new Agency();

        if(nonNull(agencyCreateDto.getAddress())) {
            Address address = new Address();
            address.setFirstLine(agencyCreateDto.getAddress().getFirstLine());
            address.setTown(agencyCreateDto.getAddress().getTown());
            address.setPostcode(agencyCreateDto.getAddress().getPostcode());
            agency.setAddress(address);
        }

        agency.setAgencyType(agencyCreateDto.getAgencyType());
        if(agencyCreateDto.getAgencyType()== AgencyType.TRAINER)agency.setIsTrainer(true);
        if(agencyCreateDto.getAgencyType()== AgencyType.TRANSPORTER)agency.setIsTransporter(true);
        agency.setEmail(agencyCreateDto.getEmail());
        agency.setLogo(agencyCreateDto.getLogo());
        agency.setName(agencyCreateDto.getName());
        agency.setTelephone(agencyCreateDto.getTelephone());
        agency.setBillingEmail(agencyCreateDto.getBillingEmail());
        agency.setBaseCurrency(agencyCreateDto.getBaseCurrency() != null ?
                              agencyCreateDto.getBaseCurrency().toUpperCase() : null);
        agency.setCreatedBy(authenticationFacade.getAuthentication().getName());
        agency.setStatus(Status.ACTIVE);
        if(nonNull(agencyCreateDto.getServiceId())) agency.setService(service.getOne(agencyCreateDto.getServiceId()));
        return agency;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
