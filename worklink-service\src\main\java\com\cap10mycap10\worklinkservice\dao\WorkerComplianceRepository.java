package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.dto.workercompliance.IWorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.enums.ComplianceStatus;
import com.cap10mycap10.worklinkservice.model.WorkerCompliance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface WorkerComplianceRepository extends JpaRepository<WorkerCompliance, Long> {

    @Query(value = "select *  \n" +
            "from worker_compliance\n" +
            "where worker_id = ?1" , nativeQuery = true)
    Page<IWorkerComplianceResultDto> findAllWorkerCompliances(Long workerId, PageRequest of);


    @Query(value = "select * \n" +
            "from worker_compliance\n" +
            "where worker_id = ?1" , nativeQuery = true)
    Page<WorkerCompliance> findAllWorkerCompliances2(Long workerId, PageRequest of);


    @Query(value = "select * from worker_compliance where id = ?1 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    WorkerComplianceResultDto findByIdn(Long id);

    @Query(value = "select *  \n" +
            "from worker_compliance\n" +
            "where status = ?1" , nativeQuery = true)
    List<WorkerCompliance> findAllByComplianceStatus(ComplianceStatus approved);
}
