package com.cap10mycap10.worklinkservice.dto.training.iHasco;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

@Data
public class CertificateDto {
    private String archived_at;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completed_at;
    private Long course_id;
    private String cpd_time;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime created_at;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expires_at;
    private String flag;
    private String historic_data_count;
    private Long id;
    private String pass_mark;
    private String status_code;
    private String test_attempts;
    private String test_score;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updated_at;
    private Long user_id;
}
