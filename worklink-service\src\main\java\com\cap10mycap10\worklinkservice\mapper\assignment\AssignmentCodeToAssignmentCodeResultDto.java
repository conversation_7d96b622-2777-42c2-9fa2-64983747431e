package com.cap10mycap10.worklinkservice.mapper.assignment;

import com.cap10mycap10.worklinkservice.dto.assignementcode.AssignmentCodeResultDto;
import com.cap10mycap10.worklinkservice.model.AssignmentCode;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

@Component
public class AssignmentCodeToAssignmentCodeResultDto implements Converter<AssignmentCode, AssignmentCodeResultDto> {
    @Override
    public AssignmentCodeResultDto convert(AssignmentCode assignmentCode) {
        AssignmentCodeResultDto resultDto = new AssignmentCodeResultDto();
        resultDto.setId(assignmentCode.getId());
        resultDto.setCode(assignmentCode.getCode());
        resultDto.setName(assignmentCode.getName());
        resultDto.setCreatedBy(assignmentCode.getCreatedBy());
        resultDto.setServiceName(assignmentCode.getServices().getName());

        return resultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
