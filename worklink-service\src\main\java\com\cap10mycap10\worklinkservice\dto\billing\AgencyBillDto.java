package com.cap10mycap10.worklinkservice.dto.billing;


import com.cap10mycap10.worklinkservice.enums.BillStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class AgencyBillDto {
    private Long id;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date issueDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dueDate;
    private String totalUnits;
    private BigDecimal chargeRate;
    private BigDecimal vatRate;
    private BigDecimal totalCharge;
    private BigDecimal discountCharge;
    private BigDecimal subTotal;
    private BigDecimal totalDue;
    private BillStatus status;
    private String notes;
    private String agency;
    private Long agentId;
    private String client;
    private Long shiftId;
    private String worker;
    private String billEmailAddress;
    private boolean paid;
    private String paymentRef;
    private Date paidDate;

}
