package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.shift.*;
import com.cap10mycap10.worklinkservice.model.Shift;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;

public interface ShiftService {

    List<BookingResultDto> save(List<ShiftCreateDto> shiftCreateDto);

    BookingResultDto findById(Long id);

    void authorisationReminder(Long id);

    List<Long> findByAgencyIdAndNotAdminBilled(Long agencyId);
    List<Shift> findAllByIds(List<Long> ids);

    List<BookingResultDto> findAll();

    Page<BookingResultDto> findAllPaged(PageRequest of, LocalDate startDate, LocalDate endDate);

    void deleteById(Long id);

    void save(ShiftUpdateDto shiftUpdateDto);

    void book(Long shiftId, Long agencyId, Long workerId) throws BusinessValidationException, JsonProcessingException;

    Page<BookingResultDto> findAllClientPaged(Long clientId, PageRequest of);

    void authorize(Long shiftId) throws BusinessValidationException;

    void release(Long shiftId, Long workerId) throws BusinessValidationException;

    void authorizeQueried(Long shiftId, Long clientId, LocalTime endTime) throws BusinessValidationException;

    ShiftReportStatus findAllShiftsByStatus();

    Page<BookingResultDto> findAllStatusPaged(String status, PageRequest of, LocalDate startDate, LocalDate endDate);

    Page<BookingResultDto> findAllStatusByClientPaged(Long clientId, String status, PageRequest of, LocalDate startDate, LocalDate endDate);
//    Page<BookingResultDto> findJobCount(Long agencyId, Long workerId, PageRequest of, LocalDate startDate, LocalDate endDate);

    void query(Long shiftId, Long workerId, String reason);

    void cancel(Long shiftId, Long clientId, String reason);

    Page<BookingResultDto> findAllPagedByStatus(PageRequest of, String status);

    Page<BookingResultDto> findAllStatusByClientIdPaged(Long clientId, String status, PageRequest of);
    Page<BookingResultDto> findJobCount(Long workerId);

    ShiftReportStatus findAllClientShiftsByStatus(Long clientId);

    ShiftReportStatus findAllAgencyShiftsByStatus(Long agencyId);

    List<IShiftReportStatus> findAllWorkerShiftsByStatus(Long workerId);


    void apply(Long shiftId, Long agencyId, Long workerId);



    void approve(Long shiftId, Long agencyId, Long workerId);

    void bookWorkerDirectly(Long shiftId, Long workerId);

    void cancelWorkerShift(Long shiftId, Long workerId, String reason);

    List<BookingResultDto> findAppliedShiftsForWorkerId(Long workerId, PageRequest of);

    List<BookingResultDto> filter(Long workerId, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    List<BookingResultDto> filterByAgent(Long workerId, Long agentId, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    List<BookingResultDto> filterByClient(Long workerId, Long clientId, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    List<BookingResultDto> filterByClientAndAgent(Long workerId, Long agentId, Long clientId, String location, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    List<BookingResultDto> filterByClientAndAgent(Long workerId, Long agentId, Long clientId, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    List<BookingResultDto> filterByClient(Long workerId, Long clientId, String location, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    List<BookingResultDto> filterByAgent(Long workerId, Long agentId, String location, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    List<BookingResultDto> filter(Long workerId, String location, LocalDate startDate, LocalDate endDate, String status, PageRequest of);

    void save(ShiftUpdateDto shiftUpdateDto, String reason);

    Shift getOne(Long shiftId);

    void save(Shift shift);


    Set<BookingResultDto> findAllShiftsForWorkerIdAndStatus(Long workerId, PageRequest of, String status);
    Page<IShiftCompliance> findShiftComplianceIssues(Long agencyId, PageRequest of);

    Set<BookingResultDto> findAllShiftsForWorkerBilling(Long workerId, Long agencyId, PageRequest of);

    List<BookingResultDto> findAllShiftsForMyAgencyPagedByStatus(Long workerId, String status, PageRequest of);

    Page<BookingResultDto> findAllShiftsForMyAgencyPaged(Long workerId, PageRequest of);

    void enableCarPoolingForEligibleShifts();

    List<ShiftCarPoolingDto> findAllWorkersForShiftCarPooling(Long shiftId);

}
