package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.service.VehicleBookingService;
import com.google.gson.JsonSyntaxException;
import com.stripe.Stripe;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.Event;
import com.stripe.model.checkout.Session;
import com.stripe.net.Webhook;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import javax.annotation.PostConstruct;

@RestController
@Slf4j
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class StripeWebhookController {

    @Value("${env.STRIPE_SECRET_KEY}")
    private String stripeApiKey;

    @Value("${env.STRIPE_WEBHOOK_SECRET}")
    private String endpointSecret;

    @PostConstruct
    public void init() {
        Stripe.apiKey = stripeApiKey;
    }

    @Autowired
    private VehicleBookingService vehicleBookingService;

    @PostMapping("/stripe/webhook")
    @ResponseStatus(HttpStatus.OK)
    public void handleStripeWebhook(@RequestBody String payload, @RequestHeader("Stripe-Signature") String sigHeader) throws StripeException {
        Event event = null;

        try {
            event = Webhook.constructEvent(payload, sigHeader, endpointSecret);
        } catch (JsonSyntaxException e) {
            // Invalid payload
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid payload");
        } catch (SignatureVerificationException e) {
            // Invalid signature
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid signature");
        }

        if ("charge.updated".equals(event.getType())
                || "checkout.session.async_payment_succeeded".equals(event.getType())) {
            Charge session = (Charge) event.getData().getObject();

            if (session != null) {
                vehicleBookingService.fulfillStripeCheckout(session);
            }
        }
    }


}
