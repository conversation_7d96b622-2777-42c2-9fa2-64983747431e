package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.enums.TrainingSessionStatus;
import com.cap10mycap10.worklinkservice.model.TrainingSession;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TrainingSessionRepository extends JpaRepository<TrainingSession, Long> {
    List<TrainingSession>  findAllByOrderByName();
    Page<TrainingSession> findAllByTrainingStatusOrderByStartDateTime(TrainingSessionStatus status, Pageable of);

    List<TrainingSession>  findAllByOrderByEndDateTime();
    List<TrainingSession> findByTrainingStatusNotOrderByStartDateTimeAsc(TrainingSessionStatus status);
//    Optional<TrainingSession> findById(Long id);


    @Query(value = "SELECT * \n" +
            "FROM training_session \n" +
            "WHERE id IN (\n" +
            "    SELECT training_session_id \n" +
            "    FROM training_session_agency \n" +
            "    WHERE agency_id = ?1\n" +
            ") \n" +
            "ORDER BY start_date_time;\n", nativeQuery = true)
    List<TrainingSession> findByAgencyId(Long id);
    List<TrainingSession> findByTrainingStatusOrderByStartDateTimeAsc(TrainingSessionStatus status);

    @Query(value = "SELECT * FROM training_session\n" +
            " WHERE training_session.id = ?2 \n" +
            " AND id IN (SELECT training_session_agency.training_session_id \n" +
            " FROM training_session_agency\n" +
            "WHERE agency_id = 4) \n" +
            " ORDER BY training_session.start_date_time;", nativeQuery = true)
    TrainingSession findByIdAndAgencyId(Long agencyId, Long trainingSessionId);

    @Query(value = "SELECT * FROM training_session\n" +
            " WHERE training_session.training_status = ?2 \n" +
            " AND id IN (SELECT training_session_agency.training_session_id \n" +
            " FROM training_session_agency\n" +
            "WHERE agency_id = ?1) \n" +
            " ORDER BY training_session.start_date_time;", nativeQuery = true)
    List<TrainingSession> findByAgencyIdAndTrainingStatus(Long agencyId, String trainingStatus1);

    @Query(value = "SELECT * FROM training_session\n" +
            " WHERE training_session.training_status = ?2 \n" +
            " AND trainer_id = ?1 \n" +
            " ORDER BY training_session.start_date_time;", nativeQuery = true)
    List<TrainingSession> findByTrainerIdAndTrainingStatus(Long agencyId, String trainingStatus1);



    @Query(value = "SELECT * FROM training_session WHERE training_id = ?2 AND (id IN (\n" +
            "         SELECT training_session_id FROM training_session_agency WHERE agency_id in (\n" +
            "             SELECT agency_id FROM agency_worker WHERE worker_id  = ?1\n" +
            "         )\n" +
            "      )  or publish_to_all_agencies = true )", nativeQuery = true)
    List<TrainingSession> findToBookByWorkerIdAndTrainingId(Long workerId, Long trainingId);



    @Query(value = "SELECT * FROM training_session  as ts\n" +
            "WHERE  \n" +
            "( ts.publish_to_all_agencies = true  OR  ts.id IN (SELECT training_session_id FROM training_session_agency  WHERE agency_id in (SELECT agency_id FROM agency_worker WHERE worker_id  = ?1)))\n" +
            "AND\n" +
            "ts.vacancies > (SELECT COUNT(*) from worker_training_session as wts WHERE wts.training_session_id = ts.id AND wts.training_status in ('APPROVED', 'WAITING_AUTHORIZATION', 'CLOSED', 'BOOKED') )\n" +
            "AND \n" +
            "datediff(ts.start_date_time, CURDATE()) >= 0\n" +
            "AND \n" +
            "ts.training_status = 'NEW'"
            , nativeQuery = true)
    List<TrainingSession> findToBookByWorkerId(Long workerId);

    @Query(value = "SELECT * FROM training_session WHERE id = (\n" +
            "         SELECT training_session_id FROM worker_training_session WHERE id = ?1 \n"+
            "      )", nativeQuery = true)
    TrainingSession findByBooking(Long workerId);

    List<TrainingSession> findByVacancies(int i);
}
