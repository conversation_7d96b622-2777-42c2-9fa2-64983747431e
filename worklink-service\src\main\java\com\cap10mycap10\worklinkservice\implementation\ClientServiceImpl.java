package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.dao.AgencyRepository;
import com.cap10mycap10.worklinkservice.dao.ClientRepository;
import com.cap10mycap10.worklinkservice.dao.ShiftRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerRepository;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.client.*;
import com.cap10mycap10.worklinkservice.dto.shift.IShiftReportStatus;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.feign.RegisterAgentAdminFeignClient;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.request.UserDto;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.response.UserResponse;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
import com.cap10mycap10.worklinkservice.mapper.client.ClientDtoToClient;
import com.cap10mycap10.worklinkservice.mapper.client.ClientToClientDto;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerToWorkerResultDto;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Client;
import com.cap10mycap10.worklinkservice.model.ClientDocs;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ClientService;
import com.cap10mycap10.worklinkservice.service.ServicesService;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import org.springframework.web.multipart.MultipartFile;

import jakarta.persistence.EntityNotFoundException;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Service
@Slf4j
public class ClientServiceImpl implements ClientService {

    @Autowired
    private ClientToClientDto toClientDto;

    @Value("${storage.volume.path}")
    private  String rootPath;
    private final ClientRepository clientRepository;
    private ObjectMapper objectMapper = new ObjectMapper();
    private final ClientDtoToClient toClient;
    private final AgencyService agencyService;
    private final AgencyRepository agencyRepository;
    private final AgencyToAgencyResultDto toAgencyResultDto;
    private final ShiftRepository shiftRepository;
    private final ServicesService servicesService;
    private final RegisterAgentAdminFeignClient registerAgentAdminFeignClient;
    private final WorkerRepository workerRepository;
    private final WorkerToWorkerResultDto toWorkerResultDto;

    public ClientServiceImpl(final ClientRepository clientRepository,
                             final ClientDtoToClient toClient,
                             final AgencyService agencyService,/* ApprovedAgencyRepository approvedAgencyRepository,*/ AgencyRepository agencyRepository, AgencyToAgencyResultDto toAgencyResultDto, ShiftRepository shiftRepository, ServicesService servicesService, RegisterAgentAdminFeignClient registerAgentAdminFeignClient, WorkerRepository workerRepository, WorkerToWorkerResultDto toWorkerResultDto) {
        this.clientRepository = clientRepository;
        this.toClient = toClient;
        this.agencyService = agencyService;
        this.agencyRepository = agencyRepository;
        this.toAgencyResultDto = toAgencyResultDto;
        this.shiftRepository = shiftRepository;
        this.servicesService = servicesService;
        this.registerAgentAdminFeignClient = registerAgentAdminFeignClient;
        this.workerRepository = workerRepository;
        this.toWorkerResultDto = toWorkerResultDto;
    }



    @Override
    @Transactional
    public ClientDto save(ClientDto clientDto) {
        Client client = clientRepository.findByEmail(clientDto.getEmail());
        if(nonNull(client)){
            throw new BusinessValidationException("This account already exists, try resetting the password");
        }
        Client savedClient = saveClient(clientDto);
        saveClientAdmin(savedClient, clientDto);
        log.info("Client created successfully");
        return toClientDto.convert(savedClient);
    }

    @Override
    public Client saveClient(ClientDto clientDto) {
        Client savedClient;
        Client client = clientRepository.findByEmail(clientDto.getEmail());

        if(!nonNull(client)){
            client = new Client();
            client.setName(clientDto.getName());
            client.setAddress(clientDto.getAddress());
            client.setLogo(clientDto.getLogo());
            client.setEmail(clientDto.getEmail());
            client.setBillingEmail(clientDto.getBillingEmail());
            client.setTelephone(clientDto.getTelephone());
            client.setSbsCode(clientDto.getSbsCode());
            client.setPurchaseOrder(clientDto.getPurchaseOrder());
            client.setStatus(Status.ACTIVE);
            if(nonNull(clientDto.getServiceId()))
                client.setService(servicesService.getOne(clientDto.getServiceId()));

            savedClient = clientRepository.save(client);
        }else savedClient = client;


        if(nonNull(clientDto.getAgencyId())) {
            Agency agency = agencyService.getOne(clientDto.getAgencyId());
            agency.addClient(savedClient);
            agencyRepository.saveAndFlush(agency);
            savedClient = clientRepository.save(savedClient);
        }

        return savedClient;
    }



    @Override
    public List<ClientDto> findAll() {
        return clientRepository.findAll().stream().map(toClientDto::convert).collect(Collectors.toList());
    }

    private void saveClientAdmin(Client client, ClientDto clientDto){
            UserDto userCreationDto = new UserDto();
            userCreationDto.setFirstName(clientDto.getAdministratorCreateDto().getFirstname());
            userCreationDto.setLastName(clientDto.getAdministratorCreateDto().getLastname());
            userCreationDto.setEmail(clientDto.getAdministratorCreateDto().getAdminEmail());
            userCreationDto.setUsername(clientDto.getAdministratorCreateDto().getAdminEmail());
            userCreationDto.setRoleId(2L);
            userCreationDto.setUserType("CLIENT");
            userCreationDto.setClientId(client.getId());
            registerAgentAdminFeignClient.registerUserAccountFeign(userCreationDto);

    }

    @Transactional
    @Override
    public ClientDto update(ClientDto clientDto) {
        try {
            Client client = getOne(clientDto.getId());

            updateIfNotNull(client::setName, clientDto.getName());
            updateIfNotNull(client::setAddress, clientDto.getAddress());
            updateIfNotNull(client::setEmail, clientDto.getEmail());
            updateIfNotNull(client::setBillingEmail, clientDto.getBillingEmail());
            updateIfNotNull(client::setTelephone, clientDto.getTelephone());
            updateIfNotNull(client::setSbsCode, clientDto.getSbsCode());
            updateIfNotNull(client::setLogo, clientDto.getLogo());
            updateIfNotNull(client::setPurchaseOrder, clientDto.getPurchaseOrder());
            updateIfNotNull(client::setStatus, Status.ACTIVE);

            if (clientDto.getServiceId() != null) {
                client.setService(servicesService.getOne(clientDto.getServiceId()));
            }

            Client savedClient = clientRepository.save(client);

            if (clientDto.getAgencyId() != null) {
                Agency agency = agencyService.getOne(clientDto.getAgencyId());
                agency.addClient(savedClient);
                agencyRepository.saveAndFlush(agency);
            }
            log.info("Client updated successfully");
            return toClientDto.convert(savedClient);
        } catch (DataIntegrityViolationException | FeignException e) {
            log.error(e.getMessage());
            throw new BusinessValidationException("Client email already exists");
        }
    }

    private <T> void updateIfNotNull(Consumer<T> setter, T value) {
        if (value != null) {
            setter.accept(value);
        }
    }


    @Override
    public ClientDto findById(Long id) {
        return toClientDto.convert(getOne(id));
    }


    @Override   
    public Page<ClientDto> findAllPaged(String searchQuery, PageRequest pageRequest, Long agencyId) {
        return clientRepository.findClientsByAgencyIdAndSearchQuery(agencyId, searchQuery, pageRequest)
                .map(toClientDto::convert);
    }

    @Override
    public Page<ClientDto> findAllPaged(String searchQuery, PageRequest pageRequest) {
        return findAllPaged(searchQuery, pageRequest, null);
    }

    @Override
    public Page<ClientDto> findAllPagedClients(Long workerId, PageRequest of) {
        return clientRepository.findAllClientsPagedByWorkerId(workerId, of).map(toClientDto::convert);
    }

    @Override
    public Page<ClientDto> searchClientForAgency(Long agencyId,String searchCriteria, PageRequest of) {
        return clientRepository.searchClientForAgency(agencyId,searchCriteria, of).map(toClientDto::convert);
    }

    @Override
    public void deleteById(Long id) {
        Client client = getOne(id);
        try {
            clientRepository.deleteById(id);
            clientRepository.flush();
        } catch (Exception e) {
            throw new BusinessValidationException("Client cannot be deleted");
        }
    }

    @Override
    public Client getOne(Long id) {
        return clientRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Client not found"));
    }

    @Override
    @Transactional
    public void addAgency(Long clientId, Long agencyId) {

    }

    @Override
    public Page<AgencyResultDto> findAllAgenciesPaged(Long clientId, PageRequest of) {
        return agencyRepository.findAllByClient(clientId, of)
                .map(toAgencyResultDto::convert);
    }

    @Override
    public int findNumberOfClients() {
        return clientRepository.findNumberOfClients();
    }

    @Override
    public IShiftReportStatus getStats(Long id) {
        return null;
    }

    @Override
    public ClientStats getMyStats(Long id) {
        int numberOfAgencies = agencyRepository.countAgenciesByClientId(id);
        int numberofShifts = shiftRepository.findNumberOfShiftsByClient(id);
        int numberOfWorkerShifts = shiftRepository.findNumberOfShiftsBookedAndClientId(id);
        return new ClientStats(numberOfAgencies, numberofShifts, numberOfWorkerShifts);
    }


    @Override
    public void linkAgentClient(Long agentId, Long clientId) {
        try {
            Client client = getOne(clientId);
            Agency agency = agencyService.getOne(agentId);
            agency.addClient(client);
            agencyRepository.save(agency);
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }

    }

    @Override
    public UserResponse findAdminById(Long id) {
        return registerAgentAdminFeignClient.getUserByClientId(id);
    }

    @Override
    public Page<WorkerResultDto> findAllWorkers(Long clientId, PageRequest of) {
        return  workerRepository.findClientWorkers(clientId, of).map(toWorkerResultDto::convert);
    }


    private List<Agency> findAgency(Long clientId) {
        return null;
    }


    @Override
    public void addClientDocs(Long clientId, Set<ClientDocs> docs) {
        Client client = getOne(clientId);
        client.addDocuments(docs);
        clientRepository.save(client);
    }

    @Override
    public void addProfilePic(Long agencyId, MultipartFile file) {
        log.info("Request to upload a payslip");
        String name = "profile.png";






        if (!file.isEmpty() ) {
            try {
                byte[] bytes = file.getBytes();

                File dir = new File(rootPath + File.separator + "client"+ File.separator + agencyId  );

                if (!dir.exists())  dir.mkdirs();

                // Create the file on server
                File serverFile = new File(dir.getAbsolutePath()
                        + File.separator + name);
                BufferedOutputStream stream = new BufferedOutputStream(
                        new FileOutputStream(serverFile));
                stream.write(bytes);
                stream.close();


                log.info("StripeServiceGoogle server File Location="
                        + serverFile.getAbsolutePath());

                log.info("Uploaded");

            } catch (Exception e) {
                log.info("You failed to upload");
                log.error(e.getMessage());
                throw new BusinessValidationException("Upload failed.");

            }

        } else {
            log.info("You failed to upload");
            throw new BusinessValidationException("Uploaded file is not empty.");

        }
    }


}
