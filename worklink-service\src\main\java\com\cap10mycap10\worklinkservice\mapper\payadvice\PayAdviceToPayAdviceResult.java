package com.cap10mycap10.worklinkservice.mapper.payadvice;

import com.cap10mycap10.worklinkservice.dao.PayslipRepository;
import com.cap10mycap10.worklinkservice.dto.payadvice.PayAdviceResult;
import com.cap10mycap10.worklinkservice.mapper.shiftexpenseclaim.ShiftExpenseClaimToShiftExpenseClaimResultDto;
import com.cap10mycap10.worklinkservice.model.PayAdvice;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;

@Component
public class PayAdviceToPayAdviceResult implements Converter<PayAdvice, PayAdviceResult> {


    @Autowired
    private final PayslipRepository payslipRepository;
    @Autowired
    private  WorkerService workerService;

    private final PayAdviceItemToPayAdviceItemResult toPayAdviceItemResult;
    @Autowired
    private  ShiftExpenseClaimToShiftExpenseClaimResultDto toShiftClaimResult;

    public PayAdviceToPayAdviceResult(PayslipRepository payslipRepository, PayAdviceItemToPayAdviceItemResult toPayAdviceItemResult) {
        this.payslipRepository = payslipRepository;
        this.toPayAdviceItemResult = toPayAdviceItemResult;
    }

    @Override
    public PayAdviceResult convert(PayAdvice payAdvice) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd LLLL yyyy");

        PayAdviceResult payAdviceResult = new PayAdviceResult();
        payAdviceResult.setPayAdviceStatus(payAdvice.getPayAdviceStatus().name());
        payAdviceResult.setId(payAdvice.getId());

        payAdviceResult.setWorkerGross(payslipRepository.findWorkerGrossPay(payAdvice.getWorkerId(), payAdvice.getAgentId()));

        payAdviceResult.setPayAdviceDate(payAdvice.getPayAdviceDate().format(formatter));
        payAdviceResult.setAgentId(payAdvice.getAgentId());
        payAdviceResult.setWorkerId(payAdvice.getWorkerId());

        Worker worker = workerService.getOne(payAdvice.getWorkerId());
        payAdviceResult.setWorkerName(worker.getFirstname()+" "+worker.getLastname());
        payAdviceResult.setPaymentRef(payAdvice.getPaymentRef());
        payAdviceResult.setTotalAmount(payAdvice.getTotalAmount());
        payAdviceResult.setPayAdviceItemResult(toPayAdviceItemResult.convert(payAdvice.getPayAdviceItems()));
        payAdviceResult.setShiftExpenseClaims( toShiftClaimResult.convert(payAdvice.getShiftExpenseClaims()) );
        return payAdviceResult;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
