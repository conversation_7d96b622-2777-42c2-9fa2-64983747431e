package com.cap10mycap10.worklinkservice.model;
import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.enums.LogStatus;
import com.cap10mycap10.worklinkservice.enums.VehicleLogType;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import jakarta.persistence.*;

import static java.util.Objects.nonNull;

@EqualsAndHashCode(callSuper = true)
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleLog extends AbstractAuditingEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Float startMileage;
    private Float endMileage;
    private Float totalMileage;
    private String notes;
    private String damageDoc;
    private String damageDescriptions;
    private String feedback;
    private String damageReport;

    private Boolean indicators;
    private Boolean drivingControls;
    private Boolean wheelCondition;
    private Boolean tyreInflation;
    private Boolean brakes;
    private Boolean windscreen;
    private Boolean mirrors;
    private Boolean speedometer;
    private Boolean battery;
    private Boolean fuel;
    private Boolean seatbelt;
    private Boolean doors;
    private Boolean oil;
    private Boolean engineCheckLight;
    private Boolean warningLight;
    private Boolean litter;
    private Boolean hardSurface;
    private Boolean seats;
    private Boolean equipment;
    private Boolean sanitizer;
    private Boolean cellArea;
    private Boolean lamp;
    private Boolean sideReapter;
    private Boolean stoplamp;
    private Boolean reflectors;
    private Boolean markers;
    private Boolean warningdevices;
    private Boolean mirror;
    private Boolean drivingcontrol;
    private Boolean body;
    private Boolean horn;
    private Boolean wipers;
    private Boolean washers;
    private Boolean fluidleaks;
    private Boolean exhaust;
    private Boolean coolant;
    private Boolean instrumentalPanel;
    private Boolean adblue;
    private Boolean trailercoupling;
    private String approvedBy;
    private String approvedByName;
    private String comment;

//    @ManyToOne
//    @EqualsAndHashCode.Exclude
//    @ToString.Exclude
//    @JoinColumn(nullable = false)
//    private Agency agency;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinColumn(nullable = false)
    private Worker worker;

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @JoinColumn(nullable = false)
    @ToString.Exclude
    @JsonIgnore
    private Vehicle vehicle;

    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false)
    private LogStatus status = LogStatus.NEW;

    @Enumerated(value = EnumType.STRING)
    @Column(nullable = false)
    private VehicleLogType type = VehicleLogType.DRIVER;

    @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinColumn(unique = true)
    private Transport transport;

    public VehicleLog(Transport transport, Worker worker){
        if(!nonNull(transport.getVehicle()))
            throw new BusinessValidationException("A vehicle has not been assigned to this job yet.");
        this.transport = transport;
        transport.setVehicleLog(this);
        this.setWorker(worker);
        this.vehicle = transport.getVehicle();
    }

    public VehicleLogDto toVehicleLogDto(){
        return new VehicleLogDto(this);
    }


    public void setEndMileage(float endMileage) {
        if(startMileage>endMileage)
            throw new BusinessValidationException("Start mileage cannot be greater than end mileage");
        this.endMileage = endMileage;
        this.totalMileage = endMileage - startMileage;
        vehicle.setMileage(endMileage);
    }

    public void setStartMileage(Float startMileage) {
        this.startMileage = startMileage;
        if(nonNull(endMileage)&&startMileage>endMileage)
            throw new BusinessValidationException("Start mileage cannot be greater than end mileage");
        if(!nonNull(this.endMileage))
            vehicle.setMileage(startMileage);
    }

    public void updateCleaningCheck(VehicleLogDto asset) {

        this.litter = asset.getLitter();
        this.hardSurface = asset.getHardSurface();
        this.seats = asset.getSeats();
        this.equipment = asset.getEquipment();
        this.sanitizer = asset.getSanitizer();
        this.cellArea = asset.getCellArea();
        this.notes = asset.getNotes();
    }

    public void updateVehicleCheck(VehicleLogDto asset) {
        this.damageReport = asset.getDamageReport();
        this.indicators = asset.getIndicators();
        this.drivingControls = asset.getDrivingControls();
        this.wheelCondition = asset.getWheelCondition();
        this.damageDescriptions = asset.getDamageDescriptions();
        this.tyreInflation = asset.getTyreInflation();
        this.brakes = asset.getBrakes();
        this.windscreen = asset.getWindscreen();
        this.mirrors = asset.getMirrors();
        this.speedometer = asset.getSpeedometer();
        this.battery = asset.getBattery();
        this.fuel = asset.getFuel();
        this.seatbelt = asset.getSeatbelt();
        this.doors = asset.getDoors();
        this.oil = asset.getOil();
        this.engineCheckLight = asset.getEngineCheckLight();
        this.warningLight = asset.getWarningLight();
        this.litter = asset.getLitter();
        this.hardSurface = asset.getHardSurface();
        this.seats = asset.getSeats();
        this.equipment = asset.getEquipment();
        this.sanitizer = asset.getSanitizer();
        this.cellArea = asset.getCellArea();
        this.lamp = asset.getLamp();
        this.sideReapter = asset.getSideReapter();
        this.stoplamp = asset.getStoplamp();
        this.reflectors = asset.getReflectors();
        this.markers = asset.getMarkers();
        this.warningdevices = asset.getWarningdevices();
        this.mirror = asset.getMirror();
        this.drivingcontrol = asset.getDrivingcontrol();
        this.body = asset.getBody();
        this.horn = asset.getHorn();
        this.wipers = asset.getWipers();
        this.washers = asset.getWashers();
        this.fluidleaks = asset.getFluidleaks();
        this.exhaust = asset.getExhaust();
        this.coolant = asset.getCoolant();
        this.instrumentalPanel = asset.getInstrumentalPanel();
        this.adblue = asset.getAdblue();
        this.trailercoupling = asset.getTrailercoupling();
        
    }
}
