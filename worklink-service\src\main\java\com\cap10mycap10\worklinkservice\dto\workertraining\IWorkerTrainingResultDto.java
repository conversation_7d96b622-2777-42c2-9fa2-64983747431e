package com.cap10mycap10.worklinkservice.dto.workertraining;

import com.cap10mycap10.worklinkservice.enums.TrainingType;

public interface IWorkerTrainingResultDto {

    Long getId();

    String getCode();
    String getDocument();

    String getName() ;
//    Boolean getUploaded() ;

    TrainingType getType();

    String getDescription();

    String getTrainingDate();

    String getTrainingExpiry();

    String getWorkerId();
    String getStatus();

    String getAgencyId();

    String getTrainingId();
    String getHascoCourseId();
    String getHascoId();
}
