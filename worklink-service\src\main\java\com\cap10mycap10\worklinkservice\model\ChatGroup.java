package com.cap10mycap10.worklinkservice.model;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
public class ChatGroup {

    public ChatGroup(String name){
        this.groupName = name;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true)
    private String groupName;

    @OneToMany(mappedBy = "chatGroup", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<ChatGroupMessage> messages = new ArrayList<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "worker_chat_group",
            joinColumns = @JoinColumn(name = "chat_group_id"),
            inverseJoinColumns = @JoinColumn(name = "worker_id")
    )
    @JsonIgnore
    private Set<Worker> chatGroupMembers = new HashSet<>();

    @OneToMany(mappedBy = "shiftChatGroup", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Shift> shift = new ArrayList<>();

    @Override
    public String toString() {
        return "ChatGroup{" +
                "id=" + id +
                ",groupName='" + groupName + '\'' +
                ",messages='" + messages + '\'' +
                ",chatGroupMembers='" + chatGroupMembers.stream().map(Worker::getFirstname).collect(Collectors.toList()) + '\'' +
                ",shift='" + shift.stream().map(Shift::getId).collect(Collectors.toList()) + '\'' +
                '}';
    }
}
