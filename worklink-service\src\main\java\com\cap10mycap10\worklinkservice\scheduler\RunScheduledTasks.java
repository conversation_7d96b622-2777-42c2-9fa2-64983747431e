package com.cap10mycap10.worklinkservice.scheduler;


import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.deputy.*;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.dto.shiftdirectorate.ShiftDirectorateCreateDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerCreateDto;
import com.cap10mycap10.worklinkservice.enums.*;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.service.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoUnit.HOURS;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Service
@EnableScheduling
public class RunScheduledTasks {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    WorkerService workerService;

    @Autowired
    ShiftDirectorateService directorateService;
    @Autowired
    WorkerRepository workerRepository;

    @Autowired
    WorkerTrainingService workerTrainingService;
    @Autowired
    TrainingService trainingService;

    @Autowired
    ComplianceService complianceService;

    @Autowired
    NotificationService notificationService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    PushNotification pushNotification;

    @Autowired
    WorkerComplianceRepository workerComplianceRepository;

    @Autowired
    AgencyService agencyService;
    @Autowired
    TrainingRepository trainingRepository;
    @Autowired
    WorkerTrainingRepository workerTrainingRepository;

    @Autowired
    private ShiftRepository shiftRepository;

    @Autowired
    private TrainingSessionRepository trainingSessionRepository;
    @Autowired
    private WorkerTrainingSessionRepository workerTrainingSessionRepository;
    @Autowired
    private TransportService transportService;
    @Autowired
    private VehicleBookingService vehicleBookingService;
    @Autowired
    private PromotionService promotionService;



    // Minute cron
    @Scheduled(cron = "0 */1 * * * ?")
    public void mainScheduler() {
        try {
            transportService.beginPendingJobs();
        } catch (Exception e) {
            log.error("{}", e);
        }

        try {
            transportService.expireUnbookedJobs();
        } catch (Exception e) {
            log.error("{}", e);
        }

        try {
            vehicleBookingService.sendRentalReminders();
        } catch (Exception e) {
            log.error("{}", e);
        }

        try {
            vehicleBookingService.checkForUnpaidBookings();
        } catch (Exception e) {
            log.error("{}", e);
        }



        try {
            promotionService.modifyPendingPromotions();
        } catch (Exception e) {
            log.error("{}", e);
        }

        try {
            promotionService.addVehiclesToPromotions();
        } catch (Exception e) {
            log.error("Error adding vehicles to promotions: {}", e);
        }

    }

    //Hourly cron
    @Scheduled(cron = "0 0 * * * *")
    public void hourScheduler() {

    }


    // Gets new shifts that have passed. And sets them to expired.
    @Scheduled(cron = "*/5 * * * * ?")
    public void checkShiftStatus() {
        List<Shift> newShiftNotElapsed = shiftRepository.findAllByStatus(ShiftStatus.NEW);
        for (Shift shift : newShiftNotElapsed) {
            if (nonNull(shift.getStart()) && dateDiff(LocalDateTime.now(), shift.getStart())) {
                if (LocalDateTime.now().isAfter(shift.getStart())) {
                    shift.setStatus(ShiftStatus.EXPIRED);
                    shiftRepository.save(shift);
                }
                if (LocalDateTime.now().isEqual(shift.getStart())) {
                    if (LocalTime.now().isAfter( shift.getStart().toLocalTime())) {
                        shift.setStatus(ShiftStatus.EXPIRED);
                        shiftRepository.save(shift);
                    }
                }
            }
        }
    }

    // Moved authorized shifts that have been processed by admin, agency and client to billed status
    @Scheduled(cron = "*/5 * * * * ?")
    public void moveShiftsToBilled() {
        List<Shift> newShiftNotElapsed = shiftRepository.findAllByStatus(ShiftStatus.AUTHORIZED);

        for (Shift shift : newShiftNotElapsed) {
            if (
                    Boolean.TRUE.equals(shift.getIsAdminBilled())  &&
                            Boolean.TRUE.equals(shift.getIsAgencyBilled()) &&
                            shift.getShiftWorkerStatus()==ShiftStatus.PAID) {


                shift.setStatus(ShiftStatus.BILLED);
                shiftRepository.save(shift);
                log.info("Shift moved to billed: {}", shift);

            }
        }
    }


    @Transactional
    @Scheduled(cron = "*/5 * * * * ?")
    public void trainingSessionStatus() {
        List<TrainingSession> sessions = trainingSessionRepository.findByTrainingStatusNotOrderByStartDateTimeAsc(TrainingSessionStatus.CLOSED);
        List<TrainingSession> trainingSessionsWithZeroVacancies = trainingSessionRepository.findByVacancies(0);
        LocalDateTime now = LocalDateTime.now();

        AtomicBoolean goodBookings = new AtomicBoolean(true);
        for (TrainingSession session : sessions){
            goodBookings.set(true);
            session.getWorkerTrainingSessions().forEach(w->{
                if(
                        w.getTrainingStatus()== WorkerTrainingSessionStatus.NEW ||
                                w.getTrainingStatus()== WorkerTrainingSessionStatus.WAITING_AUTHORIZATION ||
                                w.getTrainingStatus()== WorkerTrainingSessionStatus.BOOKED
                ){
                    goodBookings.set(false);
                }

            });

            if (now.isAfter(session.getEndDateTime()) && goodBookings.get()) {
                session.setTrainingStatus(TrainingSessionStatus.CLOSED);
                trainingSessionRepository.save(session);
            }
        }

    }


    // Manage worker bookings statuses
    @Scheduled(cron = "*/5 * * * * ?")
    public void workerTrainingSessionStatus() {
        List<WorkerTrainingSession> sessions = workerTrainingSessionRepository.findAllCompleteBooked();
        for (WorkerTrainingSession session : sessions) {
                session.setTrainingStatus(WorkerTrainingSessionStatus.WAITING_AUTHORIZATION);
                workerTrainingSessionRepository.save(session);
        }
    }



    @Scheduled(cron = "*/5 * * * * ?")
    public void checkShiftBookedStatus() {

        List<Shift> newShiftNotElapsed = shiftRepository.findAllByStatus(ShiftStatus.BOOKED);

        for (Shift shift : newShiftNotElapsed
        ) {
            if(nonNull(shift.getStart())) {
                if (dateEqual(LocalDateTime.now(), shift.getStart())) {
                    if (LocalTime.now().isAfter(shift.getEnd().toLocalTime())) {
                        shift.setStatus(ShiftStatus.AWAITING_AUTHORIZATION);
                        shiftRepository.save(shift);
                    }
                } else if (dateBeforeorEqual(LocalDateTime.now(), shift.getStart())) {
                    shift.setStatus(ShiftStatus.AWAITING_AUTHORIZATION);
                    shiftRepository.save(shift);
                }
            }
        }
    }


    private void getDeputyWorkers(Agency deputyAgency) {
        if(deputyAgency.getDeputyUrl()==null || deputyAgency.getDeputyToken()==null){
//            log.error("Worker sync has failed. This business does not have a deputy url or token: {}", deputyAgency);
        }else {

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(deputyAgency.getDeputyToken());
            HttpEntity<String> entity = new HttpEntity<String>(headers);

            ResponseEntity<DeputyWorkersRespDto> deputyWorkers = restTemplate.exchange(deputyAgency.getDeputyUrl().concat("/api/management/v2/employees"), org.springframework.http.HttpMethod.GET, entity, DeputyWorkersRespDto.class, 100);

            Objects.requireNonNull(deputyWorkers.getBody()).getResult().forEach(d-> {
//                log.info("{}", d);
                Optional.ofNullable(d.getData().getContact()).map(Contact::getEmail1)
                        .ifPresentOrElse(
                                email->syncDeputyWorker(d, deputyAgency),
                                ()-> log.info("Worker is missing an email address:{}", d.getData().getFirstName() + " " + d.getData().getLastName())
                        );


            });
        }
    }

    private void getDeputyLocations(Agency deputyAgency) {
        if(deputyAgency.getDeputyUrl()==null || deputyAgency.getDeputyToken()==null){
//            log.error("Worker sync has failed. This business does not have a deputy url or token: {}", deputyAgency);
        }else {

            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(deputyAgency.getDeputyToken());
            HttpEntity<String> entity = new HttpEntity<String>(headers);

            ResponseEntity< LinkedHashMap[]> deputyLocationsListRaw = restTemplate.exchange(deputyAgency.getDeputyUrl().concat("/api/v1/resource/Company"), org.springframework.http.HttpMethod.GET, entity, LinkedHashMap[].class, 100);

            LinkedHashMap[] deputyLocationsListInter = deputyLocationsListRaw.getBody();

            ObjectMapper mapper = new ObjectMapper();

            List<DeputyLocationsRespDto> deputyLocations =  Arrays.stream( deputyLocationsListInter)
                    .map((ob)-> {



                        DeputyLocationsRespDto n = new DeputyLocationsRespDto();

                        n.setId((Integer) ob.get("Id"));
                        n.setCompanyName((String) ob.get("CompanyName"));
                        n.setCode((String) ob.get("Code"));
                        n.setActive((Boolean) ob.get("Active"));

                        return n;
                    })
                    .collect(Collectors.toList());

//            log.info("Deputy Locations have been imported as follows: \n"+deputyLocations.toString());

            deputyLocations.forEach(d-> {
                syncDeputyLocation(d, deputyAgency);
            });
        }
    }


    private void syncDeputyWorker(DeputyWorkerDataRespDto deputyWorkerDataRespDto, Agency agency) {
        DeputyWorkerRespDto dWorker = deputyWorkerDataRespDto.getData();
        Worker worker = workerService.getByDeputyId(dWorker.getId());

        Worker worker1 = workerService.findByEmail(dWorker.getContact().getEmail1());
        if(!isNull(worker) || !isNull(worker1)){
//            log.debug("Worker already exist in our db. Skipping deputy worker with deputy id: {}", dWorker.getId());
        }else{
            WorkerCreateDto newWorker =  new WorkerCreateDto();

            newWorker.setFirstname(dWorker.getFirstName());
            newWorker.setLastname(dWorker.getLastName());
            newWorker.setEmail(dWorker.getContact().getEmail1());
            newWorker.setUsername(dWorker.getContact().getEmail1());
            newWorker.setAgencyId(agency.getId());
            newWorker.setDob(LocalDate.now());
             newWorker.setDeputyId(dWorker.getId());
            newWorker.setPhoneNumber("");
            newWorker.setUsername(dWorker.getContact().getEmail1());
            newWorker.setNationality("BH");
            newWorker.setAssignmentCode(1L);
            newWorker.setGender(Gender.FEMALE);
            newWorker.setPhoneNumber(dWorker.getContact().getPhone1());
//            newWorker.setCreatedDate(dWorker.getCreatedAt().atZone(ZoneId.of("Europe/London")).toInstant());
            workerService.save(newWorker);
        }
    }
    private void syncDeputyLocation(DeputyLocationsRespDto dLocation, Agency agency) {

//        log.info(dLocation.toString());
        ShiftDirectorate directorate = directorateService.getByDeputyId(dLocation.getId().longValue());
        if(!isNull(directorate)){
//            log.debug("Directorate already exist in our db. Skipping deputy directorate with deputy id: {}", dLocation.getId());
        }else{
            ShiftDirectorateCreateDto newWorker =  new ShiftDirectorateCreateDto();
            newWorker.setName(dLocation.getCompanyName());
            newWorker.setShiftLocationId(1L);
//            newWorker.setPostCode(dLocation.getContact().getEmail1());
//            newWorker.setPhoneNumber(agency.getId());
            newWorker.setClientId(1L);
            newWorker.setDeputyId(dLocation.getId().longValue());
            directorateService.save(newWorker);
        }
    }

    private void syncDeputyShift(DeputyLocationsRespDto dLocation, Agency agency) {

        Worker worker = workerService.getOne(dLocation.getId().longValue());
        if(!isNull(worker)){
//            log.debug("Directorate already exist in our db. Skipping deputy directorate with deputy id: {}", dLocation.getId());
        }else{
            ShiftDirectorate newWorker =  new ShiftDirectorate();

        }
    }

    @Scheduled(cron = "0 0 0/7 * * ?") // Schedule every sunday at midnight
    public void complianceExpiry() {
        workerComplianceRepository.findAllByComplianceStatus(ComplianceStatus.APPROVED)
                .stream()
                .filter(compliance -> compliance.getComplianceExpiry().isBefore(LocalDate.now().plusDays(30)))
                .forEach(compliance -> {
                    NotificationCreateDto notificationCreateDto = new NotificationCreateDto();
                    notificationCreateDto.setTitle("Compliance Expiry");
                    notificationCreateDto.setBody("Your compliance document is about to expire");
                    notificationCreateDto.setWorkerId(compliance.getWorker().getId());

                    notificationService.addWorkerNotification(notificationCreateDto);

                    compliance.getWorker().getDevices().forEach(device -> {
                        CompletableFuture.runAsync(() -> {
                            try {
                                pushNotification.sendPushMessage("Compliance Expiry", "Your compliance document is about to expire", device.getFcmToken());
                            } catch (FirebaseMessagingException e) {
                                log.error(e.toString());
                                deviceService.delete(device);
                            }
                        });
                    });
                });
    }

    private boolean checkHours(int broadcastHours, Instant createdDate) {

        long hours = HOURS.between(LocalTime.now(), LocalTime.from(createdDate));
        return broadcastHours < (int) hours;

    }

    private boolean dateDiff(LocalDateTime now, LocalDateTime shiftDate) {
        return !shiftDate.isAfter(now);
    }

    private boolean timeDiff(LocalTime now, String shiftTime) {
        return !LocalTime.parse(shiftTime).isAfter(now);
    }

    private boolean timeDiff(String endTime, String shiftTime) {
        return !LocalTime.parse(shiftTime).isBefore(LocalTime.parse(endTime));
    }



    private boolean dateEqual(LocalDateTime now, LocalDateTime shiftDate) {
        return shiftDate.isEqual(now);
    }

    private boolean dateBeforeorEqual(LocalDateTime now, LocalDateTime shiftDate) {
        return now.isAfter(shiftDate);
    }

}
