package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.MemoryStats;
import com.cap10mycap10.worklinkservice.dto.admin.AdminStats;
import com.cap10mycap10.worklinkservice.enums.AssetStatus;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.permissions.dashboard.ViewShiftDashboard;
import com.cap10mycap10.worklinkservice.service.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class AdminDashboardController {


    @Autowired
    private VehicleRepository vehicleRepository;
    private final AgencyRepository agencyRepository;
    private final ClientRepository clientRepository;
    private final WorkerRepository workerRepository;
    private final ShiftRepository shiftRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    WorkerService workerService;

    @Autowired
    ShiftDirectorateService directorateService;

    @Autowired
    WorkerTrainingService workerTrainingService;
    @Autowired
    TrainingService trainingService;

    @Autowired
    ComplianceService complianceService;

    @Autowired
    NotificationService notificationService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    PushNotification pushNotification;

    @Autowired
    WorkerComplianceRepository workerComplianceRepository;

    @Autowired
    AgencyService agencyService;
    @Autowired
    TrainingRepository trainingRepository;
    @Autowired
    WorkerTrainingRepository workerTrainingRepository;

    @Autowired
    private TrainingSessionRepository trainingSessionRepository;
    @Autowired
    private WorkerTrainingSessionRepository workerTrainingSessionRepository;
    @Autowired
    private TransportService transportService;
    @Autowired
    private VehicleBookingService vehicleBookingService;
    @Autowired
    private PromotionService promotionService;

    public AdminDashboardController(AgencyRepository agencyRepository, ClientRepository clientRepository, WorkerRepository workerRepository, ShiftRepository shiftRepository) {
        this.agencyRepository = agencyRepository;
        this.clientRepository = clientRepository;
        this.workerRepository = workerRepository;
        this.shiftRepository = shiftRepository;
    }

//    @ViewShiftDashboard
    @GetMapping(value = "admin-stats")
    public ResponseEntity<AdminStats> findAllAgencyShiftsByStatus() {
        log.info("Request to get  admin stats ");
        AdminStats adminStats = new AdminStats(agencyRepository.findNumberOfAgents(),
                clientRepository.findNumberOfClients(),
                workerRepository.findNumberOfWorkers(),
                shiftRepository.findNumberOfShifts());

        List<AssetStatus> statuses = Arrays.asList(AssetStatus.AWAITING, AssetStatus.AVAILABLE);
        adminStats.setVehicles(vehicleRepository.countByStatusIn(statuses));
        adminStats.setVehiclesPending(vehicleRepository.countByStatusIn(List.of(AssetStatus.AWAITING, AssetStatus.EDITED)));

        return ResponseEntity.ok(adminStats);
    }


    @GetMapping("memory-status")
    public MemoryStats getMemoryStatistics() {
        MemoryStats stats = new MemoryStats();
        stats.setHeapSize(String.valueOf(Runtime.getRuntime().totalMemory()));
        stats.setHeapMaxSize(String.valueOf(Runtime.getRuntime().maxMemory()));
        stats.setHeapFreeSize(String.valueOf(Runtime.getRuntime().freeMemory()));
        return stats;
    }


    @GetMapping("run-schedules")
    public MemoryStats runScheduler() {
        try {
            transportService.expireUnbookedJobs();
        } catch (Exception e) {
            log.error("{}", e);
        }

        try {
            vehicleBookingService.sendRentalReminders();
        } catch (Exception e) {
            log.error("{}", e);
        }

        try {
            vehicleBookingService.checkForUnpaidBookings();
        } catch (Exception e) {
            log.error("{}", e);
        }



        try {
            promotionService.modifyPendingPromotions();
        } catch (Exception e) {
            log.error("{}", e);
        }

        try {
            promotionService.addVehiclesToPromotions();
        } catch (Exception e) {
            log.error("Error adding vehicles to promotions: {}", e);
        }


        return null;
    }
}
