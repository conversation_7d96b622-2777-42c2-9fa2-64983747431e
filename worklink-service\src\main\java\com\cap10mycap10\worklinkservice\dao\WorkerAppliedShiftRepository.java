package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.WorkerAppliedShift;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface WorkerAppliedShiftRepository extends JpaRepository<WorkerAppliedShift, Long> {

    @Query(value = "select id," +
            "shift_id                 as shiftId,\n" +
            "applied_date         as appliedDate,\n" +
            "worker_id         as workerId,\n" +
            "shift_status         as status,\n" +
            "agency_id         as agencyId\n" +

            "from worker_applied_shift\n" +
            "where shift_id = ?1\n" +
            "   and worker_id = ?2\n "
            , nativeQuery = true)
    List<WorkerAppliedShift> findWorkerAppliedShift(Long shiftId, Long workerId);

    @Query(value = "select id," +
            "shift_id                 as shiftId,\n" +
            "applied_date         as appliedDate,\n" +
            "worker_id         as workerId,\n" +
            "shift_status         as status,\n" +
            "agency_id         as agencyId\n" +

            "from worker_applied_shift\n" +
            "where worker_id = ?1\n "
            , nativeQuery = true)
    List<WorkerAppliedShift> findWorkerAppliedShifts(Long workerId);

}
