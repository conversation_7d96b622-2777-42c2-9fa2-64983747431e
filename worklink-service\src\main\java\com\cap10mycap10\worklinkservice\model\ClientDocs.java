package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.enums.ClientDocType;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import jakarta.persistence.*;
import java.time.LocalDate;


@Entity
@Data
public class ClientDocs extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String url;
    @Enumerated(EnumType.STRING)
    private ClientDocType name;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="dd-MM-yyyy", timezone="Z")
    private LocalDate expiryDate;
    private Status status = Status.WAITING;
    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Client client;

}
