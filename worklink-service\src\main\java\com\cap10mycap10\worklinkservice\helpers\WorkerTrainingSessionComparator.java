package com.cap10mycap10.worklinkservice.helpers;

import com.cap10mycap10.worklinkservice.model.WorkerTrainingSession;
import org.springframework.stereotype.Service;

import java.util.Comparator;

@Service
public class WorkerTrainingSessionComparator implements Comparator<WorkerTrainingSession> {

    @Override
    public int compare(WorkerTrainingSession one, WorkerTrainingSession two) {
        return one.getTrainingSession().getStartDateTime().compareTo(two.getTrainingSession().getStartDateTime());
    }
}
