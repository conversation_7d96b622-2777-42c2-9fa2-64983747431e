package com.cap10mycap10.worklinkservice.helpers;// PaynowHelper.java
import com.cap10mycap10.worklinkservice.model.Invoice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import zw.co.paynow.core.Paynow;
import zw.co.paynow.core.Payment;
import zw.co.paynow.responses.WebInitResponse;
import java.math.BigDecimal;

import static java.util.Objects.nonNull;


@Service
@Slf4j
public class PaynowHelper {
    @Value("${env.paynowIntegrationId}")
    private String paynowIntegrationId;
    @Value("${env.paynowIntegrationKey}")
    private String paynowIntegrationKey;
    @Value("${env.paynowReturnUrl}")
    private String paynowReturnUrl;
    @Value("${env.paynowResultUrl}")
    private String paynowResultUrl;

    public WebInitResponse paynowBookingPay(Invoice invoice, boolean fullPayment) {
        Paynow paynow = new Paynow(paynowIntegrationId, paynowIntegrationKey);

        Payment payment = paynow.createPayment("Invoice#"+invoice.getId());
        paynow.setResultUrl(paynowResultUrl+"/"+invoice.getId());
        paynow.setReturnUrl(paynowReturnUrl+"?invoiceId="+invoice.getId());
        if(nonNull(invoice.getVehicleBooking().getId())) paynow.setReturnUrl(paynowReturnUrl+"?invoiceId="+invoice.getId()+"&bookingId="+invoice.getVehicleBooking().getId());

        if(fullPayment) invoice.getInvoiceItems().forEach(i-> payment.add(i.getDescription(), i.getTotal()));
        else {
            BigDecimal val = invoice.getSubTotalAmount();
            BigDecimal multiplicative = BigDecimal.valueOf(0.07);
            payment.add("Reservation fee", val.multiply(multiplicative));
        }

        payment.add("VAT", invoice.getVatAmount());
        payment.add("Discount", invoice.getDiscount().multiply(BigDecimal.valueOf(-1)));

        return paynow.send(payment);
    }

    public Paynow getPaynowInstance() {
        return new Paynow(paynowIntegrationId, paynowIntegrationKey);
    }

}