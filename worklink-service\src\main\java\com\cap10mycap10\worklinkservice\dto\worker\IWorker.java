//package com.cap10mycap10.worklinkservice.dto.worker;
//
//import com.cap10mycap10.worklinkservice.enums.Gender;
//import com.cap10mycap10.worklinkservice.enums.Status;
//import com.cap10mycap10.worklinkservice.enums.WorkerStatus;
//import com.cap10mycap10.worklinkservice.model.Worker;
//import com.fasterxml.jackson.annotation.JsonIgnore;
//
//public interface IWorker {
//
//    Long getId();
//
//    String getFirstname();
//
//    String getLastname();
//
//    Gender getGender();
//
//    String getPhoneNumber();
//
//    String getAddress();
//
//    String getPostcode();
//
//    String getEmail();
//
//    String getAssignmentCode();
//
//    String getAssignmentName();
//
//    WorkerStatus getAgencyStatus();
//
//    String createdBy();
//}
