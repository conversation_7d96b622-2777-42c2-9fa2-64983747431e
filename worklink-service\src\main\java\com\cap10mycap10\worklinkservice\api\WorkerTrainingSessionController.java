package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionAuthorizeDto;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionResultsDto;
import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.cap10mycap10.worklinkservice.model.WorkerTrainingSession;
import com.cap10mycap10.worklinkservice.service.TrainingSessionService;
import com.cap10mycap10.worklinkservice.service.WorkerTrainingSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class WorkerTrainingSessionController {

    @Autowired
    private TrainingSessionService trainingSessionService;

    @Autowired
    private WorkerTrainingSessionService workerTrainingSessionService;
    public static final String ROLE_AGENCY = "ROLE_AGENCY_ADMIN";


    @GetMapping(value = "training-booking/{id}")
    public ResponseEntity<WorkerTrainingSessionResultsDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get worker training booking by with id : {}", id);
        return ResponseEntity.ok(trainingSessionService.findWorkerTrainingBookingById(id));
    }

    @PutMapping(value = "training-booking/authorize", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<WorkerTrainingSessionResultsDto> authorizeComplete(@RequestBody WorkerTrainingSessionAuthorizeDto workerTrainingSessionAuthorizeDto){
        log.info("Request to authorize workerTrainingSession for: {} ", workerTrainingSessionAuthorizeDto);
        return ResponseEntity.ok(trainingSessionService.authorizeWorker(workerTrainingSessionAuthorizeDto));
    }

    @GetMapping(value = "training-booking/training-session/{trainingSessionId}")
    public ResponseEntity<List<WorkerTrainingSessionResultsDto>> findByTrainingSession(@PathVariable("trainingSessionId") Long trainingSessionId){
        log.info("Get all workers in trainingSession with id trainingStatus: {}", trainingSessionId);
        return ResponseEntity.ok(trainingSessionService.findWorkersByTrainingSessionId(trainingSessionId));
    }


    @GetMapping(value = "training-booking/feedback")
    public ResponseEntity<Optional<WorkerTrainingSession>> findPendingFeedback(){
        log.info("REquest to get worker pending training feedback ");
        return ResponseEntity.ok(workerTrainingSessionService.checkPending());
    }



    @PutMapping(value = "training-booking/book/{agencyId}/{trainingSessionId}/{workerIds}")
    public ResponseEntity<Object> book(@PathVariable("agencyId") Long agencyId,
                                         @PathVariable("trainingSessionId") Long trainingSessionId,
                                         @PathVariable("workerIds")String workerIds) throws Exception {
        log.info("Request to add workers to a training session id : {} agencyId: {}, and workerIds: {}",trainingSessionId, agencyId, workerIds);
        Set<Long> workerIdsSet = Arrays.stream(workerIds.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toSet());

        trainingSessionService.agencyAddWorkersToTrainingSession(agencyId, trainingSessionId, workerIdsSet);
        return ResponseEntity.ok().build();
    }

    @PutMapping(value = "training-booking/approval/{bookingId}/{approveBooking}")
    public ResponseEntity<Object> applicationApproval(@PathVariable("bookingId") Long bookingId,
                                                               @PathVariable("approveBooking") Boolean approveBooking
    ){

        log.info("Request to approve workerBooking with id : {} and bookingStatus of {}", bookingId, approveBooking);
        trainingSessionService.approveOrRejectWorkerBooking(bookingId, approveBooking, true);
        return ResponseEntity.ok().build();
    }
    @PutMapping(value = "training-booking/show-certificate/{bookingId}/{show}")
    public ResponseEntity<Object> showCertificate(@PathVariable("bookingId") Long bookingId,
                                                               @PathVariable("show") Boolean show
    ){
        log.info("Request to approve workerBooking with id : {} and bookingStatus of {}", bookingId, show);
        workerTrainingSessionService.showCertificate(bookingId, show);
        return ResponseEntity.ok().build();
    }



    @PutMapping(value = "training-booking/apply/{agencyId}/{trainingSessionId}/{workerId}")
    public ResponseEntity<Object> apply(@PathVariable("agencyId") Long agencyId,
                                                         @PathVariable("trainingSessionId") Long trainingSessionId,
                                                         @PathVariable("workerId") Long workerId) throws Exception {

        log.info("Request to add worker with id: {} to a training sessionsId : {} made by agencyId: {}", workerId, trainingSessionId, agencyId);
        trainingSessionService.workerAddSelfToTrainingSession(agencyId, trainingSessionId, workerId);
        return ResponseEntity.ok().build();
    }


    @GetMapping(value = "training-bookings/agency/{agencyId}/{status}/{page}/{size}")
    public ResponseEntity<Page<WorkerTrainingSessionResultsDto>> findForAgency(@PathVariable("page") int page,
                                                                                                               @PathVariable("size") int size,
                                                                                                               @PathVariable("agencyId") Long agencyId,
                                                                                                               @PathVariable("status") WorkerTrainingSessionStatus status,
                                                                                                               @RequestParam(required = false) Long trainingId,
                                                                                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to view paged  training sessions by status : {}, {}, {}, {}", page, size, agencyId, status);
        return ResponseEntity.ok(workerTrainingSessionService.findForAgency(agencyId, status, trainingId,  startDate, endDate, PageRequest.of(page, size)));
    }

    @GetMapping(value = "training-bookings/trainer/{agencyId}/{status}/{page}/{size}")
    public ResponseEntity<Page<WorkerTrainingSessionResultsDto>> findForTrainer(@PathVariable("page") int page,
                                                                                                               @PathVariable("size") int size,
                                                                                                               @PathVariable("agencyId") Long agencyId,
                                                                                                               @PathVariable("status") WorkerTrainingSessionStatus status,
                                                                                                               @RequestParam(required = false) Long trainingId,
                                                                                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
                                                                                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        log.info("Request to view paged  training sessions by status : {}, {}, {}, {}", page, size, agencyId, status);
        return ResponseEntity.ok(workerTrainingSessionService.findForTrainer(agencyId, status, trainingId,  startDate, endDate, PageRequest.of(page, size)));
    }


    @DeleteMapping(value = "training-booking/{id}")
    public ResponseEntity<Object> cancel(@PathVariable("id") Long id) {
        log.info("Request to cancel training session with trainingSessionId: {}",id);
        trainingSessionService.cancelTrainingBooking(id);
        return ResponseEntity.noContent().build();
    }


    @PostMapping(value = "training-booking/auth-reminder/{id}")
    public ResponseEntity<String> authorizationReminder(@PathVariable("id") Long id){
        log.info("Request to send an authorization reminder for training booking of id: {}", id);
        return ResponseEntity.ok(workerTrainingSessionService.authorizationReminder(id));
    }
}
