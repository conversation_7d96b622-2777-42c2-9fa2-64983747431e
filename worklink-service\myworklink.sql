-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: mariadb
-- Generation Time: May 26, 2022 at 10:09 PM
-- Server version: 10.7.3-MariaDB-1:10.7.3+maria~focal
-- PHP Version: 8.0.19

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `myworklink2`
--

-- --------------------------------------------------------

--
-- Table structure for table `agency`
--

CREATE TABLE `agency` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `county` varchar(255) DEFAULT NULL,
  `first_line` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `second_line` varchar(255) DEFAULT NULL,
  `town` varchar(255) DEFAULT NULL,
  `billing_email` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `telephone` varchar(255) DEFAULT NULL,
  `service_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `agency`
--

INSERT INTO `agency` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `county`, `first_line`, `postcode`, `second_line`, `town`, `billing_email`, `email`, `logo`, `name`, `status`, `telephone`, `service_id`) VALUES
(1, 'chrissd', '2022-05-10 10:05:51.446885', NULL, '2022-05-11 22:10:07.678565', 16, NULL, 'Agency', 'WW22LL', NULL, 'Worklink', '<EMAIL>', '<EMAIL>', 'string', 'Agency', 'ACTIVE', '02020202', 2),
(2, 'chrissd', '2022-05-10 17:46:18.491407', NULL, '2022-05-12 17:39:46.058512', 9, NULL, 'RosePetal Rpad', 'WV34FF', NULL, 'Wolves', '<EMAIL>', '<EMAIL>', 'string', 'Rosepetal', 'ACTIVE', '09637383', 2),
(3, 'chrissd', '2022-05-11 09:26:54.573950', NULL, '2022-05-11 09:26:54.573950', 0, NULL, '2670 Mainway meadows, Waterfalls', '00263', NULL, 'Harare', '<EMAIL>', '<EMAIL>', 'string', 'Tinashe', 'ACTIVE', '+263774483751', 1),
(4, 'chrissd', '2022-05-23 07:44:19.113919', NULL, '2022-05-23 08:09:31.424226', 2, NULL, '14 Clean Street', 'IP15QD', NULL, 'Ipswich', '<EMAIL>', '<EMAIL>', 'string', 'CleanAgency', 'ACTIVE', '07611718181', 3);

-- --------------------------------------------------------

--
-- Table structure for table `agency_bill`
--

CREATE TABLE `agency_bill` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `charge_rate` decimal(19,2) DEFAULT NULL,
  `discount_charge` decimal(19,2) DEFAULT NULL,
  `due_date` datetime(6) DEFAULT NULL,
  `issue_date` datetime(6) DEFAULT NULL,
  `notes` varchar(255) DEFAULT NULL,
  `paid` bit(1) DEFAULT NULL,
  `paid_date` datetime(6) DEFAULT NULL,
  `payment_ref` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `sub_total` decimal(19,2) DEFAULT NULL,
  `total_charge` decimal(19,2) DEFAULT NULL,
  `total_due` decimal(19,2) DEFAULT NULL,
  `total_units` varchar(255) DEFAULT NULL,
  `vat_rate` decimal(19,2) DEFAULT NULL,
  `agency_id` bigint(20) DEFAULT NULL,
  `client_id` bigint(20) DEFAULT NULL,
  `shift_id` bigint(20) DEFAULT NULL,
  `worker_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `agency_client`
--

CREATE TABLE `agency_client` (
  `agency_id` bigint(20) NOT NULL,
  `client_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `agency_client`
--

INSERT INTO `agency_client` (`agency_id`, `client_id`) VALUES
(1, 17),
(1, 20),
(1, 21),
(1, 23),
(2, 20),
(2, 21),
(4, 24);

-- --------------------------------------------------------

--
-- Table structure for table `agency_worker`
--

CREATE TABLE `agency_worker` (
  `agency_id` bigint(20) NOT NULL,
  `worker_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `agency_worker`
--

INSERT INTO `agency_worker` (`agency_id`, `worker_id`) VALUES
(1, 37),
(1, 38),
(1, 39),
(1, 40),
(1, 42),
(1, 44),
(2, 37),
(2, 39),
(2, 40),
(2, 41),
(2, 42),
(2, 43),
(2, 44),
(4, 45);

-- --------------------------------------------------------

--
-- Table structure for table `assignment_code`
--

CREATE TABLE `assignment_code` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `code` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `services_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `assignment_code`
--

INSERT INTO `assignment_code` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `code`, `name`, `services_id`) VALUES
(1, 'chrissd', '2022-05-10 10:10:57.608125', NULL, '2022-05-10 10:10:57.608125', 0, 'HSW', 'Health Support Worker', 2),
(2, 'chrissd', '2022-05-10 10:13:30.274077', NULL, '2022-05-10 10:13:30.274077', 0, 'T1', 'Test', 1),
(3, 'chrissd', '2022-05-10 13:54:26.325894', NULL, '2022-05-10 13:54:26.325894', 0, 'RGN', 'Registered Nurse', 2),
(4, 'chrissd', '2022-05-23 07:42:41.952571', NULL, '2022-05-23 07:43:01.319073', 2, 'CL', 'Cleaner', 3),
(5, 'chrissd', '2022-05-23 08:33:13.372474', NULL, '2022-05-23 08:33:13.372474', 0, 'FG', 'Fumigator', 3);

-- --------------------------------------------------------

--
-- Table structure for table `assignment_rate`
--

CREATE TABLE `assignment_rate` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `client_rate` decimal(19,2) DEFAULT NULL,
  `day_of_the_week` varchar(255) DEFAULT NULL,
  `end_time` varchar(255) DEFAULT NULL,
  `paye_rate` decimal(19,2) DEFAULT NULL,
  `private_rate` decimal(19,2) DEFAULT NULL,
  `start_time` varchar(255) DEFAULT NULL,
  `umbrella_rate` decimal(19,2) DEFAULT NULL,
  `agent_id` bigint(20) DEFAULT NULL,
  `assignment_code_id` bigint(20) DEFAULT NULL,
  `client_id` bigint(20) DEFAULT NULL,
  `shift_directorate_id` bigint(20) DEFAULT NULL,
  `shift_type_id` bigint(20) DEFAULT NULL,
  `shift_location_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `assignment_rate`
--

INSERT INTO `assignment_rate` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `client_rate`, `day_of_the_week`, `end_time`, `paye_rate`, `private_rate`, `start_time`, `umbrella_rate`, `agent_id`, `assignment_code_id`, `client_id`, `shift_directorate_id`, `shift_type_id`, `shift_location_id`) VALUES
(143, 'system', '2022-05-11 04:21:25.890252', NULL, '2022-05-11 04:21:56.688695', 1, '70.00', 'SUNDAY', '18:00', '10.00', '30.00', '08:00', '40.00', 1, 1, 21, 59, 1, 49),
(144, 'system', '2022-05-11 05:21:44.399284', NULL, '2022-05-11 05:21:44.399284', 0, '30.00', 'SUNDAY', '16:00', '20.00', '24.00', '08:00', '22.00', 1, 1, 21, 59, 1, 49),
(145, 'system', '2022-05-11 08:18:44.701756', NULL, '2022-05-11 08:19:23.644297', 2, '30.00', 'SUNDAY', '20:00', '15.00', '25.00', '08:00', '20.00', 2, 1, 21, 59, 1, 49),
(146, 'system', '2022-05-11 08:21:34.098936', NULL, '2022-05-11 08:21:34.098936', 0, '25.00', 'MONDAY', '20:00', '10.00', '20.00', '08:00', '15.00', 2, 1, 21, 59, 1, 49),
(147, 'system', '2022-05-11 08:21:41.407403', NULL, '2022-05-11 08:21:41.407403', 0, '25.00', 'TUESDAY', '20:00', '10.00', '20.00', '08:00', '15.00', 2, 1, 21, 59, 1, 49),
(148, 'system', '2022-05-11 08:21:48.558024', NULL, '2022-05-11 08:21:48.558024', 0, '25.00', 'WEDNESDAY', '20:00', '10.00', '20.00', '08:00', '15.00', 2, 1, 21, 59, 1, 49),
(149, 'system', '2022-05-11 08:21:52.252507', NULL, '2022-05-11 08:21:52.252507', 0, '25.00', 'THURSDAY', '20:00', '10.00', '20.00', '08:00', '15.00', 2, 1, 21, 59, 1, 49),
(150, 'system', '2022-05-11 08:21:55.648658', NULL, '2022-05-11 08:21:55.648658', 0, '25.00', 'FRIDAY', '20:00', '10.00', '20.00', '08:00', '15.00', 2, 1, 21, 59, 1, 49),
(151, 'system', '2022-05-11 08:22:49.534641', NULL, '2022-05-11 08:22:49.534641', 0, '28.00', 'SATURDAY', '20:00', '14.00', '24.00', '08:00', '20.00', 2, 1, 21, 59, 1, 49),
(152, 'system', '2022-05-11 08:23:29.296472', NULL, '2022-05-11 08:23:29.296472', 0, '28.00', 'SUNDAY', '20:00', '14.00', '24.00', '08:00', '20.00', 2, 1, 21, 60, 1, 49),
(153, 'system', '2022-05-11 08:24:05.642763', NULL, '2022-05-11 08:24:05.642763', 0, '24.00', 'MONDAY', '20:00', '18.00', '22.00', '08:00', '22.00', 2, 1, 21, 60, 1, 49),
(154, 'system', '2022-05-11 08:24:10.124501', NULL, '2022-05-11 08:24:10.124501', 0, '24.00', 'TUESDAY', '20:00', '18.00', '22.00', '08:00', '22.00', 2, 1, 21, 60, 1, 49),
(155, 'system', '2022-05-11 08:24:13.041842', NULL, '2022-05-11 08:24:13.041842', 0, '24.00', 'WEDNESDAY', '20:00', '18.00', '22.00', '08:00', '22.00', 2, 1, 21, 60, 1, 49),
(156, 'system', '2022-05-11 08:24:15.649177', NULL, '2022-05-11 08:24:15.649177', 0, '24.00', 'THURSDAY', '20:00', '18.00', '22.00', '08:00', '22.00', 2, 1, 21, 60, 1, 49),
(157, 'system', '2022-05-11 08:24:18.463968', NULL, '2022-05-11 08:24:18.463968', 0, '24.00', 'FRIDAY', '20:00', '18.00', '22.00', '08:00', '22.00', 2, 1, 21, 60, 1, 49),
(158, 'system', '2022-05-11 08:24:45.650667', NULL, '2022-05-11 08:24:45.650667', 0, '26.00', 'SATURDAY', '20:00', '16.00', '24.00', '08:00', '20.00', 2, 1, 21, 60, 1, 49),
(161, 'system', '2022-05-11 08:33:27.611012', NULL, '2022-05-11 08:33:27.611012', 0, '35.00', 'SUNDAY', '20:00', '20.00', '30.00', '08:00', '25.00', 2, 1, 21, 61, 1, 50),
(162, 'system', '2022-05-11 08:33:44.027433', NULL, '2022-05-11 08:33:44.027433', 0, '30.00', 'MONDAY', '20:00', '15.00', '25.00', '08:00', '20.00', 2, 1, 21, 61, 1, 50),
(163, 'system', '2022-05-11 08:33:47.647544', NULL, '2022-05-11 08:33:47.647544', 0, '30.00', 'TUESDAY', '20:00', '15.00', '25.00', '08:00', '20.00', 2, 1, 21, 61, 1, 50),
(164, 'system', '2022-05-11 08:33:51.148001', NULL, '2022-05-11 08:33:51.148001', 0, '30.00', 'WEDNESDAY', '20:00', '15.00', '25.00', '08:00', '20.00', 2, 1, 21, 61, 1, 50),
(165, 'system', '2022-05-11 08:33:53.898678', NULL, '2022-05-11 08:33:53.898678', 0, '30.00', 'THURSDAY', '20:00', '15.00', '25.00', '08:00', '20.00', 2, 1, 21, 61, 1, 50),
(166, 'system', '2022-05-11 08:33:56.066047', NULL, '2022-05-11 08:33:56.066047', 0, '30.00', 'FRIDAY', '20:00', '15.00', '25.00', '08:00', '20.00', 2, 1, 21, 61, 1, 50),
(167, 'system', '2022-05-11 08:34:10.546592', NULL, '2022-05-11 08:34:10.546592', 0, '35.00', 'SATURDAY', '20:00', '20.00', '30.00', '08:00', '25.00', 2, 1, 21, 61, 1, 50),
(168, 'system', '2022-05-11 08:34:45.275146', NULL, '2022-05-11 08:34:45.275146', 0, '40.00', 'SUNDAY', '20:00', '25.00', '35.00', '08:00', '30.00', 2, 1, 21, 62, 1, 50),
(169, 'system', '2022-05-11 08:35:04.343953', NULL, '2022-05-11 08:35:04.343953', 0, '35.00', 'MONDAY', '20:00', '20.00', '30.00', '08:00', '25.00', 2, 1, 21, 62, 1, 50),
(170, 'system', '2022-05-11 08:35:06.564318', NULL, '2022-05-11 08:35:06.564318', 0, '35.00', 'TUESDAY', '20:00', '20.00', '30.00', '08:00', '25.00', 2, 1, 21, 62, 1, 50),
(171, 'system', '2022-05-11 08:35:09.119319', NULL, '2022-05-11 08:35:09.119319', 0, '35.00', 'WEDNESDAY', '20:00', '20.00', '30.00', '08:00', '25.00', 2, 1, 21, 62, 1, 50),
(172, 'system', '2022-05-11 08:35:11.681106', NULL, '2022-05-11 08:35:11.681106', 0, '35.00', 'THURSDAY', '20:00', '20.00', '30.00', '08:00', '25.00', 2, 1, 21, 62, 1, 50),
(173, 'system', '2022-05-11 08:35:13.680963', NULL, '2022-05-11 08:35:13.680963', 0, '35.00', 'FRIDAY', '20:00', '20.00', '30.00', '08:00', '25.00', 2, 1, 21, 62, 1, 50),
(174, 'system', '2022-05-11 08:35:16.816221', NULL, '2022-05-11 08:35:16.816221', 0, '35.00', 'SATURDAY', '20:00', '20.00', '30.00', '08:00', '25.00', 2, 1, 21, 62, 1, 50),
(182, 'system', '2022-05-11 08:40:34.599021', NULL, '2022-05-11 08:40:34.599021', 0, '35.00', 'SUNDAY', '08:00', '20.00', '30.00', '20:00', '25.00', 2, 1, 21, 59, 4, 49),
(183, 'system', '2022-05-11 08:41:44.904158', NULL, '2022-05-11 08:41:44.904158', 0, '35.00', 'MONDAY', '08:00', '20.00', '30.00', '20:00', '25.00', 2, 1, 21, 59, 4, 49),
(184, 'system', '2022-05-11 08:41:48.534097', NULL, '2022-05-11 08:41:48.534097', 0, '35.00', 'TUESDAY', '08:00', '20.00', '30.00', '20:00', '25.00', 2, 1, 21, 59, 4, 49),
(185, 'system', '2022-05-11 08:41:51.470157', NULL, '2022-05-11 08:41:51.470157', 0, '35.00', 'WEDNESDAY', '08:00', '20.00', '30.00', '20:00', '25.00', 2, 1, 21, 59, 4, 49),
(186, 'system', '2022-05-11 08:41:54.350387', NULL, '2022-05-11 08:41:54.350387', 0, '35.00', 'THURSDAY', '08:00', '20.00', '30.00', '20:00', '25.00', 2, 1, 21, 59, 4, 49),
(187, 'system', '2022-05-11 08:41:58.042379', NULL, '2022-05-11 08:41:58.042379', 0, '35.00', 'FRIDAY', '08:00', '20.00', '30.00', '20:00', '25.00', 2, 1, 21, 59, 4, 49),
(188, 'system', '2022-05-11 08:42:00.561397', NULL, '2022-05-11 08:42:00.561397', 0, '35.00', 'SATURDAY', '08:00', '20.00', '30.00', '20:00', '25.00', 2, 1, 21, 59, 4, 49),
(189, 'system', '2022-05-11 08:42:28.377945', NULL, '2022-05-11 08:42:28.377945', 0, '40.00', 'SUNDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 60, 4, 49),
(190, 'system', '2022-05-11 08:42:32.971488', NULL, '2022-05-11 08:42:32.971488', 0, '40.00', 'MONDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 60, 4, 49),
(191, 'system', '2022-05-11 08:42:36.712665', NULL, '2022-05-11 08:42:36.712665', 0, '40.00', 'TUESDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 60, 4, 49),
(192, 'system', '2022-05-11 08:42:42.114647', NULL, '2022-05-11 08:42:42.114647', 0, '40.00', 'WEDNESDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 60, 4, 49),
(193, 'system', '2022-05-11 08:42:46.913767', NULL, '2022-05-11 08:42:46.913767', 0, '40.00', 'THURSDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 60, 4, 49),
(194, 'system', '2022-05-11 08:42:51.449645', NULL, '2022-05-11 08:42:51.449645', 0, '40.00', 'FRIDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 60, 4, 49),
(195, 'system', '2022-05-11 08:42:56.598357', NULL, '2022-05-11 08:42:56.598357', 0, '40.00', 'SATURDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 60, 4, 49),
(196, 'system', '2022-05-11 08:45:11.934462', NULL, '2022-05-11 08:45:11.934462', 0, '40.00', 'SUNDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 61, 4, 50),
(197, 'system', '2022-05-11 08:45:16.478106', NULL, '2022-05-11 08:45:16.478106', 0, '40.00', 'MONDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 61, 4, 50),
(198, 'system', '2022-05-11 08:45:19.483321', NULL, '2022-05-11 08:45:19.483321', 0, '40.00', 'TUESDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 61, 4, 50),
(199, 'system', '2022-05-11 08:45:21.759413', NULL, '2022-05-11 08:45:21.759413', 0, '40.00', 'WEDNESDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 61, 4, 50),
(200, 'system', '2022-05-11 08:45:24.486921', NULL, '2022-05-11 08:45:24.486921', 0, '40.00', 'THURSDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 61, 4, 50),
(201, 'system', '2022-05-11 08:45:26.788987', NULL, '2022-05-11 08:45:26.788987', 0, '40.00', 'FRIDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 61, 4, 50),
(202, 'system', '2022-05-11 08:45:28.819620', NULL, '2022-05-11 08:45:28.819620', 0, '40.00', 'SATURDAY', '08:00', '25.00', '35.00', '20:00', '30.00', 2, 1, 21, 61, 4, 50),
(203, 'system', '2022-05-11 08:46:01.087738', NULL, '2022-05-11 08:46:01.087738', 0, '45.00', 'SUNDAY', '08:00', '30.00', '40.00', '20:00', '35.00', 2, 1, 21, 62, 4, 50),
(204, 'system', '2022-05-11 08:46:04.696069', NULL, '2022-05-11 08:46:04.696069', 0, '45.00', 'MONDAY', '08:00', '30.00', '40.00', '20:00', '35.00', 2, 1, 21, 62, 4, 50),
(205, 'system', '2022-05-11 08:46:08.128583', NULL, '2022-05-11 08:46:08.128583', 0, '45.00', 'TUESDAY', '08:00', '30.00', '40.00', '20:00', '35.00', 2, 1, 21, 62, 4, 50),
(206, 'system', '2022-05-11 08:46:11.727333', NULL, '2022-05-11 08:46:11.727333', 0, '45.00', 'WEDNESDAY', '08:00', '30.00', '40.00', '20:00', '35.00', 2, 1, 21, 62, 4, 50),
(207, 'system', '2022-05-11 08:46:15.463329', NULL, '2022-05-11 08:46:15.463329', 0, '45.00', 'THURSDAY', '08:00', '30.00', '40.00', '20:00', '35.00', 2, 1, 21, 62, 4, 50),
(208, 'system', '2022-05-11 08:46:19.068958', NULL, '2022-05-11 08:46:19.068958', 0, '45.00', 'FRIDAY', '08:00', '30.00', '40.00', '20:00', '35.00', 2, 1, 21, 62, 4, 50),
(209, 'system', '2022-05-11 08:46:22.190006', NULL, '2022-05-11 08:46:22.190006', 0, '45.00', 'SATURDAY', '08:00', '30.00', '40.00', '20:00', '35.00', 2, 1, 21, 62, 4, 50),
(210, 'system', '2022-05-11 08:50:30.004126', NULL, '2022-05-11 08:50:30.004126', 0, '55.00', 'SUNDAY', '20:00', '40.00', '50.00', '08:00', '45.00', 2, 3, 21, 59, 1, 49),
(211, 'system', '2022-05-11 08:50:47.853374', NULL, '2022-05-11 08:50:47.853374', 0, '50.00', 'MONDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 59, 1, 49),
(212, 'system', '2022-05-11 08:50:51.868675', NULL, '2022-05-11 08:50:51.868675', 0, '50.00', 'TUESDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 59, 1, 49),
(213, 'system', '2022-05-11 08:50:55.489313', NULL, '2022-05-11 08:50:55.489313', 0, '50.00', 'WEDNESDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 59, 1, 49),
(214, 'system', '2022-05-11 08:50:58.220055', NULL, '2022-05-11 08:50:58.220055', 0, '50.00', 'THURSDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 59, 1, 49),
(215, 'system', '2022-05-11 08:51:02.450097', NULL, '2022-05-11 08:51:02.450097', 0, '50.00', 'FRIDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 59, 1, 49),
(216, 'system', '2022-05-11 08:51:18.606642', NULL, '2022-05-11 08:51:18.606642', 0, '52.00', 'SATURDAY', '20:00', '38.00', '47.00', '08:00', '45.00', 2, 3, 21, 59, 1, 49),
(217, 'system', '2022-05-11 08:51:37.161407', NULL, '2022-05-11 08:51:37.161407', 0, '52.00', 'SATURDAY', '20:00', '38.00', '47.00', '08:00', '45.00', 2, 3, 21, 60, 1, 49),
(218, 'system', '2022-05-11 08:51:46.190729', NULL, '2022-05-11 08:51:46.190729', 0, '52.00', 'SUNDAY', '20:00', '38.00', '47.00', '08:00', '45.00', 2, 3, 21, 60, 1, 49),
(219, 'system', '2022-05-11 08:52:02.821160', NULL, '2022-05-11 08:52:02.821160', 0, '50.00', 'MONDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 60, 1, 49),
(220, 'system', '2022-05-11 08:52:07.130921', NULL, '2022-05-11 08:52:07.130921', 0, '50.00', 'TUESDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 60, 1, 49),
(221, 'system', '2022-05-11 08:52:10.270511', NULL, '2022-05-11 08:52:10.270511', 0, '50.00', 'WEDNESDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 60, 1, 49),
(222, 'system', '2022-05-11 08:52:14.297495', NULL, '2022-05-11 08:52:14.297495', 0, '50.00', 'THURSDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 60, 1, 49),
(223, 'system', '2022-05-11 08:52:17.011832', NULL, '2022-05-11 08:52:17.011832', 0, '50.00', 'FRIDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 60, 1, 49),
(224, 'system', '2022-05-11 08:53:48.710414', NULL, '2022-05-11 08:53:48.710414', 0, '50.00', 'SUNDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 61, 1, 50),
(225, 'system', '2022-05-11 08:53:52.713408', NULL, '2022-05-11 08:53:52.713408', 0, '50.00', 'MONDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 61, 1, 50),
(226, 'system', '2022-05-11 08:53:56.030034', NULL, '2022-05-11 08:53:56.030034', 0, '50.00', 'TUESDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 61, 1, 50),
(227, 'system', '2022-05-11 08:53:59.143302', NULL, '2022-05-11 08:53:59.143302', 0, '50.00', 'WEDNESDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 61, 1, 50),
(228, 'system', '2022-05-11 08:54:01.854575', NULL, '2022-05-11 08:54:01.854575', 0, '50.00', 'THURSDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 61, 1, 50),
(229, 'system', '2022-05-11 08:54:04.185404', NULL, '2022-05-11 08:54:04.185404', 0, '50.00', 'FRIDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 61, 1, 50),
(230, 'system', '2022-05-11 08:54:06.488922', NULL, '2022-05-11 08:54:06.488922', 0, '50.00', 'SATURDAY', '20:00', '35.00', '45.00', '08:00', '40.00', 2, 3, 21, 61, 1, 50),
(231, 'system', '2022-05-11 08:54:25.848887', NULL, '2022-05-11 08:54:25.848887', 0, '60.00', 'SUNDAY', '20:00', '45.00', '55.00', '08:00', '50.00', 2, 3, 21, 62, 1, 50),
(232, 'system', '2022-05-11 08:54:28.672543', NULL, '2022-05-11 08:54:28.672543', 0, '60.00', 'MONDAY', '20:00', '45.00', '55.00', '08:00', '50.00', 2, 3, 21, 62, 1, 50),
(233, 'system', '2022-05-11 08:54:31.434054', NULL, '2022-05-11 08:54:31.434054', 0, '60.00', 'TUESDAY', '20:00', '45.00', '55.00', '08:00', '50.00', 2, 3, 21, 62, 1, 50),
(234, 'system', '2022-05-11 08:54:33.981211', NULL, '2022-05-11 08:54:33.981211', 0, '60.00', 'WEDNESDAY', '20:00', '45.00', '55.00', '08:00', '50.00', 2, 3, 21, 62, 1, 50),
(235, 'system', '2022-05-11 08:54:36.004291', NULL, '2022-05-11 08:54:36.004291', 0, '60.00', 'THURSDAY', '20:00', '45.00', '55.00', '08:00', '50.00', 2, 3, 21, 62, 1, 50),
(236, 'system', '2022-05-11 08:54:38.060872', NULL, '2022-05-11 08:54:38.060872', 0, '60.00', 'FRIDAY', '20:00', '45.00', '55.00', '08:00', '50.00', 2, 3, 21, 62, 1, 50),
(237, 'system', '2022-05-11 08:54:39.857252', NULL, '2022-05-11 08:54:39.857252', 0, '60.00', 'SATURDAY', '20:00', '45.00', '55.00', '08:00', '50.00', 2, 3, 21, 62, 1, 50),
(238, 'system', '2022-05-11 08:57:44.242623', NULL, '2022-05-11 08:57:44.242623', 0, '65.00', 'SUNDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 59, 4, 49),
(239, 'system', '2022-05-11 08:57:49.391560', NULL, '2022-05-11 08:57:49.391560', 0, '65.00', 'MONDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 59, 4, 49),
(240, 'system', '2022-05-11 08:57:53.469177', NULL, '2022-05-11 08:57:53.469177', 0, '65.00', 'TUESDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 59, 4, 49),
(241, 'system', '2022-05-11 08:57:55.984936', NULL, '2022-05-11 08:57:55.984936', 0, '65.00', 'WEDNESDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 59, 4, 49),
(242, 'system', '2022-05-11 08:57:58.738045', NULL, '2022-05-11 08:57:58.738045', 0, '65.00', 'THURSDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 59, 4, 49),
(243, 'system', '2022-05-11 08:58:01.112442', NULL, '2022-05-11 08:58:01.112442', 0, '65.00', 'FRIDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 59, 4, 49),
(244, 'system', '2022-05-11 08:58:03.520729', NULL, '2022-05-11 08:58:03.520729', 0, '65.00', 'SATURDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 59, 4, 49),
(246, 'system', '2022-05-11 08:58:20.187558', NULL, '2022-05-11 08:58:20.187558', 0, '65.00', 'SUNDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 60, 4, 49),
(247, 'system', '2022-05-11 08:58:22.983004', NULL, '2022-05-11 08:58:22.983004', 0, '65.00', 'MONDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 60, 4, 49),
(248, 'system', '2022-05-11 08:58:25.312487', NULL, '2022-05-11 08:58:25.312487', 0, '65.00', 'TUESDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 60, 4, 49),
(249, 'system', '2022-05-11 08:58:28.150365', NULL, '2022-05-11 08:58:28.150365', 0, '65.00', 'WEDNESDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 60, 4, 49),
(250, 'system', '2022-05-11 08:58:30.757041', NULL, '2022-05-11 08:58:30.757041', 0, '65.00', 'THURSDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 60, 4, 49),
(251, 'system', '2022-05-11 08:58:32.713255', NULL, '2022-05-11 08:58:32.713255', 0, '65.00', 'FRIDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 60, 4, 49),
(252, 'system', '2022-05-11 08:58:36.365054', NULL, '2022-05-11 08:58:36.365054', 0, '65.00', 'SATURDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 60, 4, 49),
(253, 'system', '2022-05-11 08:59:10.180639', NULL, '2022-05-11 08:59:10.180639', 0, '65.00', 'SUNDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 61, 4, 50),
(254, 'system', '2022-05-11 08:59:13.570838', NULL, '2022-05-11 08:59:13.570838', 0, '65.00', 'MONDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 61, 4, 50),
(255, 'system', '2022-05-11 08:59:19.083609', NULL, '2022-05-11 08:59:19.083609', 0, '65.00', 'WEDNESDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 61, 4, 50),
(256, 'system', '2022-05-11 08:59:23.013083', NULL, '2022-05-11 08:59:23.013083', 0, '65.00', 'THURSDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 61, 4, 50),
(257, 'system', '2022-05-11 08:59:26.013742', NULL, '2022-05-11 08:59:26.013742', 0, '65.00', 'FRIDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 61, 4, 50),
(258, 'system', '2022-05-11 08:59:28.106968', NULL, '2022-05-11 08:59:28.106968', 0, '65.00', 'SATURDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 61, 4, 50),
(259, 'system', '2022-05-11 08:59:33.369937', NULL, '2022-05-11 08:59:33.369937', 0, '65.00', 'SUNDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 62, 4, 50),
(260, 'system', '2022-05-11 08:59:39.554576', NULL, '2022-05-11 08:59:39.554576', 0, '65.00', 'MONDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 62, 4, 50),
(261, 'system', '2022-05-11 08:59:43.287845', NULL, '2022-05-11 08:59:43.287845', 0, '65.00', 'TUESDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 62, 4, 50),
(262, 'system', '2022-05-11 08:59:46.073505', NULL, '2022-05-11 08:59:46.073505', 0, '65.00', 'WEDNESDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 62, 4, 50),
(263, 'system', '2022-05-11 08:59:50.151211', NULL, '2022-05-11 08:59:50.151211', 0, '65.00', 'THURSDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 62, 4, 50),
(264, 'system', '2022-05-11 08:59:53.258067', NULL, '2022-05-11 08:59:53.258067', 0, '65.00', 'FRIDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 62, 4, 50),
(265, 'system', '2022-05-11 08:59:56.006550', NULL, '2022-05-11 08:59:56.006550', 0, '65.00', 'SATURDAY', '08:00', '50.00', '60.00', '20:00', '55.00', 2, 3, 21, 62, 4, 50),
(266, 'system', '2022-05-23 09:50:00.951416', NULL, '2022-05-23 09:50:00.951416', 0, '50.00', 'SUNDAY', '20:00', '20.00', '40.00', '08:00', '30.00', 4, 4, 24, 64, 1, 52),
(267, 'system', '2022-05-23 09:50:05.608295', NULL, '2022-05-23 09:50:05.608295', 0, '50.00', 'MONDAY', '20:00', '20.00', '40.00', '08:00', '30.00', 4, 4, 24, 64, 1, 52),
(268, 'system', '2022-05-23 09:50:08.102363', NULL, '2022-05-23 09:50:08.102363', 0, '50.00', 'TUESDAY', '20:00', '20.00', '40.00', '08:00', '30.00', 4, 4, 24, 64, 1, 52),
(269, 'system', '2022-05-23 09:50:10.796544', NULL, '2022-05-23 09:50:10.796544', 0, '50.00', 'WEDNESDAY', '20:00', '20.00', '40.00', '08:00', '30.00', 4, 4, 24, 64, 1, 52),
(270, 'system', '2022-05-23 09:50:13.244834', NULL, '2022-05-23 09:50:13.244834', 0, '50.00', 'THURSDAY', '20:00', '20.00', '40.00', '08:00', '30.00', 4, 4, 24, 64, 1, 52),
(271, 'system', '2022-05-23 09:50:15.536400', NULL, '2022-05-23 09:50:15.536400', 0, '50.00', 'FRIDAY', '20:00', '20.00', '40.00', '08:00', '30.00', 4, 4, 24, 64, 1, 52),
(272, 'system', '2022-05-23 09:50:18.046610', NULL, '2022-05-23 09:50:18.046610', 0, '50.00', 'SATURDAY', '20:00', '20.00', '40.00', '08:00', '30.00', 4, 4, 24, 64, 1, 52),
(273, 'system', '2022-05-23 09:51:05.395792', NULL, '2022-05-23 09:51:05.395792', 0, '60.00', 'SUNDAY', '08:00', '30.00', '50.00', '20:00', '40.00', 4, 4, 24, 64, 4, 52),
(274, 'system', '2022-05-23 09:51:08.637409', NULL, '2022-05-23 09:51:08.637409', 0, '60.00', 'MONDAY', '08:00', '30.00', '50.00', '20:00', '40.00', 4, 4, 24, 64, 4, 52),
(275, 'system', '2022-05-23 09:51:11.536262', NULL, '2022-05-23 09:51:11.536262', 0, '60.00', 'TUESDAY', '08:00', '30.00', '50.00', '20:00', '40.00', 4, 4, 24, 64, 4, 52),
(276, 'system', '2022-05-23 09:51:14.062497', NULL, '2022-05-23 09:51:14.062497', 0, '60.00', 'WEDNESDAY', '08:00', '30.00', '50.00', '20:00', '40.00', 4, 4, 24, 64, 4, 52),
(277, 'system', '2022-05-23 09:51:16.650564', NULL, '2022-05-23 09:51:16.650564', 0, '60.00', 'THURSDAY', '08:00', '30.00', '50.00', '20:00', '40.00', 4, 4, 24, 64, 4, 52),
(278, 'system', '2022-05-23 09:51:20.030645', NULL, '2022-05-23 09:51:20.030645', 0, '60.00', 'FRIDAY', '08:00', '30.00', '50.00', '20:00', '40.00', 4, 4, 24, 64, 4, 52),
(279, 'system', '2022-05-23 09:51:24.888827', NULL, '2022-05-23 09:51:24.888827', 0, '60.00', 'SATURDAY', '08:00', '30.00', '50.00', '20:00', '40.00', 4, 4, 24, 64, 4, 52),
(280, 'system', '2022-05-23 09:53:00.925182', NULL, '2022-05-23 09:58:33.175212', 1, '95.00', 'SUNDAY', '20:00', '65.00', '85.00', '08:00', '75.00', 4, 4, 24, 64, 6, 52),
(281, 'system', '2022-05-23 09:53:05.133540', NULL, '2022-05-23 09:53:05.133540', 0, '90.00', 'MONDAY', '20:00', '60.00', '80.00', '08:00', '70.00', 4, 4, 24, 64, 6, 52),
(282, 'system', '2022-05-23 09:53:08.011373', NULL, '2022-05-23 09:53:08.011373', 0, '90.00', 'TUESDAY', '20:00', '60.00', '80.00', '08:00', '70.00', 4, 4, 24, 64, 6, 52),
(283, 'system', '2022-05-23 09:53:11.159883', NULL, '2022-05-23 09:53:11.159883', 0, '90.00', 'WEDNESDAY', '20:00', '60.00', '80.00', '08:00', '70.00', 4, 4, 24, 64, 6, 52),
(284, 'system', '2022-05-23 09:53:13.938219', NULL, '2022-05-23 09:53:13.938219', 0, '90.00', 'THURSDAY', '20:00', '60.00', '80.00', '08:00', '70.00', 4, 4, 24, 64, 6, 52),
(285, 'system', '2022-05-23 09:53:17.450034', NULL, '2022-05-23 09:53:17.450034', 0, '90.00', 'FRIDAY', '20:00', '60.00', '80.00', '08:00', '70.00', 4, 4, 24, 64, 6, 52),
(286, 'system', '2022-05-23 09:53:19.681358', NULL, '2022-05-23 09:53:19.681358', 0, '90.00', 'SATURDAY', '20:00', '60.00', '80.00', '08:00', '70.00', 4, 4, 24, 64, 6, 52),
(287, 'system', '2022-05-23 09:53:41.132897', NULL, '2022-05-23 09:53:41.132897', 0, '100.00', 'SUNDAY', '08:00', '70.00', '90.00', '20:00', '80.00', 4, 4, 24, 64, 6, 52),
(288, 'system', '2022-05-23 09:53:45.626547', NULL, '2022-05-23 09:53:45.626547', 0, '100.00', 'MONDAY', '08:00', '70.00', '90.00', '20:00', '80.00', 4, 4, 24, 64, 6, 52),
(289, 'system', '2022-05-23 09:53:49.649217', NULL, '2022-05-23 09:53:49.649217', 0, '100.00', 'TUESDAY', '08:00', '70.00', '90.00', '20:00', '80.00', 4, 4, 24, 64, 6, 52),
(290, 'system', '2022-05-23 09:53:54.481625', NULL, '2022-05-23 09:53:54.481625', 0, '100.00', 'WEDNESDAY', '08:00', '70.00', '90.00', '20:00', '80.00', 4, 4, 24, 64, 6, 52),
(291, 'system', '2022-05-23 09:53:58.980110', NULL, '2022-05-23 09:53:58.980110', 0, '100.00', 'THURSDAY', '08:00', '70.00', '90.00', '20:00', '80.00', 4, 4, 24, 64, 6, 52),
(292, 'system', '2022-05-23 09:54:02.606326', NULL, '2022-05-23 09:54:02.606326', 0, '100.00', 'FRIDAY', '08:00', '70.00', '90.00', '20:00', '80.00', 4, 4, 24, 64, 6, 52),
(293, 'system', '2022-05-23 09:54:05.230213', NULL, '2022-05-23 09:54:05.230213', 0, '100.00', 'SATURDAY', '08:00', '70.00', '90.00', '20:00', '80.00', 4, 4, 24, 64, 6, 52);

-- --------------------------------------------------------

--
-- Table structure for table `charge_rate`
--

CREATE TABLE `charge_rate` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `charge_rate` decimal(19,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `charge_rate`
--

INSERT INTO `charge_rate` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `charge_rate`) VALUES
(1, 'system', '2022-05-10 10:11:13.983839', NULL, '2022-05-10 10:11:13.983839', 0, '0.10');

-- --------------------------------------------------------

--
-- Table structure for table `client`
--

CREATE TABLE `client` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `county` varchar(255) DEFAULT NULL,
  `first_line` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `second_line` varchar(255) DEFAULT NULL,
  `town` varchar(255) DEFAULT NULL,
  `billing_email` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `telephone` varchar(255) DEFAULT NULL,
  `service_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `client`
--

INSERT INTO `client` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `county`, `first_line`, `postcode`, `second_line`, `town`, `billing_email`, `email`, `logo`, `name`, `status`, `telephone`, `service_id`) VALUES
(17, 'System', '2022-05-10 10:21:16.409771', NULL, '2022-05-10 10:21:16.409771', 0, NULL, '24/7 Road', 'MK11TT', NULL, 'Milton Keynes', '<EMAIL>', '<EMAIL>', 'string', '24/7 Support', 'ACTIVE', '090890', 2),
(20, 'System', '2022-05-10 10:27:41.851732', NULL, '2022-05-10 10:27:41.851732', 0, NULL, '24 Road', 'MK11TT', NULL, 'Milton Keynes', '<EMAIL>', '<EMAIL>', 'string', '24 Support', 'ACTIVE', '090890', 2),
(21, 'System', '2022-05-10 13:12:50.808858', NULL, '2022-05-10 13:12:50.808858', 0, NULL, 'Client Street', 'HH33HH', NULL, 'Harare', '<EMAIL>', '<EMAIL>', 'string', 'Client', 'ACTIVE', '8900272', 2),
(23, 'System', '2022-05-11 20:12:08.335147', NULL, '2022-05-11 20:12:08.335147', 0, NULL, '2670 Test Address', '00263', NULL, 'Harare', '<EMAIL>', '<EMAIL>', 'string', 'Tinashe Test Client', 'ACTIVE', '+263774483890', 1),
(24, 'System', '2022-05-23 08:01:10.253039', NULL, '2022-05-23 08:01:10.253039', 0, NULL, 'Clean Client Road', 'IP99HH', NULL, 'Ipswich', '<EMAIL>', '<EMAIL>', 'string', 'ClientClean', 'ACTIVE', '097918191', 3);

-- --------------------------------------------------------

--
-- Table structure for table `expense_rate`
--

CREATE TABLE `expense_rate` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `rate` decimal(19,2) DEFAULT NULL,
  `unit` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `expense_rate`
--

INSERT INTO `expense_rate` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `name`, `rate`, `unit`) VALUES
(1, 'system', '2022-03-25 17:43:24.990269', NULL, '2022-03-25 17:43:24.990269', 0, 'Transport', '10.00', 1),
(2, 'system', '2022-04-10 21:06:30.113938', NULL, '2022-04-10 21:06:30.113938', 0, 'Rate 1', '1.20', 1),
(3, 'system', '2022-05-09 10:52:55.051051', NULL, '2022-05-09 10:52:55.051051', 0, 'Overtime', '10.00', 1);

-- --------------------------------------------------------

--
-- Table structure for table `invoice`
--

CREATE TABLE `invoice` (
  `id` bigint(20) NOT NULL,
  `agent_id` bigint(20) DEFAULT NULL,
  `client_id` bigint(20) DEFAULT NULL,
  `invoice_status` varchar(255) DEFAULT NULL,
  `payment_ref` varchar(50) DEFAULT NULL,
  `total_amount` decimal(19,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `invoice`
--

INSERT INTO `invoice` (`id`, `agent_id`, `client_id`, `invoice_status`, `payment_ref`, `total_amount`) VALUES
(22, 1, 21, 'UNPAID', NULL, '625.00'),
(23, 1, 21, 'UNPAID', NULL, '2475.00'),
(24, 2, 21, 'UNPAID', NULL, '350.00'),
(25, 2, 21, 'UNPAID', NULL, '2240.00'),
(26, 2, 21, 'UNPAID', NULL, '93658.00'),
(27, 2, 21, 'UNPAID', NULL, '240.00'),
(28, 2, 21, 'UNPAID', NULL, '725.00'),
(29, 2, 21, 'UNPAID', NULL, '400.00'),
(30, 2, 21, 'UNPAID', NULL, '312.00'),
(31, 2, 21, 'UNPAID', NULL, '360.00'),
(32, 2, 21, 'UNPAID', NULL, '1000.00'),
(33, 2, 21, 'UNPAID', NULL, '200.00'),
(34, 2, 21, 'UNPAID', NULL, '50.00'),
(35, 2, 21, 'UNPAID', NULL, '260.00'),
(36, 2, 21, 'UNPAID', NULL, '600.00'),
(37, 2, 21, 'UNPAID', NULL, '1580.00'),
(38, 2, 21, 'UNPAID', NULL, '700.00'),
(39, 2, 21, 'UNPAID', NULL, '3160.00'),
(40, 2, 21, 'UNPAID', NULL, '3476.00'),
(41, 2, 21, 'UNPAID', NULL, '240.00'),
(42, 4, 24, 'UNPAID', NULL, '200.00'),
(43, 4, 24, 'UNPAID', NULL, '500.00'),
(44, 4, 24, 'UNPAID', NULL, '600.00'),
(45, 4, 24, 'UNPAID', NULL, '300.00'),
(46, 4, 24, 'UNPAID', NULL, '300.00'),
(47, 4, 24, 'UNPAID', NULL, '-1560.00'),
(48, 4, 24, 'UNPAID', NULL, '-800.00'),
(49, 4, 24, 'UNPAID', NULL, '100.00'),
(50, 4, 24, 'UNPAID', NULL, '-800.00'),
(51, 4, 24, 'UNPAID', NULL, '-800.00'),
(52, 4, 24, 'UNPAID', NULL, '500.00'),
(53, 4, 24, 'UNPAID', NULL, '1200.00'),
(54, 4, 24, 'UNPAID', NULL, '1000.00'),
(55, 4, 24, 'UNPAID', NULL, '1000.00'),
(56, 4, 24, 'UNPAID', NULL, '1200.00'),
(57, 4, 24, 'UNPAID', NULL, '150.00');

-- --------------------------------------------------------

--
-- Table structure for table `invoice_item`
--

CREATE TABLE `invoice_item` (
  `id` bigint(20) NOT NULL,
  `day_of_the_week` varchar(255) DEFAULT NULL,
  `end_date` varchar(255) DEFAULT NULL,
  `end_time` varchar(255) DEFAULT NULL,
  `number_of_hours_worked` float NOT NULL,
  `rate` decimal(19,2) DEFAULT NULL,
  `shift_id` bigint(20) DEFAULT NULL,
  `start_date` varchar(255) DEFAULT NULL,
  `start_time` varchar(255) DEFAULT NULL,
  `total` decimal(19,2) DEFAULT NULL,
  `invoice_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `invoice_item`
--

INSERT INTO `invoice_item` (`id`, `day_of_the_week`, `end_date`, `end_time`, `number_of_hours_worked`, `rate`, `shift_id`, `start_date`, `start_time`, `total`, `invoice_id`) VALUES
(10, 'MONDAY', '2022-04-11', '00:00', 0, '40.00', 395, '2022-04-11', '00:00', '0.00', 22),
(11, 'SATURDAY', '2022-04-16', '10:00', 10, '35.00', 396, '2022-04-16', '00:00', '350.00', 22),
(12, 'MONDAY', '2022-04-11', '11:00', 11, '25.00', 397, '2022-04-11', '00:00', '275.00', 22),
(13, 'SUNDAY', '2022-04-10', '09:00', 9, '40.00', 374, '2022-04-10', '00:00', '360.00', 23),
(14, 'MONDAY', '2022-05-09', '21:00', 21, '60.00', 349, '2022-05-09', '00:00', '1260.00', 23),
(15, 'WEDNESDAY', '2022-05-11', '09:00', 9, '60.00', 348, '2022-05-11', '00:00', '540.00', 23),
(16, 'WEDNESDAY', '2022-05-11', '09:30', 9, '35.00', 353, '2022-05-11', '00:00', '315.00', 23),
(17, 'FRIDAY', '2022-04-08', '14:00', 14, '25.00', 419, '2022-04-08', '00:00', '350.00', 24),
(18, 'TUESDAY', '2022-04-06', '08:00', 32, '35.00', 417, '2022-04-05', '00:00', '1120.00', 25),
(19, 'WEDNESDAY', '2022-04-06', '08:00', 32, '35.00', 417, '2022-04-05', '00:00', '1120.00', 25),
(20, 'SUNDAY', '2022-02-20', '08:00', 176, '70.00', 422, '2022-02-13', '00:00', '12320.00', 26),
(21, 'SUNDAY', '2022-02-20', '08:00', 176, '30.00', 422, '2022-02-13', '00:00', '5280.00', 26),
(22, 'SUNDAY', '2022-02-20', '08:00', 176, '30.00', 422, '2022-02-13', '00:00', '5280.00', 26),
(23, 'MONDAY', '2022-02-20', '08:00', 176, '25.00', 422, '2022-02-13', '00:00', '4400.00', 26),
(24, 'WEDNESDAY', '2022-02-20', '08:00', 176, '25.00', 422, '2022-02-13', '00:00', '4400.00', 26),
(25, 'SATURDAY', '2022-02-20', '08:00', 176, '28.00', 422, '2022-02-13', '00:00', '4928.00', 26),
(26, 'WEDNESDAY', '2022-02-20', '08:00', 176, '25.00', 422, '2022-02-13', '00:00', '4400.00', 26),
(27, 'MONDAY', '2022-02-20', '08:00', 176, '25.00', 422, '2022-02-13', '00:00', '4400.00', 26),
(28, 'SUNDAY', '2022-02-20', '08:00', 176, '70.00', 422, '2022-02-13', '00:00', '12320.00', 26),
(29, 'SUNDAY', '2022-02-20', '08:00', 176, '30.00', 422, '2022-02-13', '00:00', '5280.00', 26),
(30, 'SUNDAY', '2022-02-20', '08:00', 176, '30.00', 422, '2022-02-13', '00:00', '5280.00', 26),
(31, 'SUNDAY', '2022-02-20', '08:00', 176, '70.00', 422, '2022-02-13', '00:00', '12320.00', 26),
(32, 'SUNDAY', '2022-02-20', '08:00', 176, '30.00', 422, '2022-02-13', '00:00', '5280.00', 26),
(33, 'SUNDAY', '2022-02-20', '08:00', 176, '30.00', 422, '2022-02-13', '00:00', '5280.00', 26),
(34, 'WEDNESDAY', '2022-03-31', '08:00', 32, '35.00', 416, '2022-03-30', '00:00', '1120.00', 26),
(35, 'THURSDAY', '2022-03-31', '08:00', 32, '35.00', 416, '2022-03-30', '00:00', '1120.00', 26),
(36, 'MONDAY', '2022-04-11', '10:00', 10, '25.00', 393, '2022-04-11', '00:00', '250.00', 26),
(37, 'TUESDAY', '2022-04-12', '10:00', 10, '24.00', 394, '2022-04-12', '00:00', '240.00', 27),
(38, 'FRIDAY', '2022-04-29', '10:00', 10, '25.00', 414, '2022-04-29', '00:00', '250.00', 28),
(39, 'TUESDAY', '2022-05-10', '19:00', 19, '25.00', 368, '2022-05-10', '00:00', '475.00', 28),
(40, 'SUNDAY', '2022-04-17', '10:00', 10, '40.00', 398, '2022-04-17', '00:00', '400.00', 29),
(41, 'SATURDAY', '2022-04-30', '12:00', 12, '26.00', 415, '2022-04-30', '00:00', '312.00', 30),
(42, 'SUNDAY', '2022-05-01', '09:00', 9, '40.00', 390, '2022-05-01', '00:00', '360.00', 31),
(43, 'TUESDAY', '2022-05-10', '20:00', 20, '50.00', 377, '2022-05-10', '00:00', '1000.00', 32),
(44, 'THURSDAY', '2022-05-12', '00:00', 0, '25.00', 463, '2022-05-12', '00:00', '0.00', 32),
(45, 'WEDNESDAY', '2022-05-18', '14:00', 2, '25.00', 514, '2022-05-18', '12:00', '50.00', 33),
(46, 'WEDNESDAY', '2022-05-18', '14:00', 2, '25.00', 515, '2022-05-18', '12:00', '50.00', 33),
(47, 'WEDNESDAY', '2022-05-18', '14:00', 2, '25.00', 516, '2022-05-18', '12:00', '50.00', 33),
(48, 'WEDNESDAY', '2022-05-18', '14:00', 2, '25.00', 517, '2022-05-18', '12:00', '50.00', 33),
(49, 'WEDNESDAY', '2022-05-18', '21:00', 2, '25.00', 508, '2022-05-18', '19:00', '50.00', 34),
(50, 'SATURDAY', '2022-05-14', '10:00', 10, '26.00', 357, '2022-05-14', '00:00', '260.00', 35),
(51, 'WEDNESDAY', '2022-05-18', '20:00', 12, '25.00', 528, '2022-05-18', '08:00', '300.00', 36),
(52, 'WEDNESDAY', '2022-05-18', '20:00', 12, '25.00', 529, '2022-05-18', '08:00', '300.00', 36),
(53, 'SATURDAY', '2022-05-15', '08:00', 10, '28.00', 530, '2022-05-14', '22:00', '280.00', 37),
(54, 'SUNDAY', '2022-05-15', '08:00', 10, '70.00', 530, '2022-05-14', '22:00', '700.00', 37),
(55, 'SUNDAY', '2022-05-15', '08:00', 10, '30.00', 530, '2022-05-14', '22:00', '300.00', 37),
(56, 'SUNDAY', '2022-05-15', '08:00', 10, '30.00', 530, '2022-05-14', '22:00', '300.00', 37),
(57, 'TUESDAY', '2022-05-11', '08:00', 10, '35.00', 531, '2022-05-10', '22:00', '350.00', 38),
(58, 'WEDNESDAY', '2022-05-11', '08:00', 10, '35.00', 531, '2022-05-10', '22:00', '350.00', 38),
(59, 'SATURDAY', '2022-05-08', '12:00', 20, '28.00', 532, '2022-05-07', '16:00', '560.00', 39),
(60, 'SUNDAY', '2022-05-08', '12:00', 20, '70.00', 532, '2022-05-07', '16:00', '1400.00', 39),
(61, 'SUNDAY', '2022-05-08', '12:00', 20, '30.00', 532, '2022-05-07', '16:00', '600.00', 39),
(62, 'SUNDAY', '2022-05-08', '12:00', 20, '30.00', 532, '2022-05-07', '16:00', '600.00', 39),
(63, 'SATURDAY', '2022-05-08', '12:00', 22, '28.00', 533, '2022-05-07', '14:00', '616.00', 40),
(64, 'SUNDAY', '2022-05-08', '12:00', 22, '70.00', 533, '2022-05-07', '14:00', '1540.00', 40),
(65, 'SUNDAY', '2022-05-08', '12:00', 22, '30.00', 533, '2022-05-07', '14:00', '660.00', 40),
(66, 'SUNDAY', '2022-05-08', '12:00', 22, '30.00', 533, '2022-05-07', '14:00', '660.00', 40),
(67, 'TUESDAY', '2022-05-17', '2022-05-17T23:59', 12, '24.00', 494, '2022-05-17', '2022-05-17T00:00', '288.00', 41),
(68, 'WEDNESDAY', '2022-05-18', '2022-05-18T06:00', -2, '24.00', 494, '2022-05-18', '2022-05-18T00:00', '-48.00', 41),
(69, 'SUNDAY', '2022-05-01', '2022-05-01T14:00', 4, '50.00', 535, '2022-05-01', '2022-05-01T10:00', '200.00', 42),
(70, 'MONDAY', '2022-05-02', '2022-05-02T18:00', 10, '50.00', 536, '2022-05-02', '2022-05-02T08:00', '500.00', 43),
(71, 'WEDNESDAY', '2022-05-04', '2022-05-04T20:00', 12, '50.00', 537, '2022-05-04', '2022-05-04T08:00', '600.00', 44),
(72, 'TUESDAY', '2022-05-03', '2022-05-03T14:00', 6, '50.00', 538, '2022-05-03', '2022-05-03T08:00', '300.00', 45),
(73, 'THURSDAY', '2022-05-05', '2022-05-05T23:59', 12, '50.00', 541, '2022-05-05', '2022-05-05T00:00', '600.00', 46),
(74, 'FRIDAY', '2022-05-06', '2022-05-06T02:00', -6, '50.00', 541, '2022-05-06', '2022-05-06T00:00', '-300.00', 46),
(75, 'SUNDAY', '2022-05-01', '2022-05-01T23:59', -12, '60.00', 543, '2022-05-01', '2022-05-01T00:00', '-720.00', 47),
(76, 'MONDAY', '2022-05-02', '2022-05-02T06:00', -14, '60.00', 543, '2022-05-02', '2022-05-02T00:00', '-840.00', 47),
(77, 'FRIDAY', '2022-05-06', '2022-05-06T00:00', -16, '50.00', 545, '2022-05-06', '2022-05-06T16:00', '-800.00', 48),
(78, 'SATURDAY', '2022-05-07', '2022-05-07T22:00', 2, '50.00', 544, '2022-05-07', '2022-05-07T18:00', '100.00', 49),
(79, 'SUNDAY', '2022-05-08', '2022-05-08T00:00', -16, '50.00', 546, '2022-05-08', '2022-05-08T16:00', '-800.00', 50),
(80, 'SUNDAY', '2022-05-08', '2022-05-08T00:00', -16, '50.00', 546, '2022-05-08', '2022-05-08T16:00', '-800.00', 51),
(81, 'MONDAY', '2022-05-09', '2022-05-09T22:00', 10, '50.00', 547, '2022-05-09', '2022-05-09T10:00', '500.00', 52),
(82, 'THURSDAY', '2022-05-12', '2022-05-12T23:59', 12, '50.00', 549, '2022-05-12', '2022-05-12T00:00', '600.00', 53),
(83, 'FRIDAY', '2022-05-13', '2022-05-13T20:00', 12, '50.00', 549, '2022-05-13', '2022-05-13T00:00', '600.00', 53),
(84, 'WEDNESDAY', '2022-05-11', '2022-05-11T23:59', 12, '50.00', 548, '2022-05-11', '2022-05-11T00:00', '600.00', 54),
(85, 'THURSDAY', '2022-05-12', '2022-05-12T16:00', 8, '50.00', 548, '2022-05-12', '2022-05-12T00:00', '400.00', 54),
(86, 'SATURDAY', '2022-05-14', '2022-05-14T23:59', 12, '50.00', 552, '2022-05-14', '2022-05-14T00:00', '600.00', 55),
(87, 'SUNDAY', '2022-05-15', '2022-05-15T16:00', 8, '50.00', 552, '2022-05-15', '2022-05-15T00:00', '400.00', 55),
(88, 'SUNDAY', '2022-05-15', '2022-05-15T23:59', 12, '50.00', 553, '2022-05-15', '2022-05-15T00:00', '600.00', 56),
(89, 'MONDAY', '2022-05-16', '2022-05-16T20:00', 12, '50.00', 553, '2022-05-16', '2022-05-16T00:00', '600.00', 56),
(90, 'TUESDAY', '2022-05-24', '2022-05-24T15:00', 3, '50.00', 554, '2022-05-24', '2022-05-24T12:00', '150.00', 57);

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `agency_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `name`, `agency_id`) VALUES
(1, 'chrissd', '2022-03-15 12:35:53.747018', NULL, '2022-03-15 12:35:53.747018', 0, 'Test', NULL),
(2, 'chrissd', '2022-03-15 13:26:15.345113', NULL, '2022-03-15 13:26:15.345113', 0, 'Health Care', NULL),
(3, 'chrissd', '2022-05-23 07:42:10.955491', NULL, '2022-05-23 07:42:10.955491', 0, 'Cleaning', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `shift`
--

CREATE TABLE `shift` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `applied_date` datetime(6) DEFAULT NULL,
  `applied_status` varchar(255) DEFAULT NULL,
  `authorized_date` datetime(6) DEFAULT NULL,
  `booked_date` datetime(6) DEFAULT NULL,
  `break_time` varchar(255) DEFAULT NULL,
  `cancel_reason` varchar(255) DEFAULT NULL,
  `cancelled_date` datetime(6) DEFAULT NULL,
  `directly_book_by_worker` bit(1) DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `hours_before_broadcasting` int(11) NOT NULL,
  `notes` varchar(255) DEFAULT NULL,
  `publish_to_all_workers` bit(1) DEFAULT NULL,
  `queried_date` datetime(6) DEFAULT NULL,
  `query_reason` varchar(255) DEFAULT NULL,
  `require_application_by_workers` bit(1) DEFAULT NULL,
  `shift_date` date DEFAULT NULL,
  `shift_end_time` varchar(255) DEFAULT NULL,
  `shift_start_time` varchar(255) DEFAULT NULL,
  `shift_status` varchar(255) DEFAULT NULL,
  `show_note_to_agency` bit(1) DEFAULT NULL,
  `show_note_to_fw` bit(1) DEFAULT NULL,
  `agent_id` bigint(20) DEFAULT NULL,
  `assignment_code_id` bigint(20) DEFAULT NULL,
  `client_id` bigint(20) DEFAULT NULL,
  `directorate_id` bigint(20) DEFAULT NULL,
  `shift_type_id` bigint(20) DEFAULT NULL,
  `worker_id` bigint(20) DEFAULT NULL,
  `shift_end_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `shift`
--

INSERT INTO `shift` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `applied_date`, `applied_status`, `authorized_date`, `booked_date`, `break_time`, `cancel_reason`, `cancelled_date`, `directly_book_by_worker`, `gender`, `hours_before_broadcasting`, `notes`, `publish_to_all_workers`, `queried_date`, `query_reason`, `require_application_by_workers`, `shift_date`, `shift_end_time`, `shift_start_time`, `shift_status`, `show_note_to_agency`, `show_note_to_fw`, `agent_id`, `assignment_code_id`, `client_id`, `directorate_id`, `shift_type_id`, `worker_id`, `shift_end_date`) VALUES
(337, '<EMAIL>', '2022-05-10 14:06:41.265060', NULL, '2022-05-12 00:00:00.486838', 2, NULL, NULL, NULL, NULL, '1hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift', b'1', NULL, NULL, b'0', '2022-05-11', '20:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-11'),
(338, '<EMAIL>', '2022-05-10 14:06:41.737041', NULL, '2022-05-12 00:00:00.560665', 2, NULL, NULL, NULL, NULL, '1hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift', b'1', NULL, NULL, b'0', '2022-05-11', '20:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-11'),
(339, '<EMAIL>', '2022-05-10 14:11:48.237709', NULL, '2022-05-14 00:00:00.298896', 2, NULL, NULL, NULL, NULL, '0hr 5mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'night shift', b'1', NULL, NULL, b'0', '2022-05-13', '12:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 4, NULL, '2022-04-13'),
(340, '<EMAIL>', '2022-05-10 14:11:48.778127', NULL, '2022-05-14 00:00:00.567225', 2, NULL, NULL, NULL, NULL, '0hr 5mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'night shift', b'1', NULL, NULL, b'0', '2022-05-13', '12:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 4, NULL, '2022-04-13'),
(341, '<EMAIL>', '2022-05-10 14:11:49.268743', NULL, '2022-05-12 00:00:00.619869', 2, NULL, NULL, NULL, NULL, '0hr 15mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'night shift', b'1', NULL, NULL, b'0', '2022-05-10', '20:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 4, NULL, '2022-04-10'),
(342, '<EMAIL>', '2022-05-10 14:11:49.741085', NULL, '2022-05-14 00:00:00.611436', 2, NULL, NULL, NULL, NULL, '0hr 5mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'night shift', b'1', NULL, NULL, b'0', '2022-05-13', '12:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 4, NULL, '2022-04-13'),
(343, '<EMAIL>', '2022-05-10 14:11:50.241397', NULL, '2022-05-12 14:22:54.148655', 3, NULL, NULL, NULL, '2022-05-10 15:01:35.034185', '0hr 10mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'night shift', b'1', NULL, NULL, b'0', '2022-05-12', '09:15', '00:00', 'CANCELLED', b'1', b'1', 1, 1, 21, 60, 4, 37, '2022-05-12'),
(344, '<EMAIL>', '2022-05-10 14:11:50.672220', NULL, '2022-05-14 00:00:00.655301', 2, NULL, NULL, NULL, NULL, '0hr 10mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'night shift', b'1', NULL, NULL, b'0', '2022-05-12', '09:15', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 4, NULL, '2022-05-12'),
(345, '<EMAIL>', '2022-05-10 14:11:51.375548', NULL, '2022-05-12 14:16:01.223799', 2, NULL, NULL, NULL, NULL, '0hr 15mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'night shift', b'1', NULL, NULL, b'0', '2022-05-10', '20:00', '00:00', 'DELETED', b'1', b'1', NULL, 1, 21, 60, 4, NULL, '2022-04-10'),
(346, '<EMAIL>', '2022-05-10 14:24:30.759956', NULL, '2022-05-14 00:00:00.665666', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day', b'0', NULL, NULL, b'1', '2022-05-10', '20:20', '00:00', 'EXPIRED', b'1', b'1', NULL, 3, 21, 61, 1, NULL, '2022-05-10'),
(347, '<EMAIL>', '2022-05-10 14:24:31.553070', NULL, '2022-05-14 00:00:00.681143', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day', b'0', NULL, NULL, b'1', '2022-05-11', '09:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 3, 21, 61, 1, NULL, '2022-04-11'),
(348, '<EMAIL>', '2022-05-10 14:27:48.367498', NULL, '2022-05-16 20:18:08.318491', 5, NULL, NULL, '2022-05-12 07:31:11.570102', '2022-05-10 14:27:50.202257', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day, direct book', b'0', NULL, NULL, b'0', '2022-05-11', '09:00', '00:00', 'BILLED', b'1', b'1', NULL, 3, 21, 62, 1, 38, '2022-05-11'),
(349, '<EMAIL>', '2022-05-10 14:27:49.048168', NULL, '2022-05-16 20:18:07.917776', 5, NULL, NULL, '2022-05-10 16:37:26.798687', '2022-05-10 14:27:50.480085', '0hr 20mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day, direct book', b'0', NULL, NULL, b'0', '2022-05-09', '21:00', '00:00', 'BILLED', b'1', b'1', NULL, 3, 21, 62, 1, 38, '2022-05-09'),
(350, '<EMAIL>', '2022-05-10 14:27:49.865528', NULL, '2022-05-12 14:23:22.199849', 3, NULL, NULL, NULL, '2022-05-10 14:27:51.597759', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day, direct book', b'0', NULL, NULL, b'0', '2022-05-15', '09:20', '00:00', 'CANCELLED', b'1', b'1', NULL, 3, 21, 62, 1, 38, '2022-05-15'),
(351, '<EMAIL>', '2022-05-10 14:34:11.393339', NULL, '2022-05-12 14:23:05.443241', 3, NULL, NULL, NULL, '2022-05-10 14:34:15.175714', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day, check direct booking conflicting times', b'0', NULL, NULL, b'0', '2022-05-13', '12:00', '00:00', 'CANCELLED', b'1', b'1', NULL, 1, 21, 60, 1, 37, '2022-04-13'),
(352, '<EMAIL>', '2022-05-10 14:34:12.244953', NULL, '2022-05-14 00:00:00.692726', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day, check direct booking conflicting times', b'0', NULL, NULL, b'0', '2022-05-13', '14:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-04-13'),
(353, '<EMAIL>', '2022-05-10 14:53:29.020503', NULL, '2022-05-16 20:18:08.645907', 5, NULL, NULL, '2022-05-12 07:31:14.920448', '2022-05-10 14:53:31.709791', '0hr 5mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day, direct book multiple workers', b'0', NULL, NULL, b'0', '2022-05-11', '09:30', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 62, 1, 37, '2022-05-11'),
(354, '<EMAIL>', '2022-05-10 14:53:30.444725', NULL, '2022-05-14 00:00:00.704746', 2, NULL, NULL, NULL, NULL, '0hr 10mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day, direct book multiple workers', b'0', NULL, NULL, b'0', '2022-05-11', '14:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 62, 1, NULL, '2022-05-11'),
(355, '<EMAIL>', '2022-05-10 14:53:31.410024', NULL, '2022-05-14 00:00:00.718923', 2, NULL, NULL, NULL, NULL, '0hr 5mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'standard day, direct book multiple workers', b'0', NULL, NULL, b'0', '2022-05-11', '12:45', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 62, 1, NULL, '2022-05-11'),
(356, '<EMAIL>', '2022-05-10 15:05:15.960542', NULL, '2022-05-12 14:23:18.921402', 4, NULL, 'APPLIED', NULL, '2022-05-12 07:31:43.409636', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-05-14', '10:00', '00:00', 'CANCELLED', b'1', b'1', 1, 1, 21, 60, 1, 37, '2022-05-14'),
(357, '<EMAIL>', '2022-05-10 15:05:16.866286', NULL, '2022-05-19 08:57:50.658248', 6, NULL, 'APPLIED', '2022-05-17 15:34:26.636890', '2022-05-12 16:02:18.827724', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-05-14', '10:00', '00:00', 'BILLED', b'1', b'1', 1, 1, 21, 60, 1, 37, '2022-05-14'),
(358, '<EMAIL>', '2022-05-10 15:05:17.855797', NULL, '2022-05-10 15:09:13.205055', 2, NULL, 'APPLIED', NULL, '2022-05-10 15:09:13.193971', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-06-14', '12:00', '00:00', 'NEW', b'1', b'1', 1, 1, 21, 60, 1, 37, '2022-05-14'),
(359, '<EMAIL>', '2022-05-10 15:05:18.908626', NULL, '2022-05-15 19:01:35.278566', 3, NULL, 'APPLIED', NULL, '2022-05-10 15:08:51.221545', '0hr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-05-15', '11:00', '00:00', 'EXPIRED', b'1', b'1', 1, 1, 21, 60, 1, 37, '2022-05-15'),
(360, '<EMAIL>', '2022-05-10 15:05:19.903648', NULL, '2022-05-10 15:13:01.468543', 3, NULL, 'APPLIED', NULL, '2022-05-10 15:09:06.970336', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-06-14', '12:00', '00:00', 'DELETED', b'1', b'1', 1, 1, 21, 60, 1, 37, '2022-05-14'),
(361, '<EMAIL>', '2022-05-10 15:05:21.015263', NULL, '2022-05-15 19:01:35.401912', 2, NULL, NULL, NULL, NULL, '0hr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-05-15', '11:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-15'),
(362, '<EMAIL>', '2022-05-10 15:06:24.505461', NULL, '2022-05-15 19:01:35.416451', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for publish', b'1', NULL, NULL, b'0', '2022-05-14', '10:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 62, 1, NULL, '2022-05-14'),
(363, '<EMAIL>', '2022-05-10 15:06:26.106638', NULL, '2022-05-19 09:12:32.572976', 3, NULL, NULL, NULL, '2022-05-10 15:09:15.744499', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for publish', b'1', NULL, NULL, b'0', '2022-06-14', '12:00', '00:00', 'CANCELLED', b'1', b'1', 1, 1, 21, 62, 1, 37, '2022-05-14'),
(364, '<EMAIL>', '2022-05-10 15:06:27.008197', NULL, '2022-05-18 08:39:05.107261', 4, NULL, NULL, '2022-05-18 08:39:05.103410', '2022-05-10 15:08:54.858428', '0hr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for publish', b'1', NULL, NULL, b'0', '2022-05-15', '11:00', '00:00', 'AUTHORIZED', b'1', b'1', 1, 1, 21, 62, 1, 37, '2022-05-15'),
(365, '<EMAIL>', '2022-05-10 15:08:02.536149', NULL, '2022-05-19 09:12:49.528534', 5, NULL, NULL, NULL, '2022-05-10 15:11:10.894976', '0hr 0mins', 'cancel', '2022-05-10 15:10:55.578450', NULL, 'NO_PREFERENCE', 1, 'shift for direct booking', b'0', NULL, NULL, b'0', '2022-06-16', '12:00', '00:00', 'CANCELLED', b'1', b'1', 1, 1, 21, 61, 1, 37, '2022-05-16'),
(366, '<EMAIL>', '2022-05-10 15:08:03.544806', NULL, '2022-05-19 08:44:18.967828', 4, NULL, NULL, '2022-05-19 08:44:18.898175', '2022-05-10 15:08:05.754826', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for direct booking', b'0', NULL, NULL, b'0', '2022-05-16', '10:00', '00:00', 'AUTHORIZED', b'1', b'1', NULL, 1, 21, 61, 1, 37, '2022-05-16'),
(367, '<EMAIL>', '2022-05-10 15:08:04.979609', NULL, '2022-05-18 00:00:00.334490', 3, NULL, NULL, NULL, '2022-05-10 15:08:06.141474', '0hr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for direct booking', b'0', NULL, NULL, b'0', '2022-05-17', '11:00', '00:00', 'AWAITING_AUTHORIZATION', b'1', b'1', NULL, 1, 21, 61, 1, 37, '2022-05-17'),
(368, '<EMAIL>', '2022-05-10 16:44:28.063543', NULL, '2022-05-18 10:02:32.303445', 5, NULL, NULL, '2022-05-11 14:24:07.439871', '2022-05-10 16:44:31.666938', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shifts', b'0', NULL, NULL, b'0', '2022-05-10', '19:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-10'),
(369, '<EMAIL>', '2022-05-10 16:44:29.749897', NULL, '2022-05-15 19:01:35.475518', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shifts', b'0', NULL, NULL, b'0', '2022-05-10', '21:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-10'),
(370, '<EMAIL>', '2022-05-10 16:44:30.935249', NULL, '2022-05-15 19:01:35.489812', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shifts', b'0', NULL, NULL, b'0', '2022-05-10', '20:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-10'),
(371, '<EMAIL>', '2022-05-10 16:48:43.917072', NULL, '2022-05-15 19:01:35.584940', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test shift, direct book', b'0', NULL, NULL, b'0', '2022-05-11', '04:30', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-11'),
(372, '<EMAIL>', '2022-05-10 16:48:45.342652', NULL, '2022-05-15 19:01:35.598808', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test shift, direct book', b'0', NULL, NULL, b'0', '2022-05-10', '21:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-10'),
(373, '<EMAIL>', '2022-05-10 16:48:46.630004', NULL, '2022-05-15 19:01:35.610067', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test shift, direct book', b'0', NULL, NULL, b'0', '2022-05-12', '05:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-12'),
(374, '<EMAIL>', '2022-05-10 16:52:15.262506', NULL, '2022-05-16 20:18:07.647933', 5, NULL, NULL, '2022-05-11 14:23:32.260408', '2022-05-10 16:52:16.868356', '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test ', b'0', NULL, NULL, b'0', '2022-04-10', '09:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 61, 4, 37, '2022-04-10'),
(375, '<EMAIL>', '2022-05-10 16:53:42.236242', NULL, '2022-05-15 19:01:35.623144', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test shift', b'0', NULL, NULL, b'0', '2022-05-10', '21:50', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 62, 4, NULL, '2022-05-10'),
(376, '<EMAIL>', '2022-05-10 17:04:42.379173', NULL, '2022-05-15 19:01:35.689117', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test same day shift booking start time lapsed', b'0', NULL, NULL, b'0', '2022-05-10', '12:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 61, 1, NULL, '2022-05-10'),
(377, '<EMAIL>', '2022-05-10 17:10:01.115501', NULL, '2022-05-19 08:21:44.519607', 5, NULL, NULL, '2022-05-11 14:24:11.118154', '2022-05-10 17:10:03.838843', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift ', b'0', NULL, NULL, b'0', '2022-05-10', '20:00', '00:00', 'BILLED', b'1', b'1', NULL, 3, 21, 60, 1, 38, '2022-05-10'),
(378, '<EMAIL>', '2022-05-10 17:10:43.178386', NULL, '2022-05-10 17:11:06.879374', 2, NULL, NULL, NULL, NULL, 'Date', NULL, NULL, NULL, 'MALE', 1, 'test ', b'0', NULL, NULL, b'0', '2022-04-10', '00:00', '00:00', 'DELETED', b'1', b'1', NULL, 3, 21, 62, 1, NULL, '2022-04-10'),
(379, '<EMAIL>', '2022-05-10 17:12:39.605117', NULL, '2022-05-15 19:01:35.724568', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test ', b'0', NULL, NULL, b'0', '2022-05-10', '02:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 3, 21, 60, 1, NULL, '2022-05-11'),
(380, '<EMAIL>', '2022-05-10 17:14:43.994910', NULL, '2022-05-19 00:00:00.297901', 3, NULL, NULL, NULL, '2022-05-10 17:14:45.551425', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift overlapping into next day', b'0', NULL, NULL, b'0', '2022-05-18', '02:00', '00:00', 'AWAITING_AUTHORIZATION', b'1', b'1', NULL, 1, 21, 59, 4, 37, '2022-05-19'),
(381, '<EMAIL>', '2022-05-10 17:54:05.810029', NULL, '2022-05-15 19:01:35.821518', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift to 2 agencies , publish', b'1', NULL, NULL, b'0', '2022-05-11', '14:16', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-11'),
(382, '<EMAIL>', '2022-05-10 17:54:07.518770', NULL, '2022-05-15 19:01:36.004486', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift to 2 agencies , publish', b'1', NULL, NULL, b'0', '2022-05-11', '14:16', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-11'),
(383, '<EMAIL>', '2022-05-10 17:55:39.171656', NULL, '2022-05-15 19:01:36.074964', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift to 2 agencies , directly book', b'0', NULL, NULL, b'0', '2022-05-11', '14:16', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-11'),
(384, '<EMAIL>', '2022-05-10 17:55:41.082914', NULL, '2022-05-15 19:01:36.096051', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift to 2 agencies , directly book', b'0', NULL, NULL, b'0', '2022-05-11', '14:16', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-11'),
(385, '<EMAIL>', '2022-05-10 17:58:09.992350', NULL, '2022-05-15 19:01:36.181064', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'direct book worker ', b'0', NULL, NULL, b'0', '2022-05-11', '17:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 61, 1, NULL, '2022-05-11'),
(386, '<EMAIL>', '2022-05-10 17:58:38.039309', NULL, '2022-05-11 14:31:50.268928', 5, NULL, NULL, '2022-05-11 14:24:00.807226', '2022-05-10 17:58:39.700714', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'direct book worker ', b'0', '2022-05-11 14:31:50.262233', 'query test, check in client profile and amend hrs', b'0', '2022-04-27', '17:00', '00:00', 'IN_QUERY', b'1', b'1', NULL, 1, 21, 61, 1, 37, '2022-04-27'),
(387, '<EMAIL>', '2022-05-10 18:57:27.892603', NULL, '2022-05-19 09:11:43.477098', 3, NULL, NULL, NULL, '2022-05-10 18:57:29.311683', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'past ', b'0', NULL, NULL, b'0', '2022-05-20', '04:00', '00:00', 'CANCELLED', b'1', b'1', NULL, 1, 21, 60, 1, 37, '2022-05-20'),
(388, '<EMAIL>', '2022-05-10 18:57:28.708639', NULL, '2022-05-20 00:00:00.836540', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'past ', b'0', NULL, NULL, b'0', '2022-05-20', '03:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-20'),
(389, '<EMAIL>', '2022-05-10 19:00:48.981951', NULL, '2022-05-20 00:00:01.055876', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'conflicting shift', b'0', NULL, NULL, b'0', '2022-05-20', '08:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 61, 1, NULL, '2022-05-20'),
(390, '<EMAIL>', '2022-05-10 19:08:05.372727', NULL, '2022-05-19 06:58:00.921825', 5, NULL, NULL, '2022-05-11 14:24:04.014648', '2022-05-10 19:08:08.187801', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'conflicting shift when booking ,multiple workers', b'0', NULL, NULL, b'0', '2022-05-01', '09:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 62, 1, 37, '2022-05-01'),
(391, '<EMAIL>', '2022-05-10 19:08:07.416530', NULL, '2022-05-19 09:12:10.684589', 3, NULL, NULL, NULL, '2022-05-10 19:08:08.999864', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'conflicting shift when booking ,multiple workers', b'0', NULL, NULL, b'0', '2022-05-21', '08:00', '00:00', 'CANCELLED', b'1', b'1', NULL, 1, 21, 62, 1, 37, '2022-05-21'),
(392, '<EMAIL>', '2022-05-11 08:16:45.227729', NULL, '2022-05-15 19:01:36.212698', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test 2', b'0', NULL, NULL, b'0', '2022-05-11', '14:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 61, 1, NULL, '2022-05-11'),
(393, '<EMAIL>', '2022-05-11 14:12:16.345874', NULL, '2022-05-17 15:38:38.832489', 5, NULL, NULL, '2022-05-11 14:23:36.929639', '2022-05-11 14:12:17.626946', '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'direct book', b'0', NULL, NULL, b'0', '2022-04-11', '10:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-04-11'),
(394, '<EMAIL>', '2022-05-11 14:14:09.227003', NULL, '2022-05-17 20:01:44.009266', 5, NULL, NULL, '2022-05-11 14:23:46.910791', '2022-05-11 14:14:10.615162', '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'direct book', b'0', NULL, NULL, b'0', '2022-04-12', '10:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 60, 1, 37, '2022-04-12'),
(395, '<EMAIL>', '2022-05-11 14:14:59.730672', NULL, '2022-05-16 19:38:56.412489', 5, NULL, NULL, '2022-05-11 14:23:41.427545', '2022-05-11 14:15:01.026812', '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'night shift', b'0', NULL, NULL, b'0', '2022-04-11', '00:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 61, 4, 37, '2022-04-11'),
(396, '<EMAIL>', '2022-05-11 14:16:42.027387', NULL, '2022-05-16 19:38:56.976916', 5, NULL, NULL, '2022-05-11 14:23:53.150095', '2022-05-11 14:16:43.387082', '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'direct book', b'0', NULL, NULL, b'0', '2022-04-16', '10:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 62, 1, 37, '2022-04-16'),
(397, '<EMAIL>', '2022-05-11 14:17:41.615883', NULL, '2022-05-16 19:38:57.207192', 5, NULL, NULL, '2022-05-11 14:23:44.089397', '2022-05-11 14:17:42.723340', '0hr 0mins', NULL, NULL, NULL, 'FEMALE', 1, 'direct booking', b'0', NULL, NULL, b'0', '2022-04-11', '11:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 41, '2022-04-11'),
(398, '<EMAIL>', '2022-05-11 14:19:11.372863', NULL, '2022-05-18 19:13:51.088614', 5, NULL, NULL, '2022-05-11 14:23:57.155228', '2022-05-11 14:19:12.587933', '0hr 0mins', NULL, NULL, NULL, 'FEMALE', 1, 'direct booking', b'0', NULL, NULL, b'0', '2022-04-17', '10:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 62, 1, 41, '2022-04-17'),
(399, '<EMAIL>', '2022-05-11 14:23:01.678634', NULL, '2022-05-18 15:02:31.590160', 5, NULL, NULL, '2022-05-11 14:23:50.441549', '2022-05-11 14:23:03.270448', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'direct booking, times overlapping day to night check billing rates', b'0', '2022-05-18 15:02:31.541073', 'time needs adjusting, test', b'0', '2022-04-14', '22:00', '00:00', 'IN_QUERY', b'1', b'1', NULL, 1, 21, 62, 1, 37, '2022-04-11'),
(400, '<EMAIL>', '2022-05-12 07:26:41.790117', NULL, '2022-05-15 19:01:36.224768', 2, NULL, NULL, NULL, NULL, 'Date', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(401, '<EMAIL>', '2022-05-12 07:27:34.759169', NULL, '2022-05-15 19:01:36.317417', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(402, '<EMAIL>', '2022-05-12 07:27:35.426778', NULL, '2022-05-15 19:01:36.330507', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(403, '<EMAIL>', '2022-05-12 07:27:36.254473', NULL, '2022-05-15 19:01:36.381150', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(404, '<EMAIL>', '2022-05-12 07:27:36.231209', NULL, '2022-05-15 19:01:36.397147', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(405, '<EMAIL>', '2022-05-12 07:27:36.186619', NULL, '2022-05-15 19:01:36.408509', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(406, '<EMAIL>', '2022-05-12 07:27:36.881425', NULL, '2022-05-15 19:01:36.423064', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(407, '<EMAIL>', '2022-05-12 07:27:37.474607', NULL, '2022-05-15 19:01:36.435476', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(408, '<EMAIL>', '2022-05-12 07:27:38.047157', NULL, '2022-05-15 19:01:36.494753', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(409, '<EMAIL>', '2022-05-12 07:27:41.447557', NULL, '2022-05-15 19:01:36.505297', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(410, '<EMAIL>', '2022-05-12 07:27:41.577090', NULL, '2022-05-15 19:01:36.518434', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(411, '<EMAIL>', '2022-05-12 07:27:42.381417', NULL, '2022-05-15 19:01:36.530310', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(412, '<EMAIL>', '2022-05-12 07:27:42.569013', NULL, '2022-05-15 19:01:36.586666', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(413, '<EMAIL>', '2022-05-12 07:27:42.694947', NULL, '2022-05-15 19:01:36.625091', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'lk', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 2, 23, 63, 1, NULL, '2022-05-12'),
(414, '<EMAIL>', '2022-05-12 07:35:35.519739', NULL, '2022-05-18 10:02:32.039959', 5, NULL, NULL, '2022-05-12 07:37:10.131473', '2022-05-12 07:35:36.814603', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test billing', b'0', NULL, NULL, b'0', '2022-04-29', '10:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-04-29'),
(415, '<EMAIL>', '2022-05-12 07:36:48.761079', NULL, '2022-05-19 06:54:30.016826', 5, NULL, NULL, '2022-05-12 07:37:14.179466', '2022-05-12 07:36:49.956011', '1hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test billing saturday shift', b'0', NULL, NULL, b'0', '2022-04-30', '12:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 60, 1, 37, '2022-04-30'),
(416, '<EMAIL>', '2022-05-12 08:36:02.930258', NULL, '2022-05-17 15:38:38.613840', 5, NULL, NULL, '2022-05-12 11:09:46.738894', '2022-05-12 08:36:04.241183', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'night shift into day shift, check billing, start date and and  date different', b'0', NULL, NULL, b'0', '2022-03-30', '08:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 4, 37, '2022-03-31'),
(417, '<EMAIL>', '2022-05-12 08:41:42.252573', NULL, '2022-05-17 01:33:39.802400', 5, NULL, NULL, '2022-05-12 11:09:49.186563', '2022-05-12 08:41:43.562299', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'start date and end date are different, break time included', b'0', NULL, NULL, b'0', '2022-04-05', '08:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 4, 37, '2022-04-06'),
(418, '<EMAIL>', '2022-05-12 08:56:48.485765', NULL, '2022-05-16 11:30:17.705054', 5, NULL, NULL, '2022-05-12 11:09:51.956318', '2022-05-12 08:56:49.936944', '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'shift same date, times overlap into different rate, no break.', b'0', NULL, NULL, b'0', '2022-04-07', '10:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-04-07'),
(419, '<EMAIL>', '2022-05-12 09:14:52.633988', NULL, '2022-05-17 01:22:10.527294', 5, NULL, NULL, '2022-05-12 11:09:54.964751', '2022-05-12 09:14:54.131708', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift same date, times overlap into different rate, break included', b'0', NULL, NULL, b'0', '2022-04-08', '14:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-04-08'),
(420, '<EMAIL>', '2022-05-12 09:57:22.780845', NULL, '2022-05-16 08:57:39.030140', 5, NULL, NULL, '2022-05-12 11:09:39.221309', '2022-05-12 09:57:24.244336', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x3, no break ', b'0', NULL, NULL, b'0', '2022-03-06', '16:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-03-07'),
(421, '<EMAIL>', '2022-05-12 10:21:41.117446', NULL, '2022-05-16 08:57:39.678972', 5, NULL, NULL, '2022-05-12 11:09:43.827729', '2022-05-12 10:21:42.424389', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x3, break included', b'0', NULL, NULL, b'0', '2022-03-20', '16:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-03-21'),
(422, '<EMAIL>', '2022-05-12 11:08:58.484450', NULL, '2022-05-17 15:38:38.118636', 5, NULL, NULL, '2022-05-12 11:09:35.839301', '2022-05-12 11:08:59.973523', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x, no break ', b'0', NULL, NULL, b'0', '2022-02-13', '08:00', '00:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-02-20'),
(423, '<EMAIL>', '2022-05-12 14:09:57.074322', NULL, '2022-05-15 19:01:36.698743', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 4, NULL, '2022-05-12'),
(424, '<EMAIL>', '2022-05-12 14:09:57.255730', NULL, '2022-05-15 19:01:36.711106', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 4, NULL, '2022-05-12'),
(425, '<EMAIL>', '2022-05-12 14:10:00.478000', NULL, '2022-05-15 19:01:36.724257', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 4, NULL, '2022-05-12'),
(426, '<EMAIL>', '2022-05-12 14:10:01.050874', NULL, '2022-05-15 19:01:36.770009', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 4, NULL, '2022-05-12'),
(427, '<EMAIL>', '2022-05-12 14:12:58.951426', NULL, '2022-05-12 14:23:00.471309', 3, NULL, NULL, NULL, '2022-05-12 14:13:02.486572', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mmm,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 39, '2022-05-12'),
(428, '<EMAIL>', '2022-05-12 14:12:59.410688', NULL, '2022-05-15 19:01:36.809188', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mmm,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(429, '<EMAIL>', '2022-05-12 14:13:02.126345', NULL, '2022-05-15 19:01:36.873730', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mmm,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(430, '<EMAIL>', '2022-05-12 14:13:02.373547', NULL, '2022-05-15 19:01:36.921229', 2, NULL, NULL, NULL, NULL, 'Date', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mmm,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(431, '<EMAIL>', '2022-05-12 14:19:57.773179', NULL, '2022-05-15 19:01:36.982589', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'nm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(432, '<EMAIL>', '2022-05-12 14:19:58.129216', NULL, '2022-05-15 19:01:37.083511', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'nm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(433, '<EMAIL>', '2022-05-12 14:20:46.361750', NULL, '2022-05-12 14:23:12.571872', 3, NULL, NULL, NULL, '2022-05-12 14:20:48.601287', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'nm', b'0', NULL, NULL, b'0', '2022-05-13', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 39, '2022-05-14'),
(434, '<EMAIL>', '2022-05-12 14:20:46.978717', NULL, '2022-05-12 14:23:15.912540', 3, NULL, NULL, NULL, '2022-05-12 14:20:48.589448', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'nm', b'0', NULL, NULL, b'0', '2022-05-13', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 39, '2022-05-14'),
(435, '<EMAIL>', '2022-05-12 14:25:12.035983', NULL, '2022-05-12 14:27:21.787088', 3, NULL, NULL, NULL, '2022-05-12 14:25:13.126725', 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'Twist', b'0', NULL, NULL, b'0', '2022-05-24', '00:00', '00:00', 'CANCELLED', b'1', b'1', NULL, 1, 21, 59, 1, 44, '2022-05-25'),
(436, '<EMAIL>', '2022-05-12 14:25:12.151420', NULL, '2022-05-12 14:27:26.065152', 3, NULL, NULL, NULL, '2022-05-12 14:25:13.246837', 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'Twist', b'0', NULL, NULL, b'0', '2022-05-24', '00:00', '00:00', 'CANCELLED', b'1', b'1', NULL, 1, 21, 59, 1, 44, '2022-05-25'),
(437, '<EMAIL>', '2022-05-12 14:34:07.087065', NULL, '2022-05-12 15:46:02.835470', 3, NULL, NULL, NULL, '2022-05-12 14:34:10.600653', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 40, '2022-05-12'),
(438, '<EMAIL>', '2022-05-12 14:34:07.210583', NULL, '2022-05-15 19:01:37.094106', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(439, '<EMAIL>', '2022-05-12 14:34:10.193570', NULL, '2022-05-15 19:01:37.102451', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(440, '<EMAIL>', '2022-05-12 14:34:10.438064', NULL, '2022-05-15 19:01:37.115448', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(441, '<EMAIL>', '2022-05-12 14:35:06.146950', NULL, '2022-05-15 19:01:37.181659', 2, NULL, NULL, NULL, NULL, 'Date', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(442, '<EMAIL>', '2022-05-12 14:37:11.865615', NULL, '2022-05-15 19:01:37.194091', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'nm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(443, '<EMAIL>', '2022-05-12 14:37:12.179085', NULL, '2022-05-15 19:01:37.203869', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'nm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(444, '<EMAIL>', '2022-05-12 14:37:15.247281', NULL, '2022-05-15 19:01:37.221448', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'nm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(445, '<EMAIL>', '2022-05-12 14:37:15.263644', NULL, '2022-05-15 19:01:37.275278', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'nm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(446, '<EMAIL>', '2022-05-12 14:47:41.736767', NULL, '2022-05-12 15:46:08.167699', 3, NULL, NULL, NULL, '2022-05-12 14:47:42.826794', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 44, '2022-05-12'),
(447, '<EMAIL>', '2022-05-12 14:47:42.072984', NULL, '2022-05-15 19:01:37.287690', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(448, '<EMAIL>', '2022-05-12 14:47:44.778121', NULL, '2022-05-15 19:01:37.298041', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(449, '<EMAIL>', '2022-05-12 14:50:34.759611', NULL, '2022-05-15 19:01:37.307467', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, ',,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(450, '<EMAIL>', '2022-05-12 14:50:35.166317', NULL, '2022-05-15 19:01:37.315638', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, ',,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(451, '<EMAIL>', '2022-05-12 14:50:38.639841', NULL, '2022-05-15 19:01:37.323122', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, ',,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(452, '<EMAIL>', '2022-05-12 14:52:51.534052', NULL, '2022-05-15 19:01:37.332137', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(453, '<EMAIL>', '2022-05-12 14:52:51.613000', NULL, '2022-05-15 19:01:37.402456', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(454, '<EMAIL>', '2022-05-12 14:52:54.140679', NULL, '2022-05-15 19:01:37.429568', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(455, '<EMAIL>', '2022-05-12 14:57:41.242066', NULL, '2022-05-15 19:01:37.479034', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'm,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(456, '<EMAIL>', '2022-05-12 14:57:41.774120', NULL, '2022-05-15 19:01:37.508986', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'm,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(457, '<EMAIL>', '2022-05-12 14:57:44.852730', NULL, '2022-05-15 19:01:37.520943', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'm,', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(458, '<EMAIL>', '2022-05-12 15:04:20.726166', NULL, '2022-05-18 09:52:12.479677', 3, NULL, NULL, NULL, '2022-05-12 15:04:22.005639', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-05-26', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 39, '2022-05-27'),
(459, '<EMAIL>', '2022-05-12 15:04:21.580954', NULL, '2022-05-26 00:00:00.216471', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-05-26', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-27'),
(460, '<EMAIL>', '2022-05-12 15:04:24.034077', NULL, '2022-05-26 00:00:00.412034', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-05-26', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-27'),
(461, '<EMAIL>', '2022-05-12 15:13:52.697241', NULL, '2022-05-12 15:46:12.095942', 3, NULL, NULL, NULL, '2022-05-12 15:13:54.012792', 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 37, '2022-05-12'),
(462, '<EMAIL>', '2022-05-12 15:13:53.084344', NULL, '2022-05-15 19:01:37.574649', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(463, '<EMAIL>', '2022-05-12 15:13:56.347261', NULL, '2022-05-19 08:21:45.599648', 5, NULL, NULL, '2022-05-17 15:34:20.808930', '2022-05-12 15:13:57.605949', 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'BILLED', b'0', b'0', NULL, 1, 21, 59, 1, 42, '2022-05-12'),
(464, '<EMAIL>', '2022-05-12 15:14:18.289618', NULL, '2022-05-15 19:01:37.583003', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(465, '<EMAIL>', '2022-05-12 15:14:18.293697', NULL, '2022-05-15 19:01:37.596490', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(466, '<EMAIL>', '2022-05-12 15:14:22.017741', NULL, '2022-05-15 19:01:37.605074', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(467, '<EMAIL>', '2022-05-12 15:15:43.494052', NULL, '2022-05-19 09:12:17.104428', 3, NULL, NULL, NULL, '2022-05-12 15:15:45.320680', 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-06-01', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 37, '2022-06-02'),
(468, '<EMAIL>', '2022-05-12 15:15:43.780642', NULL, '2022-05-18 09:53:51.938058', 3, NULL, NULL, NULL, '2022-05-12 15:15:45.519501', 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-06-01', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 44, '2022-06-02'),
(469, '<EMAIL>', '2022-05-12 15:15:47.558631', NULL, '2022-05-19 09:12:20.027147', 3, NULL, NULL, NULL, '2022-05-12 15:15:48.540600', 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'mh', b'0', NULL, NULL, b'0', '2022-06-01', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 42, '2022-06-02'),
(470, '<EMAIL>', '2022-05-12 15:45:37.663071', NULL, '2022-05-19 09:12:23.434437', 3, NULL, NULL, NULL, '2022-05-12 15:45:38.873089', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-06-03', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 39, '2022-06-04'),
(471, '<EMAIL>', '2022-05-12 15:45:37.949437', NULL, '2022-05-19 09:12:26.753691', 3, NULL, NULL, NULL, '2022-05-12 15:45:39.174049', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-06-03', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 44, '2022-06-04'),
(472, '<EMAIL>', '2022-05-12 15:45:41.648976', NULL, '2022-05-19 09:12:29.556764', 3, NULL, NULL, NULL, '2022-05-12 15:45:42.566736', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'jk', b'0', NULL, NULL, b'0', '2022-06-03', '00:00', '00:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 42, '2022-06-04'),
(473, '<EMAIL>', '2022-05-12 16:11:05.244812', NULL, '2022-05-15 19:01:37.626439', 2, NULL, NULL, NULL, NULL, 'Date', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(474, '<EMAIL>', '2022-05-12 16:12:04.608718', NULL, '2022-05-15 19:01:37.698183', 2, NULL, NULL, NULL, NULL, '2hr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(475, '<EMAIL>', '2022-05-12 16:12:04.869705', NULL, '2022-05-15 19:01:37.714390', 2, NULL, NULL, NULL, NULL, '3hr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'mm', b'1', NULL, NULL, b'0', '2022-05-12', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-12'),
(476, '<EMAIL>', '2022-05-15 17:48:57.300412', NULL, '2022-05-15 19:01:37.731468', 2, NULL, NULL, NULL, NULL, '1hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test shift end date', b'1', NULL, NULL, b'0', '2022-05-15', '11:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-16'),
(477, '<EMAIL>', '2022-05-15 17:48:57.300117', NULL, '2022-05-15 19:01:37.820988', 2, NULL, NULL, NULL, NULL, '1hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test shift end date', b'1', NULL, NULL, b'0', '2022-05-15', '11:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-16'),
(478, '<EMAIL>', '2022-05-15 17:49:03.795311', NULL, '2022-05-15 19:01:37.921384', 2, NULL, NULL, NULL, NULL, '1hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test shift end date', b'1', NULL, NULL, b'0', '2022-05-15', '11:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-16'),
(479, '<EMAIL>', '2022-05-15 17:49:04.192318', NULL, '2022-05-15 19:01:37.993311', 2, NULL, NULL, NULL, NULL, '1hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'test shift end date', b'1', NULL, NULL, b'0', '2022-05-15', '11:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-16'),
(480, '<EMAIL>', '2022-05-15 19:11:19.185536', NULL, '2022-05-19 09:12:13.518858', 3, NULL, NULL, NULL, '2022-05-15 19:11:20.879526', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Tester', b'0', NULL, NULL, b'0', '2022-05-28', '03:00', '03:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 42, '2022-05-29'),
(481, '<EMAIL>', '2022-05-15 19:11:31.918893', NULL, '2022-05-15 19:11:32.021301', 1, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Tester', b'0', NULL, NULL, b'0', '2022-05-28', '03:00', '03:00', 'NEW', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-29'),
(482, '<EMAIL>', '2022-05-15 19:11:57.800895', NULL, '2022-05-18 09:52:35.634123', 3, NULL, NULL, NULL, '2022-05-15 19:11:59.230961', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Tester', b'0', NULL, NULL, b'0', '2022-05-29', '05:00', '03:00', 'CANCELLED', b'0', b'0', NULL, 1, 21, 59, 1, 42, '2022-05-30'),
(483, '<EMAIL>', '2022-05-15 20:00:23.418811', NULL, '2022-05-17 12:00:00.572857', 2, NULL, NULL, NULL, NULL, '0hr 30mins', NULL, NULL, NULL, 'MALE', 1, 'start and end time display test', b'1', NULL, NULL, b'0', '2022-05-17', '16:00', '12:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-17'),
(484, '<EMAIL>', '2022-05-15 20:00:23.424056', NULL, '2022-05-16 08:20:00.572490', 2, NULL, NULL, NULL, NULL, '1hr nullmins', NULL, NULL, NULL, 'MALE', 1, 'start and end time display test', b'1', NULL, NULL, b'0', '2022-05-16', '16:45', '08:20', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-16'),
(485, '<EMAIL>', '2022-05-15 20:00:23.424970', NULL, '2022-05-16 08:20:00.624945', 2, NULL, NULL, NULL, NULL, '1hr nullmins', NULL, NULL, NULL, 'MALE', 1, 'start and end time display test', b'1', NULL, NULL, b'0', '2022-05-16', '16:45', '08:20', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-16');
INSERT INTO `shift` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `applied_date`, `applied_status`, `authorized_date`, `booked_date`, `break_time`, `cancel_reason`, `cancelled_date`, `directly_book_by_worker`, `gender`, `hours_before_broadcasting`, `notes`, `publish_to_all_workers`, `queried_date`, `query_reason`, `require_application_by_workers`, `shift_date`, `shift_end_time`, `shift_start_time`, `shift_status`, `show_note_to_agency`, `show_note_to_fw`, `agent_id`, `assignment_code_id`, `client_id`, `directorate_id`, `shift_type_id`, `worker_id`, `shift_end_date`) VALUES
(486, '<EMAIL>', '2022-05-15 20:00:23.432346', NULL, '2022-05-17 07:00:00.287029', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'start and end time display test', b'1', NULL, NULL, b'0', '2022-05-17', '11:00', '07:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-17'),
(487, '<EMAIL>', '2022-05-15 20:01:40.551366', NULL, '2022-05-17 08:00:00.072501', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'start and end time display test apply', b'0', NULL, NULL, b'1', '2022-05-17', '10:00', '08:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-17'),
(488, '<EMAIL>', '2022-05-15 20:01:40.656301', NULL, '2022-05-16 10:10:00.364945', 2, NULL, NULL, NULL, NULL, '0hr nullmins', NULL, NULL, NULL, 'MALE', 1, 'start and end time display test apply', b'0', NULL, NULL, b'1', '2022-05-16', '12:12', '10:10', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-16'),
(489, '<EMAIL>', '2022-05-15 20:01:40.815940', NULL, '2022-05-17 13:00:00.090888', 2, NULL, NULL, NULL, NULL, '0hr 30mins', NULL, NULL, NULL, 'MALE', 1, 'start and end time display test apply', b'0', NULL, NULL, b'1', '2022-05-17', '17:00', '13:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-17'),
(490, '<EMAIL>', '2022-05-15 20:05:27.484534', NULL, '2022-05-17 13:00:00.114796', 2, NULL, NULL, NULL, NULL, '0hr 30mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'start and end time display test direct book', b'0', NULL, NULL, b'0', '2022-05-17', '17:00', '13:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 62, 1, NULL, '2022-05-17'),
(491, '<EMAIL>', '2022-05-15 20:05:27.731418', NULL, '2022-05-15 20:05:30.296050', 2, NULL, NULL, NULL, NULL, '0hr 30mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'start and end time display test direct book', b'0', NULL, NULL, b'0', '2022-04-30', '15:00', '10:30', 'EXPIRED', b'1', b'1', NULL, 1, 21, 62, 1, NULL, '2022-04-30'),
(492, '<EMAIL>', '2022-05-15 20:05:27.700088', NULL, '2022-05-17 08:00:00.176594', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'start and end time display test direct book', b'0', NULL, NULL, b'0', '2022-05-17', '10:00', '08:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 62, 1, NULL, '2022-05-17'),
(493, '<EMAIL>', '2022-05-16 18:17:06.121954', NULL, '2022-05-17 03:00:00.377921', 2, NULL, NULL, NULL, NULL, '2hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'Tinashe Test Shift', b'0', NULL, NULL, b'0', '2022-05-17', '05:00', '03:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-18'),
(494, '<EMAIL>', '2022-05-16 18:17:06.122429', NULL, '2022-05-23 14:55:08.295831', 5, NULL, NULL, '2022-05-19 12:25:07.538387', '2022-05-16 18:17:09.458867', '1hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'Tinashe Test Shift', b'0', NULL, NULL, b'0', '2022-05-16', '06:00', '22:00', 'BILLED', b'1', b'1', NULL, 1, 21, 60, 1, 37, '2022-05-17'),
(495, '<EMAIL>', '2022-05-16 18:17:06.122899', NULL, '2022-05-19 08:44:30.118962', 4, NULL, NULL, '2022-05-19 08:44:30.108955', '2022-05-16 18:17:09.605261', 'nullhr nullmins', NULL, NULL, NULL, 'MALE', 1, 'Tinashe Test Shift', b'0', NULL, NULL, b'0', '2022-05-16', '23:00', '22:00', 'AUTHORIZED', b'1', b'1', NULL, 1, 21, 60, 1, 37, '2022-05-16'),
(496, '<EMAIL>', '2022-05-16 18:17:32.037998', NULL, '2022-05-17 03:00:00.640506', 2, NULL, NULL, NULL, NULL, '2hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'Tinashe Test Shift', b'0', NULL, NULL, b'0', '2022-05-17', '05:00', '03:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-18'),
(497, '<EMAIL>', '2022-05-16 18:17:33.201450', NULL, '2022-05-16 22:00:00.373475', 2, NULL, NULL, NULL, NULL, '0hr 15mins', NULL, NULL, NULL, 'MALE', 1, 'Tinashe Test Shift', b'0', NULL, NULL, b'0', '2022-05-16', '23:00', '22:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-16'),
(498, '<EMAIL>', '2022-05-16 18:17:33.246871', NULL, '2022-05-16 22:00:00.512932', 2, NULL, NULL, NULL, NULL, '1hr 0mins', NULL, NULL, NULL, 'MALE', 1, 'Tinashe Test Shift', b'0', NULL, NULL, b'0', '2022-05-16', '06:00', '22:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 60, 1, NULL, '2022-05-17'),
(499, '<EMAIL>', '2022-05-16 19:02:25.465149', NULL, '2022-05-16 19:02:30.706704', 2, NULL, NULL, NULL, NULL, 'Date', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'm', b'1', NULL, NULL, b'0', '2022-05-16', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-16'),
(500, '<EMAIL>', '2022-05-18 08:16:56.727188', NULL, '2022-05-18 08:17:00.274814', 2, NULL, NULL, NULL, NULL, 'Date', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test on autofill end date', b'1', NULL, NULL, b'0', '2022-05-18', '00:00', '00:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-18'),
(501, '<EMAIL>', '2022-05-18 08:45:17.034973', NULL, '2022-05-19 12:26:06.683291', 4, NULL, NULL, '2022-05-19 12:26:06.679646', '2022-05-18 08:45:18.566275', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test booking different workers allat once', b'0', NULL, NULL, b'0', '2022-05-18', '13:00', '11:00', 'AUTHORIZED', b'1', b'1', NULL, 1, 21, 59, 1, 40, '2022-05-18'),
(502, '<EMAIL>', '2022-05-18 08:45:17.623547', NULL, '2022-05-19 09:12:02.876786', 3, NULL, NULL, NULL, '2022-05-18 08:45:19.221030', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test booking different workers allat once', b'0', NULL, NULL, b'0', '2022-05-20', '13:00', '11:00', 'CANCELLED', b'1', b'1', NULL, 1, 21, 59, 1, 41, '2022-05-20'),
(503, '<EMAIL>', '2022-05-18 08:45:17.711259', NULL, '2022-05-19 09:11:38.970758', 3, NULL, NULL, NULL, '2022-05-18 08:45:19.634195', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test booking different workers allat once', b'0', NULL, NULL, b'0', '2022-05-19', '13:00', '11:00', 'CANCELLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-19'),
(504, '<EMAIL>', '2022-05-18 08:59:39.707771', NULL, '2022-05-19 08:00:01.324440', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book one worker different days different times', b'0', NULL, NULL, b'0', '2022-05-19', '10:00', '08:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-19'),
(505, '<EMAIL>', '2022-05-18 08:59:39.746468', NULL, '2022-05-19 09:12:06.261277', 3, NULL, NULL, NULL, '2022-05-18 08:59:41.926284', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book one worker different days different times', b'0', NULL, NULL, b'0', '2022-05-20', '08:00', '06:00', 'CANCELLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-20'),
(506, '<EMAIL>', '2022-05-18 08:59:39.814715', NULL, '2022-05-19 00:00:00.508024', 3, NULL, NULL, NULL, '2022-05-18 08:59:41.867065', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book one worker different days different times', b'0', NULL, NULL, b'0', '2022-05-18', '16:00', '14:00', 'AWAITING_AUTHORIZATION', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-18'),
(507, '<EMAIL>', '2022-05-18 09:05:53.065435', NULL, '2022-05-19 00:00:00.678166', 3, NULL, NULL, NULL, '2022-05-18 09:05:55.145670', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'one worker multiple shifts same day, different times', b'0', NULL, NULL, b'0', '2022-05-18', '23:30', '21:30', 'AWAITING_AUTHORIZATION', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-18'),
(508, '<EMAIL>', '2022-05-18 09:05:53.178307', NULL, '2022-05-19 08:52:08.569517', 5, NULL, NULL, '2022-05-19 08:45:03.069974', '2022-05-18 09:05:55.312399', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'one worker multiple shifts same day, different times', b'0', NULL, NULL, b'0', '2022-05-18', '21:00', '19:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-18'),
(509, '<EMAIL>', '2022-05-18 09:05:53.245141', NULL, '2022-05-19 00:00:00.910493', 3, NULL, NULL, NULL, '2022-05-18 09:05:55.513265', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'one worker multiple shifts same day, different times', b'0', NULL, NULL, b'0', '2022-05-18', '18:30', '16:30', 'AWAITING_AUTHORIZATION', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-18'),
(510, '<EMAIL>', '2022-05-18 09:12:23.026542', NULL, '2022-05-18 21:30:03.065034', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'one worker multiple shifts same day, overlapping', b'0', NULL, NULL, b'0', '2022-05-18', '23:30', '21:30', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-18'),
(511, '<EMAIL>', '2022-05-18 09:12:23.239597', NULL, '2022-05-18 16:00:00.246357', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'one worker multiple shifts same day, overlapping', b'0', NULL, NULL, b'0', '2022-05-18', '18:30', '16:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-18'),
(512, '<EMAIL>', '2022-05-18 09:12:23.329558', NULL, '2022-05-18 19:00:00.313199', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'one worker multiple shifts same day, overlapping', b'0', NULL, NULL, b'0', '2022-05-18', '21:00', '19:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-18'),
(513, '<EMAIL>', '2022-05-18 09:50:47.760701', NULL, '2022-05-19 00:00:00.981991', 3, NULL, NULL, NULL, '2022-05-18 09:50:55.576687', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '18:00', '16:00', 'AWAITING_AUTHORIZATION', b'1', b'1', NULL, 1, 21, 59, 1, 44, '2022-05-18'),
(514, '<EMAIL>', '2022-05-18 09:50:48.130518', NULL, '2022-05-19 08:48:58.419606', 5, NULL, NULL, '2022-05-19 08:44:36.814981', '2022-05-18 09:50:55.586252', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '14:00', '12:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 42, '2022-05-18'),
(515, '<EMAIL>', '2022-05-18 09:50:48.573296', NULL, '2022-05-19 08:48:58.909084', 5, NULL, NULL, '2022-05-19 08:44:40.323602', '2022-05-18 09:50:56.002052', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '14:00', '12:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 39, '2022-05-18'),
(516, '<EMAIL>', '2022-05-18 09:50:48.627050', NULL, '2022-05-19 08:48:59.137703', 5, NULL, NULL, '2022-05-19 08:44:44.564027', '2022-05-18 09:50:54.781438', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '14:00', '12:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 43, '2022-05-18'),
(517, '<EMAIL>', '2022-05-18 09:50:53.742700', NULL, '2022-05-19 08:48:59.318862', 5, NULL, NULL, '2022-05-19 08:44:49.072742', '2022-05-18 09:50:56.695403', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '14:00', '12:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 41, '2022-05-18'),
(518, '<EMAIL>', '2022-05-18 09:50:53.841169', NULL, '2022-05-19 00:00:01.312080', 3, NULL, NULL, NULL, '2022-05-18 09:50:55.985333', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '18:00', '16:00', 'AWAITING_AUTHORIZATION', b'1', b'1', NULL, 1, 21, 59, 1, 39, '2022-05-18'),
(519, '<EMAIL>', '2022-05-18 09:50:53.979282', NULL, '2022-05-19 00:00:01.897767', 3, NULL, NULL, NULL, '2022-05-18 09:50:56.498218', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '18:00', '16:00', 'AWAITING_AUTHORIZATION', b'1', b'1', NULL, 1, 21, 59, 1, 43, '2022-05-18'),
(520, '<EMAIL>', '2022-05-18 09:50:54.193943', NULL, '2022-05-18 16:00:00.342335', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '18:00', '16:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-18'),
(521, '<EMAIL>', '2022-05-18 09:50:58.059441', NULL, '2022-05-18 12:00:00.457862', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '14:00', '12:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-18'),
(522, '<EMAIL>', '2022-05-18 09:50:58.279778', NULL, '2022-05-19 12:31:55.863324', 4, NULL, NULL, '2022-05-19 12:31:55.859853', '2022-05-18 09:50:59.501414', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'book shifts for billing', b'0', NULL, NULL, b'0', '2022-05-18', '18:00', '16:00', 'AUTHORIZED', b'1', b'1', NULL, 1, 21, 59, 1, 41, '2022-05-18'),
(523, '<EMAIL>', '2022-05-18 14:58:05.273249', NULL, '2022-05-22 06:00:00.457964', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for application', b'0', NULL, NULL, b'1', '2022-05-22', '08:00', '06:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-22'),
(524, '<EMAIL>', '2022-05-18 14:58:05.270299', NULL, '2022-05-22 08:00:00.296716', 2, NULL, NULL, NULL, NULL, 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for application', b'0', NULL, NULL, b'1', '2022-05-22', '10:00', '08:00', 'EXPIRED', b'1', b'1', NULL, 1, 21, 59, 1, NULL, '2022-05-22'),
(525, '<EMAIL>', '2022-05-18 14:58:05.441430', NULL, '2022-05-23 06:00:03.415830', 3, NULL, 'APPLIED', NULL, '2022-05-18 15:00:13.464191', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for application', b'0', NULL, NULL, b'1', '2022-05-23', '08:00', '06:00', 'EXPIRED', b'1', b'1', 1, 1, 21, 59, 1, 37, '2022-05-23'),
(526, '<EMAIL>', '2022-05-18 14:58:08.990744', NULL, '2022-05-23 11:00:00.415305', 3, NULL, 'APPLIED', NULL, '2022-05-18 15:00:24.913385', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for application', b'0', NULL, NULL, b'1', '2022-05-23', '13:00', '11:00', 'EXPIRED', b'1', b'1', 1, 1, 21, 59, 1, 37, '2022-05-23'),
(527, '<EMAIL>', '2022-05-18 19:22:44.590092', NULL, '2022-05-18 19:22:45.081118', 2, NULL, NULL, NULL, NULL, 'Date', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test', b'1', NULL, NULL, b'0', '2022-05-18', '00:00', '00:00', 'EXPIRED', b'1', b'0', NULL, 1, 21, 59, 1, NULL, '2022-05-18'),
(528, '<EMAIL>', '2022-05-19 09:20:16.885163', NULL, '2022-05-19 09:23:29.350624', 5, NULL, NULL, '2022-05-19 09:22:57.921525', '2022-05-19 09:20:18.802951', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift same date,  break included, same rate', b'0', NULL, NULL, b'0', '2022-05-18', '20:00', '08:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 42, '2022-05-18'),
(529, '<EMAIL>', '2022-05-19 09:20:17.294377', NULL, '2022-05-19 09:23:29.653233', 5, NULL, NULL, '2022-05-19 09:23:04.457766', '2022-05-19 09:20:19.165456', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift same date,  break included, same rate', b'0', NULL, NULL, b'0', '2022-05-18', '20:00', '08:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-18'),
(530, '<EMAIL>', '2022-05-19 11:27:25.087734', NULL, '2022-05-19 11:29:15.094408', 5, NULL, NULL, '2022-05-19 11:28:51.417392', '2022-05-19 11:27:26.187506', '0hr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift start date and end date different, same rate ,no break', b'0', NULL, NULL, b'0', '2022-05-14', '08:00', '22:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-15'),
(531, '<EMAIL>', '2022-05-19 11:35:46.941080', NULL, '2022-05-19 11:37:37.941673', 5, NULL, NULL, '2022-05-19 11:37:02.535350', '2022-05-19 11:35:48.552923', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift start date and end date different, same rate, break included', b'0', NULL, NULL, b'0', '2022-05-10', '08:00', '22:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 4, 37, '2022-05-11'),
(532, '<EMAIL>', '2022-05-19 11:53:01.780505', NULL, '2022-05-19 11:57:28.669075', 5, NULL, NULL, '2022-05-19 11:56:59.755807', '2022-05-19 11:53:03.074448', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x3, no break ', b'0', NULL, NULL, b'0', '2022-05-07', '12:00', '16:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 37, '2022-05-08'),
(533, '<EMAIL>', '2022-05-19 12:15:15.455541', NULL, '2022-05-19 12:16:18.721949', 5, NULL, NULL, '2022-05-19 12:15:54.974437', '2022-05-19 12:15:16.554956', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x3, break included', b'0', NULL, NULL, b'0', '2022-05-07', '12:00', '14:00', 'BILLED', b'1', b'1', NULL, 1, 21, 59, 1, 42, '2022-05-08'),
(534, '<EMAIL>', '2022-05-23 15:22:34.526164', NULL, '2022-05-23 15:22:35.326222', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shifts same date, no break', b'0', NULL, NULL, b'0', '2022-05-02', '18:00', '08:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-02'),
(535, '<EMAIL>', '2022-05-23 15:22:34.528657', NULL, '2022-05-23 18:08:31.169773', 5, NULL, NULL, '2022-05-23 18:01:38.263581', '2022-05-23 15:22:36.644284', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shifts same date, no break', b'0', NULL, NULL, b'0', '2022-05-01', '14:00', '10:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-01'),
(536, '<EMAIL>', '2022-05-23 15:25:36.283252', NULL, '2022-05-23 18:09:46.534997', 5, NULL, NULL, '2022-05-23 18:02:25.508102', '2022-05-23 15:25:37.368144', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shifts same date, no break', b'0', NULL, NULL, b'0', '2022-05-02', '18:00', '08:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-02'),
(537, '<EMAIL>', '2022-05-23 15:32:17.793773', NULL, '2022-05-23 18:10:32.708932', 5, NULL, NULL, '2022-05-23 18:04:01.415293', '2022-05-23 15:32:18.855559', '2hr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift same date, same rate,  break included', b'0', NULL, NULL, b'0', '2022-05-04', '20:00', '08:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-04'),
(538, '<EMAIL>', '2022-05-23 15:32:17.898563', NULL, '2022-05-23 18:11:19.999436', 5, NULL, NULL, '2022-05-23 18:02:52.973209', '2022-05-23 15:32:19.057425', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift same date, same rate,  break included', b'0', NULL, NULL, b'0', '2022-05-03', '14:00', '08:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-03'),
(539, '<EMAIL>', '2022-05-23 15:41:54.379175', NULL, '2022-05-23 15:41:55.062925', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift start date and end date different, same rate ,no break', b'0', NULL, NULL, b'0', '2022-05-05', '06:00', '22:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-05'),
(540, '<EMAIL>', '2022-05-23 15:41:54.433291', NULL, '2022-05-23 15:41:55.146922', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift start date and end date different, same rate ,no break', b'0', NULL, NULL, b'0', '2022-05-04', '02:00', '22:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-05'),
(541, '<EMAIL>', '2022-05-23 15:43:21.976980', NULL, '2022-05-23 18:20:03.545794', 5, NULL, NULL, '2022-05-23 18:02:22.655687', '2022-05-23 15:43:23.065961', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift start date and end date different, same rate ,no break', b'0', NULL, NULL, b'0', '2022-05-04', '02:00', '22:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-05'),
(542, '<EMAIL>', '2022-05-23 15:45:44.606988', NULL, '2022-05-23 15:45:45.077421', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift start date and end date different, same rate ,no break', b'0', NULL, NULL, b'0', '2022-05-05', '06:00', '22:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 4, NULL, '2022-05-06'),
(543, '<EMAIL>', '2022-05-23 15:48:04.981015', NULL, '2022-05-23 18:23:39.574182', 5, NULL, NULL, '2022-05-23 18:02:16.852858', '2022-05-23 15:48:06.270848', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'Shift start date and end date different, same rate ,no break', b'0', NULL, NULL, b'0', '2022-04-30', '06:00', '22:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 4, 45, '2022-05-01'),
(544, '<EMAIL>', '2022-05-23 15:59:13.114461', NULL, '2022-05-23 18:28:56.840955', 5, NULL, NULL, '2022-05-23 18:02:20.356739', '2022-05-23 15:59:14.340537', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift same date, times overlap into different rate, no break.', b'0', NULL, NULL, b'0', '2022-05-07', '22:00', '18:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-07'),
(545, '<EMAIL>', '2022-05-23 15:59:13.310489', NULL, '2022-05-23 18:25:30.797517', 5, NULL, NULL, '2022-05-23 18:02:30.900092', '2022-05-23 15:59:14.767840', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift same date, times overlap into different rate, no break.', b'0', NULL, NULL, b'0', '2022-05-06', '00:00', '16:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-06'),
(546, '<EMAIL>', '2022-05-23 16:12:05.382689', NULL, '2022-05-24 07:44:47.093798', 5, NULL, NULL, '2022-05-23 18:02:27.620094', '2022-05-23 16:12:06.781591', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift same date, times overlap into different rate, break included, rate change ', b'0', NULL, NULL, b'0', '2022-05-08', '00:00', '16:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-08'),
(547, '<EMAIL>', '2022-05-23 16:12:05.602770', NULL, '2022-05-24 07:48:56.383448', 5, NULL, NULL, '2022-05-23 18:06:12.316324', '2022-05-23 16:12:06.797086', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift same date, times overlap into different rate, break included, rate change ', b'0', NULL, NULL, b'0', '2022-05-09', '22:00', '10:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-09'),
(548, '<EMAIL>', '2022-05-23 17:08:47.106868', NULL, '2022-05-24 08:02:50.743981', 5, NULL, NULL, '2022-05-23 18:06:15.697390', '2022-05-23 17:08:48.326118', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x2, no break ', b'0', NULL, NULL, b'0', '2022-05-10', '16:00', '16:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-11'),
(549, '<EMAIL>', '2022-05-23 17:08:47.316494', NULL, '2022-05-24 08:00:06.864959', 5, NULL, NULL, '2022-05-23 18:06:19.944743', '2022-05-23 17:08:48.525272', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x2, no break ', b'0', NULL, NULL, b'0', '2022-05-11', '20:00', '20:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-12'),
(550, '<EMAIL>', '2022-05-23 17:21:13.957852', NULL, '2022-05-23 17:21:15.093965', 2, NULL, NULL, NULL, NULL, '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x2, break included', b'0', NULL, NULL, b'0', '2022-05-14', '20:00', '20:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-14'),
(551, '<EMAIL>', '2022-05-23 17:21:14.149678', NULL, '2022-05-23 17:21:15.193176', 2, NULL, NULL, NULL, NULL, '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x2, break included', b'0', NULL, NULL, b'0', '2022-05-13', '16:00', '16:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-14'),
(552, '<EMAIL>', '2022-05-23 17:22:11.904388', NULL, '2022-05-24 08:05:20.020942', 5, NULL, NULL, '2022-05-23 18:06:23.307251', '2022-05-23 17:22:12.996276', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x2, break included', b'0', NULL, NULL, b'0', '2022-05-13', '16:00', '16:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-14'),
(553, '<EMAIL>', '2022-05-23 17:22:58.753166', NULL, '2022-05-24 08:11:01.965685', 5, NULL, NULL, '2022-05-23 18:06:27.416559', '2022-05-23 17:22:59.868342', '2hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift start date and end date different, times overlap into different rates x2, break included', b'0', NULL, NULL, b'0', '2022-05-14', '20:00', '20:00', 'BILLED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-15'),
(554, '<EMAIL>', '2022-05-24 08:31:55.986564', NULL, '2022-05-26 13:57:02.041632', 5, NULL, NULL, '2022-05-26 13:53:00.329833', '2022-05-24 08:50:46.011423', 'nullhr nullmins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'check time display is break time is not selected on shift creation', b'1', NULL, NULL, b'0', '2022-05-24', '15:00', '12:00', 'BILLED', b'1', b'1', 1, 4, 24, 64, 1, 45, '2022-05-24'),
(555, '<EMAIL>', '2022-05-24 08:43:20.990493', NULL, '2022-05-24 20:00:00.054599', 2, NULL, NULL, NULL, NULL, '1hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-05-24', '08:00', '20:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-25'),
(556, '<EMAIL>', '2022-05-24 08:43:21.216464', NULL, '2022-05-24 14:00:00.424662', 3, NULL, 'APPLIED', NULL, '2022-05-24 08:49:23.777370', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-05-24', '18:00', '14:00', 'EXPIRED', b'1', b'1', 1, 4, 24, 64, 1, 45, '2022-05-24'),
(557, '<EMAIL>', '2022-05-24 08:43:21.287089', NULL, '2022-05-24 18:00:00.067519', 2, NULL, NULL, NULL, NULL, '0hr 20mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-05-24', '20:00', '18:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-24'),
(558, '<EMAIL>', '2022-05-24 08:43:21.326831', NULL, '2022-05-24 15:00:00.196392', 3, NULL, 'APPLIED', NULL, '2022-05-24 08:49:04.941831', '0hr 30mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'shift for apply', b'0', NULL, NULL, b'1', '2022-05-24', '17:00', '15:00', 'EXPIRED', b'1', b'1', 1, 4, 24, 64, 1, 45, '2022-05-24'),
(559, '<EMAIL>', '2022-05-24 17:51:58.570353', NULL, '2022-05-24 17:52:00.216557', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'm', b'0', NULL, NULL, b'0', '2022-05-24', '00:00', '00:00', 'EXPIRED', b'0', b'0', NULL, 1, 21, 59, 6, NULL, '2022-05-24'),
(560, '<EMAIL>', '2022-05-24 17:53:45.698967', NULL, '2022-05-24 17:53:47.978928', 2, NULL, NULL, NULL, '2022-05-24 17:53:47.977169', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'km', b'0', NULL, NULL, b'0', '2022-06-01', '00:00', '00:00', 'BOOKED', b'0', b'0', NULL, 1, 21, 59, 1, 37, '2022-06-01'),
(561, '<EMAIL>', '2022-05-26 13:17:36.019520', NULL, '2022-05-26 18:00:00.186764', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, publish to', b'1', NULL, NULL, b'0', '2022-05-26', '20:00', '18:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-26'),
(562, '<EMAIL>', '2022-05-26 13:17:36.511275', NULL, '2022-05-26 13:17:40.302394', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, publish to', b'1', NULL, NULL, b'0', '2022-05-26', '16:00', '12:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-26'),
(563, '<EMAIL>', '2022-05-26 13:17:36.703792', NULL, '2022-05-26 13:36:43.335005', 2, NULL, NULL, NULL, '2022-05-26 13:36:43.332874', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, publish to', b'1', NULL, NULL, b'0', '2022-05-26', '00:00', '22:00', 'BOOKED', b'1', b'1', 1, 4, 24, 64, 1, 45, '2022-05-26'),
(564, '<EMAIL>', '2022-05-26 13:19:39.767519', NULL, '2022-05-26 22:00:00.196835', 3, NULL, 'APPLIED', NULL, '2022-05-26 13:36:29.092700', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, apply to', b'0', NULL, NULL, b'1', '2022-05-26', '00:00', '22:00', 'EXPIRED', b'1', b'1', 1, 4, 24, 64, 1, 45, '2022-05-26'),
(565, '<EMAIL>', '2022-05-26 13:19:39.897328', NULL, '2022-05-26 18:00:00.252011', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, apply to', b'0', NULL, NULL, b'1', '2022-05-26', '20:00', '18:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-26'),
(566, '<EMAIL>', '2022-05-26 13:19:40.084687', NULL, '2022-05-26 13:19:45.026106', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, apply to', b'0', NULL, NULL, b'1', '2022-05-26', '16:00', '12:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-26'),
(567, '<EMAIL>', '2022-05-26 13:34:20.116283', NULL, '2022-05-26 13:34:22.019834', 2, NULL, NULL, NULL, '2022-05-26 13:34:22.018328', '0hr 30mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, direct book', b'0', NULL, NULL, b'0', '2022-05-26', '18:00', '14:00', 'BOOKED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-26'),
(568, '<EMAIL>', '2022-05-26 13:34:20.195381', NULL, '2022-05-26 13:34:21.993982', 2, NULL, NULL, NULL, '2022-05-26 13:34:21.992861', '1hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, direct book', b'0', NULL, NULL, b'0', '2022-05-26', '12:00', '08:00', 'BOOKED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-26'),
(569, '<EMAIL>', '2022-05-26 13:34:20.669680', NULL, '2022-05-26 13:34:25.021579', 2, NULL, NULL, NULL, NULL, '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, direct book', b'0', NULL, NULL, b'0', '2022-05-25', '12:00', '08:00', 'EXPIRED', b'1', b'1', NULL, 4, 24, 64, 1, NULL, '2022-05-25'),
(570, '<EMAIL>', '2022-05-26 13:34:20.670963', NULL, '2022-05-26 13:53:07.489376', 4, NULL, NULL, '2022-05-26 13:53:07.488356', '2022-05-26 13:34:21.988557', '0hr 0mins', NULL, NULL, NULL, 'NO_PREFERENCE', 1, 'test shift, direct book', b'0', NULL, NULL, b'0', '2022-05-25', '17:00', '13:00', 'AUTHORIZED', b'1', b'1', NULL, 4, 24, 64, 1, 45, '2022-05-25');

-- --------------------------------------------------------

--
-- Table structure for table `shift_agency`
--

CREATE TABLE `shift_agency` (
  `shift_id` bigint(20) NOT NULL,
  `agency_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `shift_agency`
--

INSERT INTO `shift_agency` (`shift_id`, `agency_id`) VALUES
(337, 1),
(338, 1),
(339, 1),
(340, 1),
(341, 1),
(342, 1),
(343, 1),
(344, 1),
(345, 1),
(346, 1),
(347, 1),
(348, 1),
(349, 1),
(350, 1),
(351, 1),
(352, 1),
(353, 1),
(354, 1),
(355, 1),
(356, 1),
(357, 1),
(358, 1),
(359, 1),
(360, 1),
(361, 1),
(362, 1),
(363, 1),
(364, 1),
(365, 1),
(366, 1),
(367, 1),
(368, 1),
(369, 1),
(370, 1),
(371, 1),
(372, 1),
(373, 1),
(374, 1),
(375, 1),
(376, 1),
(377, 1),
(378, 1),
(379, 1),
(380, 1),
(381, 1),
(381, 2),
(382, 1),
(382, 2),
(383, 1),
(383, 2),
(384, 1),
(384, 2),
(385, 2),
(386, 2),
(387, 2),
(388, 2),
(389, 2),
(390, 1),
(390, 2),
(391, 1),
(391, 2),
(392, 2),
(393, 2),
(394, 2),
(395, 2),
(396, 2),
(397, 2),
(398, 2),
(399, 2),
(400, 1),
(401, 1),
(402, 1),
(403, 1),
(404, 1),
(405, 1),
(406, 1),
(407, 1),
(408, 1),
(409, 1),
(410, 1),
(411, 1),
(412, 1),
(413, 1),
(414, 2),
(415, 2),
(416, 2),
(417, 2),
(418, 2),
(419, 1),
(419, 2),
(420, 2),
(421, 2),
(422, 2),
(423, 1),
(424, 1),
(425, 1),
(426, 1),
(427, 1),
(428, 1),
(429, 1),
(430, 1),
(431, 1),
(432, 1),
(433, 1),
(434, 1),
(435, 1),
(436, 1),
(437, 1),
(438, 1),
(439, 1),
(440, 1),
(441, 1),
(442, 1),
(443, 1),
(444, 1),
(445, 1),
(446, 1),
(447, 1),
(448, 1),
(449, 1),
(450, 1),
(451, 1),
(452, 1),
(453, 1),
(454, 1),
(455, 1),
(456, 1),
(457, 1),
(458, 1),
(459, 1),
(460, 1),
(461, 1),
(462, 1),
(463, 1),
(464, 1),
(465, 1),
(466, 1),
(467, 1),
(468, 1),
(469, 1),
(470, 1),
(471, 1),
(472, 1),
(473, 1),
(474, 1),
(474, 2),
(475, 1),
(475, 2),
(476, 1),
(477, 1),
(478, 1),
(479, 1),
(480, 1),
(481, 1),
(482, 1),
(483, 2),
(484, 2),
(485, 2),
(486, 2),
(487, 2),
(488, 2),
(489, 2),
(490, 2),
(491, 2),
(492, 2),
(493, 1),
(494, 1),
(495, 1),
(496, 1),
(497, 1),
(498, 1),
(499, 1),
(500, 2),
(501, 2),
(502, 2),
(503, 2),
(504, 2),
(505, 2),
(506, 2),
(507, 2),
(508, 2),
(509, 2),
(510, 2),
(511, 2),
(512, 2),
(513, 2),
(514, 2),
(515, 2),
(516, 2),
(517, 2),
(518, 2),
(519, 2),
(520, 2),
(521, 2),
(522, 2),
(523, 2),
(524, 2),
(525, 2),
(526, 2),
(527, 2),
(528, 1),
(528, 2),
(529, 1),
(529, 2),
(530, 2),
(531, 2),
(532, 2),
(533, 1),
(534, 4),
(535, 4),
(536, 4),
(537, 4),
(538, 4),
(539, 4),
(540, 4),
(541, 4),
(542, 4),
(543, 4),
(544, 4),
(545, 4),
(546, 4),
(547, 4),
(548, 4),
(549, 4),
(550, 4),
(551, 4),
(552, 4),
(553, 4),
(554, 4),
(555, 4),
(556, 4),
(557, 4),
(558, 4),
(559, 2),
(560, 2),
(561, 4),
(562, 4),
(563, 4),
(564, 4),
(565, 4),
(566, 4),
(567, 4),
(568, 4),
(569, 4),
(570, 4);

-- --------------------------------------------------------

--
-- Table structure for table `shift_directorate`
--

CREATE TABLE `shift_directorate` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `phone_number` varchar(255) DEFAULT NULL,
  `location_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `shift_directorate`
--

INSERT INTO `shift_directorate` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `name`, `phone_number`, `location_id`) VALUES
(59, '<EMAIL>', '2022-05-10 13:35:31.155289', NULL, '2022-05-10 13:35:31.155289', 0, 'Karma', '8989011', 49),
(60, '<EMAIL>', '2022-05-10 13:35:45.390183', NULL, '2022-05-10 13:35:45.390183', 0, 'Maestro', '5782022', 49),
(61, '<EMAIL>', '2022-05-10 13:36:01.147176', NULL, '2022-05-10 13:36:01.147176', 0, 'Morningside', '09978191', 50),
(62, '<EMAIL>', '2022-05-10 13:36:21.903752', NULL, '2022-05-10 13:36:21.903752', 0, 'Sunninghill', '089222', 50),
(63, '<EMAIL>', '2022-05-11 20:15:36.612139', NULL, '2022-05-11 20:15:36.612139', 0, 'Mainway', '89', 51),
(64, '<EMAIL>', '2022-05-23 08:40:54.156769', NULL, '2022-05-23 08:40:54.156769', 0, 'Woking', '0909909', 52);

-- --------------------------------------------------------

--
-- Table structure for table `shift_location`
--

CREATE TABLE `shift_location` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `phone_number` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `client_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `shift_location`
--

INSERT INTO `shift_location` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `name`, `phone_number`, `postcode`, `client_id`) VALUES
(49, '<EMAIL>', '2022-05-10 13:34:52.939625', NULL, '2022-05-10 13:34:52.939625', 0, 'Harare', '02002022', 'HH44RR', 21),
(50, '<EMAIL>', '2022-05-10 13:35:09.561503', NULL, '2022-05-10 13:35:09.561503', 0, 'Gweru', '04040022', 'GW55RR', 21),
(51, '<EMAIL>', '2022-05-11 20:15:21.139117', NULL, '2022-05-11 20:15:21.139117', 0, 'Mainway', '897', '00123', 23),
(52, '<EMAIL>', '2022-05-23 08:40:31.369966', NULL, '2022-05-23 08:40:31.369966', 0, 'Surrey', '0719144387', 'GU212TP', 24);

-- --------------------------------------------------------

--
-- Table structure for table `shift_type`
--

CREATE TABLE `shift_type` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `shift_type`
--

INSERT INTO `shift_type` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `name`, `status`) VALUES
(1, 'chrissd', '2022-03-15 12:36:13.111631', NULL, '2022-05-23 07:33:07.110237', 2, 'Standard ', 'ACTIVE'),
(4, 'chrissd', '2022-03-17 12:44:38.543219', NULL, '2022-03-17 12:44:38.543219', 0, 'Standard Night', 'ACTIVE'),
(5, 'chrissd', '2022-03-17 12:44:46.729157', NULL, '2022-03-17 12:44:46.729157', 0, 'Sleep-in', 'ACTIVE'),
(6, 'chrissd', '2022-04-07 16:24:46.222002', NULL, '2022-04-07 16:24:46.222002', 0, 'Bank Holiday', 'ACTIVE');

-- --------------------------------------------------------

--
-- Table structure for table `tax_code`
--

CREATE TABLE `tax_code` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `code` varchar(255) DEFAULT NULL,
  `deduction` decimal(19,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `tax_code`
--

INSERT INTO `tax_code` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `code`, `deduction`) VALUES
(1, 'system', '2022-04-10 21:06:44.071490', NULL, '2022-04-10 21:06:44.071490', 0, '2', '1.00');

-- --------------------------------------------------------

--
-- Table structure for table `vat_rate`
--

CREATE TABLE `vat_rate` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `vat_rate` decimal(19,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `worker`
--

CREATE TABLE `worker` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(50) NOT NULL,
  `created_date` datetime(6) DEFAULT NULL,
  `last_modified_by` varchar(50) DEFAULT NULL,
  `last_modified_date` datetime(6) DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `firstname` varchar(255) DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `phone_number` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `assignment_code_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `worker`
--

INSERT INTO `worker` (`id`, `created_by`, `created_date`, `last_modified_by`, `last_modified_date`, `version`, `email`, `firstname`, `gender`, `lastname`, `phone_number`, `status`, `username`, `assignment_code_id`) VALUES
(37, '<EMAIL>', '2022-05-10 13:40:06.601627', NULL, '2022-05-10 13:40:06.601627', 0, '<EMAIL>', 'Worker', 'MALE', 'Worker', '0717788', 'ACTIVE', 'Worker', 1),
(38, '<EMAIL>', '2022-05-10 13:57:44.689441', NULL, '2022-05-10 13:57:44.689441', 0, '<EMAIL>', 'Nurse', 'FEMALE', 'Nurser', '09787722', 'ACTIVE', 'Nurse', 3),
(39, '<EMAIL>', '2022-05-10 14:42:25.722226', NULL, '2022-05-10 14:42:25.722226', 0, '<EMAIL>', 'TestW', 'FEMALE', 'WorkerT', '0978733', 'ACTIVE', 'TestWorker', 1),
(40, '<EMAIL>', '2022-05-10 14:43:37.766497', NULL, '2022-05-10 14:43:37.766497', 0, '<EMAIL>', 'Tatenda', 'FEMALE', 'WorkerT', '0978733', 'ACTIVE', 'Tatenda', 1),
(41, '<EMAIL>', '2022-05-11 13:57:24.354021', NULL, '2022-05-11 13:57:24.354021', 0, '<EMAIL>', 'Worker1', 'FEMALE', 'Worker1', '0867661', 'ACTIVE', 'Worker1', 1),
(42, '<EMAIL>', '2022-05-11 13:58:33.223038', NULL, '2022-05-11 13:58:33.223038', 0, '<EMAIL>', 'Worker27', 'MALE', 'Worker27', '0909909', 'ACTIVE', 'Worker27', 1),
(43, '<EMAIL>', '2022-05-11 14:00:10.750838', NULL, '2022-05-11 14:00:10.750838', 0, '<EMAIL>', 'Worker28', 'FEMALE', 'Worker28', '0872272', 'ACTIVE', 'Worker28', 1),
(44, '<EMAIL>', '2022-05-11 14:01:11.507891', NULL, '2022-05-11 14:01:11.507891', 0, '<EMAIL>', 'Worker270', 'MALE', 'Worker270', '078989', 'ACTIVE', 'Worker270', 1),
(45, '<EMAIL>', '2022-05-23 08:09:31.300844', NULL, '2022-05-23 08:09:31.300844', 0, '<EMAIL>', 'CleanWorker', 'FEMALE', 'CleanW', '0828222', 'ACTIVE', 'CleanWorker', 4);

-- --------------------------------------------------------

--
-- Table structure for table `worker_applied_shift`
--

CREATE TABLE `worker_applied_shift` (
  `id` bigint(20) NOT NULL,
  `applied_date` date DEFAULT NULL,
  `shift_id` bigint(20) DEFAULT NULL,
  `shift_status` varchar(255) DEFAULT NULL,
  `worker_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `worker_applied_shift`
--

INSERT INTO `worker_applied_shift` (`id`, `applied_date`, `shift_id`, `shift_status`, `worker_id`) VALUES
(7, '2022-05-10', 356, 'APPLIED', 37),
(8, '2022-05-10', 357, 'APPLIED', 37),
(11, '2022-05-10', 359, 'APPLIED', 37),
(12, '2022-05-10', 360, 'APPLIED', 37),
(13, '2022-05-10', 358, 'APPLIED', 37),
(14, '2022-05-18', 525, 'APPLIED', 37),
(15, '2022-05-18', 526, 'APPLIED', 37),
(18, '2022-05-24', 558, 'APPLIED', 45),
(19, '2022-05-24', 556, 'APPLIED', 45),
(22, '2022-05-26', 564, 'APPLIED', 45);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `agency`
--
ALTER TABLE `agency`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_83gf327dym4y446171hk5q670` (`email`),
  ADD UNIQUE KEY `UK_6m6c3mscbn3eohv8sslxlp12c` (`name`),
  ADD KEY `FKhf6ayr0ox4mc56vvn8r5dmxqc` (`service_id`);

--
-- Indexes for table `agency_bill`
--
ALTER TABLE `agency_bill`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UKnapmhxi4cdnyfyws5csm1pkmc` (`shift_id`),
  ADD KEY `FKhgwtkr5hwyi8wxhuj75t17442` (`agency_id`),
  ADD KEY `FKfqyc7t3dumfgp5hisdn8bd0tm` (`client_id`),
  ADD KEY `FKi80nymyof8yam4ul7wt5n0coc` (`worker_id`);

--
-- Indexes for table `agency_client`
--
ALTER TABLE `agency_client`
  ADD PRIMARY KEY (`agency_id`,`client_id`),
  ADD KEY `FK5h863llknan060n9x82hgr24h` (`client_id`);

--
-- Indexes for table `agency_worker`
--
ALTER TABLE `agency_worker`
  ADD PRIMARY KEY (`agency_id`,`worker_id`),
  ADD KEY `FK4mglt3p0e2xxk91926k8sodc3` (`worker_id`);

--
-- Indexes for table `assignment_code`
--
ALTER TABLE `assignment_code`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_assignment_code_agency` (`name`,`code`),
  ADD UNIQUE KEY `UK_m5qwy0tr4coxiwc5go3smyhfh` (`code`),
  ADD UNIQUE KEY `UK_kh72j7xmph1bm5xrb30t5mdjm` (`name`),
  ADD KEY `FKnligo0j0aawfk6a5jvuvtdxql` (`services_id`);

--
-- Indexes for table `assignment_rate`
--
ALTER TABLE `assignment_rate`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `charge_rate`
--
ALTER TABLE `charge_rate`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `client`
--
ALTER TABLE `client`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_svvrhud9obm7shbvkequh5ki7` (`billing_email`),
  ADD UNIQUE KEY `UK_bfgjs3fem0hmjhvih80158x29` (`email`),
  ADD UNIQUE KEY `UK_dn5jasds5r1j3ewo5k3nhwkkq` (`name`),
  ADD KEY `FK7r5eyumvihwojq7iltcm4nqa6` (`service_id`);

--
-- Indexes for table `expense_rate`
--
ALTER TABLE `expense_rate`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `invoice`
--
ALTER TABLE `invoice`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `invoice_item`
--
ALTER TABLE `invoice_item`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FKbu6tmpd0mtgu9wrw5bj5uv09v` (`invoice_id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_h4rqgjwnqidx6mvj4i22dxwxe` (`name`),
  ADD KEY `FKpelv8bsy9ud4v7plow3ofbdvd` (`agency_id`);

--
-- Indexes for table `shift`
--
ALTER TABLE `shift`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK30kk2q7f6psdf9878et4ipf9t` (`agent_id`),
  ADD KEY `FKla5xjd32yvkjd21f6280i2jln` (`assignment_code_id`),
  ADD KEY `FK48ng4faqpd9j94d0wprutjlt5` (`client_id`),
  ADD KEY `FK5fcg0n615wipggrqc5uu0f9ns` (`directorate_id`),
  ADD KEY `FK9exbyhm3yeo9p64nbf5ghipcy` (`shift_type_id`),
  ADD KEY `FK98xauy1ryyhp5hce3jdc45602` (`worker_id`);

--
-- Indexes for table `shift_agency`
--
ALTER TABLE `shift_agency`
  ADD PRIMARY KEY (`shift_id`,`agency_id`),
  ADD KEY `FKpm2v6ue8hlx4toy0d3d38e2bw` (`agency_id`);

--
-- Indexes for table `shift_directorate`
--
ALTER TABLE `shift_directorate`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_directorate_location` (`name`,`location_id`),
  ADD KEY `FKkbnvao96vxsninumcoohu66un` (`location_id`);

--
-- Indexes for table `shift_location`
--
ALTER TABLE `shift_location`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_e659v5vxk0ebwy7pa3anphm3p` (`name`),
  ADD KEY `FKd4j3dlbnay4ntr1xn2eoil2g` (`client_id`);

--
-- Indexes for table `shift_type`
--
ALTER TABLE `shift_type`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_mry2hufoeidq19ihhkjhegqdq` (`name`);

--
-- Indexes for table `tax_code`
--
ALTER TABLE `tax_code`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_m2a26s070jqyvdr5bw64pe7qd` (`code`);

--
-- Indexes for table `vat_rate`
--
ALTER TABLE `vat_rate`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `worker`
--
ALTER TABLE `worker`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_fg9c0arm4uk7iobap0j2smull` (`email`),
  ADD UNIQUE KEY `UK_1pracyjlfmis4kre9cb740etf` (`username`),
  ADD KEY `FK5cv09cjrc7udng3y611yhnhvp` (`assignment_code_id`);

--
-- Indexes for table `worker_applied_shift`
--
ALTER TABLE `worker_applied_shift`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_id_account_type` (`worker_id`,`shift_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `agency`
--
ALTER TABLE `agency`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `agency_bill`
--
ALTER TABLE `agency_bill`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `assignment_code`
--
ALTER TABLE `assignment_code`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `assignment_rate`
--
ALTER TABLE `assignment_rate`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=294;

--
-- AUTO_INCREMENT for table `charge_rate`
--
ALTER TABLE `charge_rate`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `client`
--
ALTER TABLE `client`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `expense_rate`
--
ALTER TABLE `expense_rate`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `invoice`
--
ALTER TABLE `invoice`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=58;

--
-- AUTO_INCREMENT for table `invoice_item`
--
ALTER TABLE `invoice_item`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=91;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `shift`
--
ALTER TABLE `shift`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=571;

--
-- AUTO_INCREMENT for table `shift_directorate`
--
ALTER TABLE `shift_directorate`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=65;

--
-- AUTO_INCREMENT for table `shift_location`
--
ALTER TABLE `shift_location`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=53;

--
-- AUTO_INCREMENT for table `shift_type`
--
ALTER TABLE `shift_type`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `tax_code`
--
ALTER TABLE `tax_code`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `vat_rate`
--
ALTER TABLE `vat_rate`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `worker`
--
ALTER TABLE `worker`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=46;

--
-- AUTO_INCREMENT for table `worker_applied_shift`
--
ALTER TABLE `worker_applied_shift`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `agency`
--
ALTER TABLE `agency`
  ADD CONSTRAINT `FKhf6ayr0ox4mc56vvn8r5dmxqc` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`);

--
-- Constraints for table `agency_bill`
--
ALTER TABLE `agency_bill`
  ADD CONSTRAINT `FKa7jv9gxw6xyq8lw9fsyb9fiwj` FOREIGN KEY (`shift_id`) REFERENCES `shift` (`id`),
  ADD CONSTRAINT `FKfqyc7t3dumfgp5hisdn8bd0tm` FOREIGN KEY (`client_id`) REFERENCES `client` (`id`),
  ADD CONSTRAINT `FKhgwtkr5hwyi8wxhuj75t17442` FOREIGN KEY (`agency_id`) REFERENCES `agency` (`id`),
  ADD CONSTRAINT `FKi80nymyof8yam4ul7wt5n0coc` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`);

--
-- Constraints for table `agency_client`
--
ALTER TABLE `agency_client`
  ADD CONSTRAINT `FK5h863llknan060n9x82hgr24h` FOREIGN KEY (`client_id`) REFERENCES `client` (`id`),
  ADD CONSTRAINT `FKncbkjs9iokkd4ur6dshnnlsg2` FOREIGN KEY (`agency_id`) REFERENCES `agency` (`id`);

--
-- Constraints for table `agency_worker`
--
ALTER TABLE `agency_worker`
  ADD CONSTRAINT `FK2r0wetme2dnhtfrfc1gk558c5` FOREIGN KEY (`agency_id`) REFERENCES `agency` (`id`),
  ADD CONSTRAINT `FK4mglt3p0e2xxk91926k8sodc3` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`);

--
-- Constraints for table `assignment_code`
--
ALTER TABLE `assignment_code`
  ADD CONSTRAINT `FKnligo0j0aawfk6a5jvuvtdxql` FOREIGN KEY (`services_id`) REFERENCES `services` (`id`);

--
-- Constraints for table `assignment_rate`
--
ALTER TABLE `assignment_rate`
  ADD CONSTRAINT `FK4b0iabll0n9lk12ehg048ooxb` FOREIGN KEY (`assignment_code_id`) REFERENCES `assignment_code` (`id`),
  ADD CONSTRAINT `FK94ttflehrx7acqktsx05l9a1t` FOREIGN KEY (`agent_id`) REFERENCES `agency` (`id`),
  ADD CONSTRAINT `FKcwdc2qi7bh5dj8b5pwp30mq9j` FOREIGN KEY (`client_id`) REFERENCES `client` (`id`),
  ADD CONSTRAINT `FKgbogal4stx1q2no6oa88mrj6q` FOREIGN KEY (`shift_directorate_id`) REFERENCES `shift_directorate` (`id`),
  ADD CONSTRAINT `FKq86ikseyqv2lr42b0qen16l9e` FOREIGN KEY (`shift_location_id`) REFERENCES `shift_location` (`id`),
  ADD CONSTRAINT `FKt2pv0l8msalhphjcbhlkcsd5f` FOREIGN KEY (`shift_type_id`) REFERENCES `shift_type` (`id`);

--
-- Constraints for table `client`
--
ALTER TABLE `client`
  ADD CONSTRAINT `FK7r5eyumvihwojq7iltcm4nqa6` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`);

--
-- Constraints for table `invoice_item`
--
ALTER TABLE `invoice_item`
  ADD CONSTRAINT `FKbu6tmpd0mtgu9wrw5bj5uv09v` FOREIGN KEY (`invoice_id`) REFERENCES `invoice` (`id`);

--
-- Constraints for table `services`
--
ALTER TABLE `services`
  ADD CONSTRAINT `FKpelv8bsy9ud4v7plow3ofbdvd` FOREIGN KEY (`agency_id`) REFERENCES `agency` (`id`);

--
-- Constraints for table `shift`
--
ALTER TABLE `shift`
  ADD CONSTRAINT `FK30kk2q7f6psdf9878et4ipf9t` FOREIGN KEY (`agent_id`) REFERENCES `agency` (`id`),
  ADD CONSTRAINT `FK48ng4faqpd9j94d0wprutjlt5` FOREIGN KEY (`client_id`) REFERENCES `client` (`id`),
  ADD CONSTRAINT `FK5fcg0n615wipggrqc5uu0f9ns` FOREIGN KEY (`directorate_id`) REFERENCES `shift_directorate` (`id`),
  ADD CONSTRAINT `FK98xauy1ryyhp5hce3jdc45602` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`),
  ADD CONSTRAINT `FK9exbyhm3yeo9p64nbf5ghipcy` FOREIGN KEY (`shift_type_id`) REFERENCES `shift_type` (`id`),
  ADD CONSTRAINT `FKla5xjd32yvkjd21f6280i2jln` FOREIGN KEY (`assignment_code_id`) REFERENCES `assignment_code` (`id`);

--
-- Constraints for table `shift_agency`
--
ALTER TABLE `shift_agency`
  ADD CONSTRAINT `FKll7yw5scqwx8t65uyjmi6qu47` FOREIGN KEY (`shift_id`) REFERENCES `shift` (`id`),
  ADD CONSTRAINT `FKpm2v6ue8hlx4toy0d3d38e2bw` FOREIGN KEY (`agency_id`) REFERENCES `agency` (`id`);

--
-- Constraints for table `shift_directorate`
--
ALTER TABLE `shift_directorate`
  ADD CONSTRAINT `FKkbnvao96vxsninumcoohu66un` FOREIGN KEY (`location_id`) REFERENCES `shift_location` (`id`);

--
-- Constraints for table `shift_location`
--
ALTER TABLE `shift_location`
  ADD CONSTRAINT `FKd4j3dlbnay4ntr1xn2eoil2g` FOREIGN KEY (`client_id`) REFERENCES `client` (`id`);

--
-- Constraints for table `worker`
--
ALTER TABLE `worker`
  ADD CONSTRAINT `FK5cv09cjrc7udng3y611yhnhvp` FOREIGN KEY (`assignment_code_id`) REFERENCES `assignment_code` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
