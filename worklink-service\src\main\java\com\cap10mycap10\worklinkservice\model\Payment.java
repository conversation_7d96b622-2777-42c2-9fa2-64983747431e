package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.InvoiceStatus;
import com.cap10mycap10.worklinkservice.enums.PaymentType;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.stripe.model.Charge;
import com.stripe.model.checkout.Session;
import lombok.*;
import zw.co.paynow.responses.StatusResponse;


import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Objects;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonSerialize
public class Payment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String ref;

    @Enumerated(EnumType.STRING)
    private InvoiceStatus status;

    @Enumerated(EnumType.STRING)
    private PaymentType paymentType;

    @Column(nullable = false)
    private BigDecimal total;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mmXXX")
    private ZonedDateTime paymentDate = ZonedDateTime.now();


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Invoice invoice;


    public Payment(StatusResponse status,  BigDecimal amount) {
        if(status.isPaid()) this.status = InvoiceStatus.PAID;
        else throw new BusinessValidationException("Payment was not successful");
        this.paymentType = PaymentType.PAYNOW;
        ref = status.pollUrl();
        total = amount;

    }


    public Payment(Charge checkoutSession, BigDecimal total) {
        if(Objects.equals(checkoutSession.getStatus(), "succeeded")) this.status = InvoiceStatus.PAID;
        else throw new BusinessValidationException("Payment was not successful");
        ref = checkoutSession.getReceiptUrl();
        paymentType = PaymentType.STRIPE;
        this.total = total;
    }


    public Payment(Double paidAmount, String manualPayment) {
        this.status = InvoiceStatus.PAID;
        this.paymentType = PaymentType.MANUAL;
        total = BigDecimal.valueOf(paidAmount);
    }
}
