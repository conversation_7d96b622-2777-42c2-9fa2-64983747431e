package com.cap10mycap10.worklinkservice.dto.transport;

import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.vehiclelog.VehicleLogDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.enums.DecisionEnum;
import com.cap10mycap10.worklinkservice.enums.Gender;
import com.cap10mycap10.worklinkservice.enums.Level;
import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Getter
@Setter
public class TransportDto {
    private Long Id;
    private Long clientId;
    private String clientName;
    private String agencyName;
    private TransportStatus transportStatus;
    private Float breakTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime start;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime end;
    private Set<Long> transportLegibleAgencyIds;
    private Long agencyId;
    private Long pickupDirectorateId;
    private String pickupLocationContactNumber;
    private String destination;
    private Level selfNeglect;
    private String selfNeglectDesc;
    private String assaultStaffDesc;
    private String selfHarmDesc;
    private String physicalAggressionDesc;
    private String mentalHealthStatus;
    private String verballyAggressiveDesc;
    private String absconsionRiskDesc;
    private String genderIssuesDesc;
    private String racialIssuesDesc;
    private String sexuallyInappropriateDesc;
    private String destinationPostCode;
    private String destinationContactNumber;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime dateTimeRequired;
    private Integer passengerAge;
    private Long totalMinutes;
    @Enumerated(EnumType.STRING)
    private Gender passengerGender;
    private String passengerAdditionalRisks;
    @Column(length = 500)
    private String riskDoc;
    private MultipartFile riskDocFile;
    private String patientDocumentOne;
    private MultipartFile multipartPatientDocumentOne;
    private String patientDocumentTwo;
    private MultipartFile multipartPatientDocumentTwo;
    private String patientDocumentThree;
    private String patientName;
    private MultipartFile multipartPatientDocumentThree;
    private String reasonForTransport;
    //    Current Risks
    private Level assaultStaff;
    private Level physicalAggression;
    private Level verballyAggressive;
    private Level selfHarm;
    private Level absconsionRisk;
    private Level sexuallyInappropriate;
    private String otherRisks;
    private String mobilityIssues;
    private String passengerWalkInfo;
    private String otherMobilityIssues;
    //    Escort service required
    private String escortServiceRisk;
    private Boolean isPassengerAwareOfTransport;
    private Boolean passengerRequiresRestraints;
    private String reasonsForRestrains;
    private String specialRequests;
    List<TransportWorkerSpecDto> transportWorkerSpecList;
    private String patientDocuments;
    private String mha;
    private String pcaddress;
    private String pcemail;
    private String pcbusiness;
    private String pward;
    private String pname;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate pdob;
    private String nhs;
    private String diagnosis;
    private String dname;
    private String dbusiness;
    private String dward;
    private String dcontact;
    private String demail;
    private String refPrefix;
    private Level genderIssues;
    private Level racialIssues;
    private String medication;
    private String physicalHealth;
    private String rapidTranq;
    private String infectionControl;
    private Boolean covid;
    private Integer wardEscort;
    private String offerFood;
    private String allergies;
    private String submittedBy;
    private String bpostCode;
    private String pickupPostCode;
    private String semail;
    private Boolean canOfferFood;
    private String sphone;
    private DecisionEnum rapidStatus;
    private String pOrderNum;
    private String sbsCode;
    private String bname;
    private String baddress;
    private String binvoice;
    private String bphone;
    private String pmeds;
    private String bemail;
    private String authority;
    private Boolean walk;
    private Float mileage;
    private String walkInfo;
    private VehicleDto vehicle;
    private WorkerResultDto teamLeader;
    private WorkerResultDto driver;
    private String createdBy;


    private String startMileage;
    private String endMileage;
    private VehicleLogDto vehicleLog;


    private String lastModifiedBy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime lastModifiedDate;

    private List<String> propertyList;
    private List<String> medicationList;
    private Float cashHandover;
    private Boolean pDroppedOff;
    private String pComment;
    private String signature;

    private int pfCleanliness;
    private int pfCourtesy;
    private int pfKnowledge;
    private int pfTreatment;
    private int pfAdvice;
    private int pfComfort;
    private int pfExperience;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm")
    private LocalDateTime dropTime;
            private String patientRecipient;
    private String recipientContact;
            private String recipientRole;
    private String recipientSignature;
            private String newAddress;
    private String newPostCode;
            private String newPhone;
    private String newEmail;


}
