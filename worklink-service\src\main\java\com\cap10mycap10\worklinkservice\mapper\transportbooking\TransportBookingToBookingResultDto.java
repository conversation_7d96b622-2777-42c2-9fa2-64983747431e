//package com.cap10mycap10.worklinkservice.mapper.transportbooking;
//
//import com.cap10mycap10.worklinkservice.dao.AssignmentCodeRepository;
//import com.cap10mycap10.worklinkservice.dao.WorkerRepository;
//import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
//import com.cap10mycap10.worklinkservice.enums.BookingType;
//import com.cap10mycap10.worklinkservice.model.TransportBooking;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.function.Function;
//
//import static java.util.Objects.nonNull;
//
//@Service
//@Slf4j
//public class TransportBookingToBookingResultDto implements Function<TransportBooking, BookingResultDto> {
//    @Autowired
//    private WorkerRepository workerRepository;
//
//    @Autowired
//    private AssignmentCodeRepository assignmentCodeRepository;
//
//    @Override
//    public BookingResultDto apply(TransportBooking shift) {
//
//        BookingResultDto bookingResultDto = new BookingResultDto();
//
//        bookingResultDto.setId(shift.getId());
//        bookingResultDto.setShiftStatus(shift.getStatus().toString());
//        bookingResultDto.setBookingType(BookingType.TRANSPORT);
//        bookingResultDto.setWorkerSpecId(shift.getWorkerSpec().getId());
//        bookingResultDto.setWorkerId(shift.getWorker().getId());
//
//        if(nonNull(shift.getStart()))
//            bookingResultDto.setStart(shift.getStart().toString());
//        else if(nonNull(shift.getDateTimeBooked()))
//            bookingResultDto.setStart(shift.getDateTimeBooked().toString());
//        if(nonNull(shift.getEnd()))
//           bookingResultDto.setEnd(shift.getEnd().toString());
//
//        bookingResultDto.setWorker(shift.getWorker().getFirstname() + " " + shift.getWorker().getLastname());
//
//        return bookingResultDto;
//    }
//}
