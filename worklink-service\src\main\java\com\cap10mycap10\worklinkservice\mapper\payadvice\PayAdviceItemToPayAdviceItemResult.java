package com.cap10mycap10.worklinkservice.mapper.payadvice;

import com.cap10mycap10.worklinkservice.dto.payadvice.PayAdviceItemResult;
import com.cap10mycap10.worklinkservice.model.PayAdviceItem;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class PayAdviceItemToPayAdviceItemResult implements Converter<List<PayAdviceItem>, List<PayAdviceItemResult>> {
    @Override
    public List<PayAdviceItemResult> convert(List<PayAdviceItem> payAdviceItem) {
        List<PayAdviceItemResult> payAdviceItemResults = new ArrayList<>();
        for (PayAdviceItem item:payAdviceItem
        ) {
            PayAdviceItemResult payAdviceItemResult = new PayAdviceItemResult();
            payAdviceItemResult.setEndDate(item.getEndDate());
            payAdviceItemResult.setStartDate(item.getStartDate());
            payAdviceItemResult.setDayOfTheWeek(item.getDayOfTheWeek());
            payAdviceItemResult.setId(item.getId());
            payAdviceItemResult.setDirectorate(item.getDirectorate());
            payAdviceItemResult.setShiftId(item.getShiftId());
            payAdviceItemResult.setRate(item.getRate());
            payAdviceItemResult.setEndTime(item.getEndTime());
            payAdviceItemResult.setStartTime(item.getStartTime());
            payAdviceItemResult.setNumberOfHoursWorked(item.getNumberOfHoursWorked());
            payAdviceItemResult.setTotal(item.getTotal());
            payAdviceItemResults.add(payAdviceItemResult);
        }
        return payAdviceItemResults;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
