package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.trainingfeedback.TrainingFeedbacksRes;
import com.cap10mycap10.worklinkservice.model.TrainingFeedback;
import com.cap10mycap10.worklinkservice.service.TrainingFeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;


@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
@Transactional(rollbackFor = Exception.class)
public class TrainingFeedbackController {
    @Autowired
    private  TrainingFeedbackService trainingFeedbackService;


    @PostMapping(value = "training-feedback/{bookingId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TrainingFeedback> create(
            @PathVariable( value = "bookingId")  Long bookingId,
            @RequestBody TrainingFeedback trainingFeedback) {
        log.info("Request to add trainingFeedback with : {}", trainingFeedback);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.created(uri)
                .body(trainingFeedbackService.save(trainingFeedback, bookingId));
    }

    @GetMapping(value = "training-feedback/{id}")
    public ResponseEntity<TrainingFeedback> findById(@PathVariable("id") Long id) {
        log.info("Request to get trainingFeedback with id : {}", id);
        return ResponseEntity.ok(trainingFeedbackService.findById(id));
    }



    @GetMapping(value = "training-feedback/booking/{id}")
    @Transactional
    public ResponseEntity<TrainingFeedbacksRes> findByBooking(@PathVariable("id") Long id) {
        log.info("Request to get trainingFeedback with id : {}", id);
        return ResponseEntity.ok(trainingFeedbackService.findBySession(id));
    }

    @DeleteMapping(value = "training-feedback/{id}")
    public ResponseEntity<Object> delete(@PathVariable("id") Long id) {
        trainingFeedbackService.deleteById(id);
        return ResponseEntity.noContent().build();
    }
}
