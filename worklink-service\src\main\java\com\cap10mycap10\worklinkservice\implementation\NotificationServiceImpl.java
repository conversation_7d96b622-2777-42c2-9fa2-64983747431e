package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.NotificationRepository;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
//import com.cap10mycap10.worklinkservice.dto.notification.NotificationUpdateDto;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationResultDto;
import com.cap10mycap10.worklinkservice.enums.WorklinkUserType;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.mapper.notification.NotificationToNotificationResultDto;
import com.cap10mycap10.worklinkservice.model.Notification;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ClientService;
import com.cap10mycap10.worklinkservice.service.NotificationService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NotificationServiceImpl implements NotificationService {

    private final WorkerService workerService;

    @Autowired
    private AgencyService agencyService;
    @Autowired
    private ClientService clientService;

    private final NotificationToNotificationResultDto toNotificationResultDto;
    private final NotificationRepository notificationRepository;

//    private final NotificationToNotificationResultDto toAnotificationToNotificationResultDto;

    public NotificationServiceImpl(WorkerService workerService, NotificationToNotificationResultDto toNotificationResultDto, NotificationRepository notificationRepository) {
        this.workerService = workerService;
        this.toNotificationResultDto = toNotificationResultDto;
        this.notificationRepository = notificationRepository;
    }

    @Override
    public void addWorkerNotification(NotificationCreateDto notificationCreateDto) {
        Notification notification = new Notification();
        notification.setBody(notificationCreateDto.getBody());
        notification.setTitle(notificationCreateDto.getTitle());
        notification.setToken(notificationCreateDto.getToken());
        notification.setWorker(workerService.getOne(notificationCreateDto.getWorkerId()));
        notificationRepository.save(notification);
    }

    @Override
    public void saveNotification(NotificationCreateDto notificationCreateDto) {
        Notification notification = new Notification();

        notification.setBody(notificationCreateDto.getBody());
        notification.setTitle(notificationCreateDto.getTitle());
        if(notificationCreateDto.getSendingAgencyId()!=null&&notificationCreateDto.getSendingAgencyId()!= 0L)notification.setSenderAgency(agencyService.getOne(notificationCreateDto.getSendingAgencyId()));
        if(notificationCreateDto.getSendingClientId()!=null&&notificationCreateDto.getSendingClientId()!= 0L)notification.setSenderClient(clientService.getOne(notificationCreateDto.getSendingClientId()));
        notification.setSenderType(notificationCreateDto.getSenderType());
        notification.setSendToAll(notificationCreateDto.getSendToAll());
        notification.setRecipientType(notificationCreateDto.getSendToType());

        notificationRepository.save(notification);
    }

    @Override
    public void deleteNotification(Long id) {

        Notification notification = notificationRepository.findById(id).orElseThrow(() -> new RecordNotFoundException("Notification not found"));


            notificationRepository.delete(notification);
            notificationRepository.flush();


    }

    @Override
    public Notification findById(Long id) {
        return null;
    }

    @Override
    @Transactional
    public List<NotificationResultDto> findWorkerNotifications(Long workerId, PageRequest of) {

        List<Notification> notifications2 = new ArrayList<>();


        log.info("Getting worker notifications.");
        Worker worker = workerService.getOne(workerId);
        Set<Notification> notifications = worker.getNotifications();

        worker.getAgencySet().forEach( a -> {
            notifications2.addAll(notificationRepository.findAllBySenderAgencyOrderByCreatedDateDesc(a));
        });

        notifications2.addAll(notificationRepository.findAllBySenderTypeAndSendToAllOrderByIdDesc(WorklinkUserType.ADMIN, true));
        notifications2.addAll(notificationRepository.findAllBySenderTypeAndRecipientAssCodeOrderByIdDesc( WorklinkUserType.ADMIN, worker.getAssignmentCode()));


        notifications.removeAll(Collections.singleton(null));

        ArrayList<Notification> list = new ArrayList<>(notifications);

        list.addAll(notifications2);


        list.removeAll(Collections.singleton(null));

        List<NotificationResultDto> res = list
                .stream()
                .map(toNotificationResultDto::convert)
                .collect(Collectors.toList());

        res.removeAll(Collections.singleton(null));
        Collections.sort(res, Comparator.comparing(x -> x.getId(),Comparator.reverseOrder()));


        return res;


    }


    @Override
    public Page<Notification> findAllPaged(PageRequest of) {
        return null;
    }

//    @Override
//    public Notification save(NotificationUpdateDto notificationUpdateDto) {
//        return null;
//    }

    @Override
    public Notification getOne(Long id) {
        return null;
    }
}
