package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.device.DeviceWorkerUpdateDto;
import com.cap10mycap10.worklinkservice.model.Device;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.Set;

public interface DeviceService {
    void save(DeviceWorkerUpdateDto deviceWorkerUpdateDto) throws JsonProcessingException;

    Set<Device> getWorkerDevices(Long workerId);

    void delete(Device device);
}
