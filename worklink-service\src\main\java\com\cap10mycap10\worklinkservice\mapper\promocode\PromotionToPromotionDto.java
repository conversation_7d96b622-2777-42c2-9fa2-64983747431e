package com.cap10mycap10.worklinkservice.mapper.promocode;

import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.PromotionType;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
import com.cap10mycap10.worklinkservice.mapper.vehiclefilter.VehicleFilterToVehicleFilterDto;
import com.cap10mycap10.worklinkservice.model.Promotion;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static java.util.Objects.nonNull;

@Component
@Slf4j
public class PromotionToPromotionDto implements Converter<Promotion, PromotionDto> {

    @Autowired
    private AgencyToAgencyResultDto toAgencyResultDto;
    @Autowired
    private VehicleFilterToVehicleFilterDto toVehicleFilterDto;

    @Override
    public PromotionDto convert(Promotion promotion) {
        PromotionDto promotionDto = new PromotionDto();

        promotionDto.setId(promotion.getId());
        promotionDto.setCreatedDate(promotion.getCreatedDate());
        promotionDto.setCode(promotion.getCode());
        promotionDto.setDescription(promotion.getDescription());

        promotionDto.setExpiryDate(promotion.getExpiryDate());
        promotionDto.setDiscount(promotion.getDiscount());
        promotionDto.setUsageLimit(promotion.getUsageLimit());

        promotionDto.setStatus(promotion.getStatus());

        promotionDto.setExtraDays(promotion.getExtraDays());
        promotionDto.setExtraMileage(promotion.getExtraMileage());
        promotionDto.setPromotionType(promotion.getPromotionType());


        promotionDto.setCancelledDate(promotion.getCancelledDate());
        promotionDto.setActivatedDate(promotion.getActivatedDate());
        promotionDto.setAdminDiscount(promotion.getAdminDiscount());
        if(nonNull(promotion.getAgency())) promotionDto.setAgency(toAgencyResultDto.convert(promotion.getAgency()));
        promotionDto.setUsageCount(promotion.getUsageCount());
        promotionDto.setStartDate(promotion.getStartDate());

        promotionDto.setDaysHired(promotion.getDaysHired());
//        promotionDto.setExtraMileage(promotion.getExtraMileage());
//        promotionDto.setExtraDays(promotion.getExtraDays());
        promotionDto.setTitle(promotion.getTitle());
        promotionDto.setHtmlBody(promotion.getHtmlBody());

        promotionDto.setVehicleFilter(toVehicleFilterDto.convert(promotion.getVehicleFilter()));

        return promotionDto;
    }


    @Override
    public JavaType getInputType(TypeFactory typeFactory){
        return typeFactory.constructType(Promotion.class);
    }
    @Override
    public JavaType getOutputType(TypeFactory typeFactory){
        return typeFactory.constructType(PromotionDto.class);
    }
}
