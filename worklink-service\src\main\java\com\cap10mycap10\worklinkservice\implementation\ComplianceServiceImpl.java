package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.ComplianceRepository;
import com.cap10mycap10.worklinkservice.dto.compliance.ComplianceCreateDto;
import com.cap10mycap10.worklinkservice.dto.compliance.ComplianceUpdateDto;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.Compliance;
import com.cap10mycap10.worklinkservice.service.ServicesService;
import com.cap10mycap10.worklinkservice.service.ComplianceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ComplianceServiceImpl implements ComplianceService {

    private final ComplianceRepository complianceRepository;

    private final ServicesService servicesService;

    public ComplianceServiceImpl(ComplianceRepository complianceRepository, ServicesService servicesService) {
        this.complianceRepository = complianceRepository;
        this.servicesService = servicesService;
    }

    @Override
    public void addCompliance(ComplianceCreateDto complianceCreateDto) {
        Compliance compliance = new Compliance();
        compliance.setName(complianceCreateDto.getName());
        compliance.setDescription(complianceCreateDto.getDescription());
        compliance.setCode(complianceCreateDto.getCode());
        compliance.setServices(servicesService.getOne(complianceCreateDto.getServiceId()));
        complianceRepository.save(compliance);

    }

    @Override
    public void deleteCompliance(Long id) {
        Compliance compliance = getOne(id);
        complianceRepository.delete(compliance);
    }

    @Override
    public Compliance findById(Long id) {
        return getOne(id);
    }

    @Override
    public List<Compliance> findAll() {
        return complianceRepository.findAllByOrderByName();
    }


    @Override
    public Page<Compliance> findAllPaged(PageRequest of) {
        return complianceRepository.findAll(of);
    }

    @Override
    public Compliance save(ComplianceUpdateDto complianceUpdateDto) {
        Compliance compliance = getOne(complianceUpdateDto.getComplianceId());

        compliance.setName(complianceUpdateDto.getName());
        compliance.setDescription(complianceUpdateDto.getDescription());
        compliance.setServices(servicesService.getOne(complianceUpdateDto.getServiceId()));
        compliance.setCode(complianceUpdateDto.getCode());
        complianceRepository.save(compliance);
        return compliance;
    }

    @Override
    public Compliance getOne(Long id) {

        return complianceRepository.findById(id).orElseThrow(
                () -> new RecordNotFoundException("Compliance was not found")
        );
    }
}
