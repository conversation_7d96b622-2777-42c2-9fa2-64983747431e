package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.agencyexpenses.AgencyExpenseRateDto;
import com.cap10mycap10.worklinkservice.model.AgencyExpenseRate;

import java.util.List;

public interface AgencyExpenseRateService {


    void addAgencyExpenseRate(AgencyExpenseRateDto expenseRateDto);

    List<AgencyExpenseRate> findAgencyRates(Long agencyId);

    AgencyExpenseRate save(AgencyExpenseRateDto agencyExpenseRateDto);

    AgencyExpenseRate getOne(Long id);
}
