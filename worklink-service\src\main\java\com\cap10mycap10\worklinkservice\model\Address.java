package com.cap10mycap10.worklinkservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Embeddable;

@Embeddable
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Address {

    private String firstLine;
    private String secondLine;
    private String town;
    private String county;
    private String country;
    private String postcode;


    public Address(String firslne){
        this.firstLine = firslne;
    }
    @Override
    public String toString() {
        return postcode + ", " + firstLine + ", " + town + ", " + county;
    }

}
