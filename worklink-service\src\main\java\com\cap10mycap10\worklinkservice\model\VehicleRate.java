package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import jakarta.persistence.*;
import java.time.DayOfWeek;

@Entity
@Data
public class VehicleRate extends AbstractAuditingEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(nullable = false)
    private Float rate;
    @Enumerated(EnumType.STRING)
    private WeekDay weekDay;

    @ManyToOne(fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Vehicle vehicle;

    public VehicleRate() {
    }

    public VehicleRate(VehicleRate rate) {
        this.rate = rate.getRate();
        weekDay = rate.getWeekDay();
    }
}
