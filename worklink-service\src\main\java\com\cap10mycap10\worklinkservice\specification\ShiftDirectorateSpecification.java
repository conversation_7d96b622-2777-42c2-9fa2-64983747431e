package com.cap10mycap10.worklinkservice.specification;

import com.cap10mycap10.worklinkservice.model.ShiftDirectorate;
import com.cap10mycap10.worklinkservice.model.Location;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.Join;

public class ShiftDirectorateSpecification {

    private ShiftDirectorateSpecification() {}

    public static Specification<ShiftDirectorate> nameLike(String nameLike) {
        return (root, query, builder) -> builder.like(root.get("name"), "%" + nameLike + "%");
    }
    

    public static Specification<ShiftDirectorate> hasLocation(Long locationId) {
        return (root, query, builder) -> {
            Join<Location,ShiftDirectorate> shiftLocation = root.join("location");
            return builder.equal(shiftLocation.get("id"), locationId);
        };
    }
//
//    // Third request parameter filter: Get hospitals in one of the specified cities
//    public static Specification<ShiftDirectorate> inCity(List<String> cities) {
//        return (root, query, builder) -> root.get("city")
//                                             .in(cities);
//    }
}