-- Migration script to add vehicle_location junction table and migrate existing data
-- This allows vehicles to be assigned to multiple locations

-- Step 1: Create the vehicle_location junction table
CREATE TABLE vehicle_location (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    vehicle_id BIGINT NOT NULL,
    location_id BIGINT NOT NULL,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    priority INT DEFAULT 0,
    notes VARCHAR(500),
    version BIGINT NOT NULL DEFAULT 0,
    created_by VA<PERSON><PERSON><PERSON>(50) NOT NULL DEFAULT 'system',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    last_modified_by VA<PERSON><PERSON><PERSON>(50),
    last_modified_date D<PERSON>ETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_vehicle_location_vehicle FOREIGN KEY (vehicle_id) REFERENCES vehicle(id) ON DELETE CASCADE,
    CONSTRAINT fk_vehicle_location_location FOREIGN KEY (location_id) REFERENCES location(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate vehicle-location assignments
    CONSTRAINT uk_vehicle_location UNIQUE (vehicle_id, location_id),
    
    -- Index for performance
    INDEX idx_vehicle_location_vehicle_id (vehicle_id),
    INDEX idx_vehicle_location_location_id (location_id),
    INDEX idx_vehicle_location_active (active)
);

-- Step 2: Migrate existing vehicle location data to the junction table
-- This will copy all existing vehicle-location relationships to the new table
INSERT INTO vehicle_location (vehicle_id, location_id, active, priority, created_by, created_date)
SELECT 
    v.id as vehicle_id,
    v.location_id as location_id,
    TRUE as active,
    0 as priority,
    'migration' as created_by,
    NOW() as created_date
FROM vehicle v 
WHERE v.location_id IS NOT NULL;

-- Step 3: Add a comment to document the migration
ALTER TABLE vehicle_location COMMENT = 'Junction table for many-to-many relationship between vehicles and locations. Created during migration from single location per vehicle.';

-- Note: The location_id column in the vehicle table will be removed in a separate migration
-- after confirming the new system works correctly. This allows for rollback if needed.
