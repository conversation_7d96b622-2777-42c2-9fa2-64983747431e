package com.cap10mycap10.worklinkservice.dao;

import com.cap10mycap10.worklinkservice.model.SettlementStatement;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;

public interface SettlementStatementRepository extends JpaRepository<SettlementStatement, Long> {

    @Query("SELECT s FROM SettlementStatement s " +
            "WHERE (:carRentalId IS NULL OR s.carRental.id = :carRentalId) " +
            "AND (:createdDateStart IS NULL OR s.settlementDate >= :createdDateStart) " +
            "AND (:createdDateEnd IS NULL OR s.settlementDate <= :createdDateEnd) " +
            "AND (:id IS NULL OR s.id = :id) " +
            "AND (:amount IS NULL OR s.totalAmount = :amount) " +
            "AND (:status IS NULL OR s.status = :status)")
    Page<SettlementStatement> findByFilters(@Param("carRentalId") Long carRentalId,
                                            @Param("createdDateStart") LocalDate createdDateStart,
                                            @Param("createdDateEnd") LocalDate createdDateEnd,
                                            @Param("id") Long id,
                                            @Param("amount") Double amount,
                                            @Param("status") String status,
                                            Pageable pageable);
}


