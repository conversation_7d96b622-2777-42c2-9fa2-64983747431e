package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.workertrainingsession.WorkerTrainingSessionResultsDto;
import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.cap10mycap10.worklinkservice.model.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface WorkerTrainingSessionService {
    WorkerTrainingSession getOne(Long id);
    WorkerTrainingSession findByWorkerAndTrainingSession(Worker worker, TrainingSession trainingSession);


    Training getTraining(Long id);

    Optional<WorkerTrainingSession> checkPending();

    List<Long> findByAgencyIdAndNotAdminBilled(Long agencyId);
    List<BookingResultDto> findWorkerBookings(Long workerId, WorkerTrainingSessionStatus status);

    void save(WorkerTrainingSession workerTrainingSession);
    void showCertificate(Long id, Boolean show);

    @Transactional
    Page<WorkerTrainingSessionResultsDto> findForAgency(Long agencyId, WorkerTrainingSessionStatus trainingStatus, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of);

    @Transactional
    Page<WorkerTrainingSessionResultsDto> findForTrainer(Long agencyId, WorkerTrainingSessionStatus trainingStatus, Long trainingId, LocalDate startDate, LocalDate endDate, PageRequest of);

    String authorizationReminder(Long id);
}
