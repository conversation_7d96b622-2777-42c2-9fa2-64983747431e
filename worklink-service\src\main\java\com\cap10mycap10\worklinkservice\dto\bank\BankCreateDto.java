package com.cap10mycap10.worklinkservice.dto.bank;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class BankCreateDto {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate signDate;
    private String bank;
    private String name;
    private String code;
    private String account;
    private String fullname;
    private String lastModifiedDate;
    private Long workerId;
}
