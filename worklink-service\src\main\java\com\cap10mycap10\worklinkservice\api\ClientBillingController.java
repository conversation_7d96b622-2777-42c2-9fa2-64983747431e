package com.cap10mycap10.worklinkservice.api;

import com.cap10mycap10.worklinkservice.dto.billing.AgencyBillDto;
import com.cap10mycap10.worklinkservice.dto.billing.AgencyBillStatusDto;
import com.cap10mycap10.worklinkservice.dto.billing.ShiftBillDto;
import com.cap10mycap10.worklinkservice.service.AgencyBillingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ClientBillingController {

    private final AgencyBillingService agencyBillingService;

    public ClientBillingController(AgencyBillingService agencyBillingService) {
        this.agencyBillingService = agencyBillingService;
    }

    /* @ViewBill*/
    @GetMapping(value = "/billing/{id}")
    public ResponseEntity<AgencyBillDto> findById(@PathVariable("id") Long id) {
        log.info("Request to get agency with id: {}", id);
        return ResponseEntity.ok(agencyBillingService.findById(id));
    }

    /* @ListAllBills*/
    @GetMapping(value = "billing/{page}/{size}")
    public ResponseEntity<List<AgencyBillDto>> listAllBills(@PathVariable("page") int page,
                                                                    @PathVariable("size") int size,
                                                                    @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                    @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
                                                                    @RequestParam(value = "payeeId", required = false) Long agentId
                                                            ) {
        log.info("Request to get paged charge rates with : {}, {}", page, size);
        return ResponseEntity.ok(agencyBillingService.findAllPaged(PageRequest.of(page, size), agentId, startDate, endDate));
    }

    /* @ListAllBillsByStatus*/
    @PostMapping(value = "billing/pending/{page}/{size}")
    public ResponseEntity<List<AgencyBillDto>> listAllBillsByStatus(@PathVariable("page") int page,
                                                                    @PathVariable("size") int size,
                                                                    @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                    @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
                                                                    @RequestParam(value = "payeeId", required = false) Long agentId) {
        log.info("Request to get paged bills with : {}, {}", page, size);
        return ResponseEntity.ok(agencyBillingService.findAllPendingPaged(PageRequest.of(page, size), agentId, startDate, endDate));
    }

    /* @CreateDebitNote*/
    @PostMapping(value = "billing/debit-note/create")
    public ResponseEntity createDebitNote(@RequestBody ShiftBillDto shiftBillDto
    ) {
        log.info("Request to create debit orders for shifts : {}, {}, {}", shiftBillDto.getShiftIds());
        agencyBillingService.createDebitOrders(shiftBillDto);
        return ResponseEntity.ok(HttpStatus.ACCEPTED);
    }

    /* @SetDebitNotePaidStatus*/
    @PutMapping(value = "billing/debit-note/status")
    public ResponseEntity SetDebitNotePaidStatus(@RequestBody AgencyBillStatusDto agencyBillStatusDto
    ) {
        log.info("Request to change bill status : {}", agencyBillStatusDto.toString());
        return agencyBillingService.SetDebitNotePaidStatus(agencyBillStatusDto);
    }
}
