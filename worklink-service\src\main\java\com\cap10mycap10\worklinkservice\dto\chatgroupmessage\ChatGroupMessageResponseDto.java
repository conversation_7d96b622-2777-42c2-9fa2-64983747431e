package com.cap10mycap10.worklinkservice.dto.chatgroupmessage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatGroupMessageResponseDto {

    private String chatGroupName;

    private Long chatGroupMessageId;

    private String message;

    private String sender;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    private LocalDateTime sentAt;

    private String messageType;
}
