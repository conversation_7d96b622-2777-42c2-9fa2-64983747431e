package com.cap10mycap10.worklinkservice.model;

import lombok.*;

import jakarta.persistence.*;
import java.math.BigDecimal;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class TaxRate extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String name; // e.g., "Standard VAT", "Reduced VAT", "Zero Rate"

    @Column(nullable = false)
    private BigDecimal percentage; // Tax percentage (e.g., 15.00 for 15%)

    @Column(nullable = false)
    private boolean active = true; // Whether this tax rate is currently active

    private String description; // Optional description of the tax rate

    @Column(nullable = false)
    private Long agencyId; // Agency this tax rate belongs to

    // Constructor for creating default tax rates
    public TaxRate(String name, BigDecimal percentage, String description, Long agencyId) {
        this.name = name;
        this.percentage = percentage;
        this.description = description;
        this.agencyId = agencyId;
        this.active = true;
    }
}
