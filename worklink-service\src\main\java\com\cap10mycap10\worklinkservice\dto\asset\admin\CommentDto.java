package com.cap10mycap10.worklinkservice.dto.asset.admin;

import com.cap10mycap10.worklinkservice.enums.AssetStatus;
import com.cap10mycap10.worklinkservice.enums.FuelType;
import com.cap10mycap10.worklinkservice.enums.TransmissionType;
import com.cap10mycap10.worklinkservice.enums.VehicleType;
import com.cap10mycap10.worklinkservice.model.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
public class CommentDto {

    private String comment;

}
