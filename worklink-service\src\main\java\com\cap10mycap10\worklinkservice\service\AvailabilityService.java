package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.dto.availability.*;
import com.cap10mycap10.worklinkservice.dto.availability.AvailabilityCreateDto;
import com.cap10mycap10.worklinkservice.model.Availability;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

public interface AvailabilityService {

    void updateAvailability(AvailabilityCreateDto availabilityCreateDto);

    void deleteAvailability(Long id);

    Availability findById(Long id);

    Page<IAvailabilityResultDto> findWorkerAvailability(Long workerId, PageRequest of);

    Page<Availability> findAllPaged(PageRequest of);

    Availability save(AvailabilityUpdateDto availabilityUpdateDto);

    Availability getOne(Long id);
}
