package com.cap10mycap10.worklinkservice.mapper.invoice;

import com.cap10mycap10.worklinkservice.dao.PaymentRepository;
import com.cap10mycap10.worklinkservice.dto.invoice.InvoiceResult;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
import com.cap10mycap10.worklinkservice.mapper.client.ClientToClientDto;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Invoice;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;

import static java.util.Objects.nonNull;

@Component
public class InvoiceToInvoiceResult implements Converter<Invoice, InvoiceResult> {
    @Autowired
    private  AgencyService agencyService;

    @Autowired
    private WorkerService workerService;

    @Autowired
    private InvoiceItemToInvoiceItemResult toInvoiceItemResult;

    @Autowired
    private AgencyToAgencyResultDto toAgencyResultDto;

    @Autowired
    private PaymentRepository paymentRepository;


    @Autowired
    private ClientToClientDto toClientDto;

    @Override
    public InvoiceResult convert(Invoice invoice) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");

        InvoiceResult invoiceResult = new InvoiceResult();
        invoiceResult.setInvoiceStatus(invoice.getInvoiceStatus().name());
        invoiceResult.setId(invoice.getId());
        invoiceResult.setServiceCharge(invoice.getServiceCharge());
        invoiceResult.setServiceChargeDesc(invoice.getServiceChargeDesc());
        invoiceResult.setVatAmount(invoice.getVatAmount());
        invoiceResult.setVatPercentage(invoice.getVatPercentage());
        if(nonNull(invoice.getAgency()))invoiceResult.setAgencyId(invoice.getAgency().getId());
        invoiceResult.setInvoiceType(invoice.getInvoiceType());
        invoiceResult.setPublished(invoice.getPublished());
        if(nonNull(invoice.getClient()))invoiceResult.setClientId(invoice.getClient().getId());
        if(nonNull(invoice.getClient()))invoiceResult.setClientName(invoice.getClient().getName());
        if(nonNull(invoice.getClient()))invoiceResult.setClient(toClientDto.convert(invoice.getClient()));
        if(nonNull(invoice.getVehicleBooking()))invoiceResult.setVehicleBookingId(invoice.getVehicleBooking().getId());
        invoiceResult.setPayeeId(invoice.getPayeeId());
        invoiceResult.setWorkerId(invoice.getWorkerId());
//        invoiceResult.setAgencyName(agencyService.getOne(invoice.getPayeeId()).getCity());
        if(nonNull(invoice.getWorkerId())) {
            Worker worker = workerService.getOne(invoice.getWorkerId());
            invoiceResult.setWorkerName(worker.getFirstname() + " "+ worker.getLastname());
        }

        if(nonNull(invoice.getAgency())) {
            Agency agency = agencyService.getOne(invoice.getAgency().getId());
            invoiceResult.setAgencyName(agency.getName());
            invoiceResult.setAgency(toAgencyResultDto.convert(agency));
        }

        invoiceResult.setInvoiceDate(invoice.getInvoiceDate());
        if(invoice.getDueDate()!=null) invoiceResult.setDueDate(invoice.getDueDate());

        invoiceResult.setDiscount(invoice.getDiscount());
        invoiceResult.setTotalAmount(invoice.getTotalAmount());
        invoiceResult.setSubTotalAmount(invoice.getSubTotalAmount());
        invoiceResult.setRedirectUrl(invoice.getRedirectUrl());
        invoiceResult.setClientSecret(invoice.getClientSecret());
        invoiceResult.setInvoiceItemResult(toInvoiceItemResult.convert(invoice.getInvoiceItems()));
//        invoiceResult.setPayments(invoice.getPayments());
        invoiceResult.setPayments(paymentRepository.findAllByInvoiceId(invoice.getId()));

        return invoiceResult;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
