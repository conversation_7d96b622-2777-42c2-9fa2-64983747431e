package com.cap10mycap10.worklinkservice.mapper.billing;

import com.cap10mycap10.worklinkservice.dto.billing.AgencyBillDto;
import com.cap10mycap10.worklinkservice.model.AgencyBill;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.util.Converter;

@Component
public class AgencyBillToAgencyBillDto implements Converter<AgencyBill, AgencyBillDto> {
    @Override
    public AgencyBillDto convert(AgencyBill agencyBill) {
        AgencyBillDto agencyBillDto = new AgencyBillDto();
        if (agencyBill.getAgency() != (null))
            agencyBillDto.setAgency(agencyBill.getAgency().getName());
        if (agencyBill.getClient() != (null))
            agencyBillDto.setClient(agencyBill.getClient().getName());
        if (agencyBill.getWorker() != (null))
            agencyBillDto.setWorker(agencyBill.getWorker().getFirstname()+ " "+ agencyBill.getWorker().getLastname());
        if (agencyBill.getShift() != (null))
            agencyBillDto.setShiftId(agencyBill.getShift().getId());
        agencyBillDto.setChargeRate(agencyBill.getChargeRate());
        agencyBillDto.setId(agencyBill.getId());
        agencyBillDto.setIssueDate(agencyBill.getIssueDate());
        agencyBillDto.setDueDate(agencyBill.getDueDate());
        agencyBillDto.setStatus(agencyBill.getStatus());
        agencyBillDto.setTotalUnits(agencyBill.getTotalUnits());
        agencyBillDto.setTotalCharge(agencyBill.getTotalCharge());
        agencyBillDto.setNotes(agencyBill.getNotes());
        agencyBillDto.setSubTotal(agencyBill.getSubTotal());
        agencyBillDto.setDiscountCharge(agencyBill.getDiscountCharge());
        agencyBillDto.setVatRate(agencyBill.getVatRate());
        agencyBillDto.setTotalDue(agencyBill.getTotalDue());
        if (agencyBill.getAgency() != null) {
            agencyBillDto.setAgentId(agencyBill.getAgency().getId());
            agencyBillDto.setBillEmailAddress(agencyBill.getAgency().getEmail());
        }
        if (agencyBill.getPaid() != null)
            agencyBillDto.setPaid(agencyBill.getPaid());
        if (agencyBill.getPaymentRef() != null)
            agencyBillDto.setPaymentRef(agencyBill.getPaymentRef());
        if (agencyBill.getPaidDate() != null)
            agencyBillDto.setPaidDate(agencyBill.getPaidDate());
        return agencyBillDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}
